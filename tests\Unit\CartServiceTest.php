<?php

namespace Tests\Unit;

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\Product;
use App\Models\User;
use App\Services\CartService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

class CartServiceTest extends TestCase
{
    use RefreshDatabase;

    protected CartService $cartService;
    protected User $user;
    protected Component $component;
    protected Product $product;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->cartService = new CartService();
        
        // Create test user
        $this->user = User::factory()->create();
        
        // Create test component category
        $category = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu',
        ]);
        
        // Create test component
        $this->component = Component::factory()->create([
            'name' => 'Test CPU',
            'category_id' => $category->id,
            'price' => 299.99,
            'stock' => 10,
            'is_active' => true,
        ]);

        // Create test product
        $this->product = Product::factory()->create([
            'name' => 'Test Gaming Laptop',
            'price' => 1299.99,
            'stock_quantity' => 5,
            'status' => 'active',
            'in_stock' => true,
            'manage_stock' => true,
        ]);
    }

    public function test_get_cart_creates_session_cart_for_guest()
    {
        // Start a session for testing
        $this->startSession();
        
        $cart = $this->cartService->getCart();
        
        $this->assertInstanceOf(Cart::class, $cart);
        $this->assertNull($cart->user_id);
        $this->assertNotNull($cart->session_id);
    }

    public function test_get_cart_creates_user_cart_for_authenticated_user()
    {
        $this->actingAs($this->user);
        
        $cart = $this->cartService->getCart();
        
        $this->assertInstanceOf(Cart::class, $cart);
        $this->assertEquals($this->user->id, $cart->user_id);
    }

    public function test_add_to_cart_creates_new_cart_item()
    {
        $this->startSession();
        
        $cartItem = $this->cartService->addToCart($this->component, 2);
        
        $this->assertInstanceOf(CartItem::class, $cartItem);
        $this->assertEquals($this->component->id, $cartItem->component_id);
        $this->assertEquals(2, $cartItem->quantity);
        $this->assertEquals($this->component->price, $cartItem->price);
    }

    public function test_add_to_cart_updates_existing_cart_item()
    {
        $this->startSession();
        
        // Add item first time
        $this->cartService->addToCart($this->component, 2);
        
        // Add same item again
        $cartItem = $this->cartService->addToCart($this->component, 3);
        
        $this->assertEquals(5, $cartItem->quantity);
    }

    public function test_add_to_cart_throws_exception_for_zero_quantity()
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Quantity must be greater than 0');
        
        $this->cartService->addToCart($this->component, 0);
    }

    public function test_add_to_cart_throws_exception_for_inactive_component()
    {
        $this->component->update(['is_active' => false]);
        
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Component is not available');
        
        $this->cartService->addToCart($this->component, 1);
    }

    public function test_add_to_cart_throws_exception_for_insufficient_stock()
    {
        $this->component->update(['stock' => 2]);
        
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Insufficient stock available');
        
        $this->cartService->addToCart($this->component, 5);
    }

    public function test_add_to_cart_throws_exception_when_total_quantity_exceeds_stock()
    {
        $this->startSession();
        
        $this->component->update(['stock' => 5]);
        
        // Add 3 items first
        $this->cartService->addToCart($this->component, 3);
        
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Insufficient stock for requested quantity');
        
        // Try to add 3 more (total would be 6, but stock is 5)
        $this->cartService->addToCart($this->component, 3);
    }

    public function test_update_item_quantity_updates_existing_item()
    {
        $this->startSession();
        
        $cartItem = $this->cartService->addToCart($this->component, 2);
        
        $updatedItem = $this->cartService->updateItemQuantity($cartItem->id, 5);
        
        $this->assertEquals(5, $updatedItem->quantity);
    }

    public function test_update_item_quantity_removes_item_when_quantity_is_zero()
    {
        $this->startSession();
        
        $cartItem = $this->cartService->addToCart($this->component, 2);
        
        $result = $this->cartService->updateItemQuantity($cartItem->id, 0);
        
        $this->assertTrue($result);
        $this->assertDatabaseMissing('cart_items', ['id' => $cartItem->id]);
    }

    public function test_update_item_quantity_throws_exception_for_negative_quantity()
    {
        $this->startSession();
        
        $cartItem = $this->cartService->addToCart($this->component, 2);
        
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Quantity cannot be negative');
        
        $this->cartService->updateItemQuantity($cartItem->id, -1);
    }

    public function test_update_item_quantity_throws_exception_for_insufficient_stock()
    {
        $this->startSession();
        
        $this->component->update(['stock' => 3]);
        $cartItem = $this->cartService->addToCart($this->component, 2);
        
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Insufficient stock for requested quantity');
        
        $this->cartService->updateItemQuantity($cartItem->id, 5);
    }

    public function test_remove_from_cart_removes_cart_item()
    {
        $this->startSession();
        
        $cartItem = $this->cartService->addToCart($this->component, 2);
        
        $result = $this->cartService->removeFromCart($cartItem);
        
        $this->assertTrue($result);
        $this->assertDatabaseMissing('cart_items', ['id' => $cartItem->id]);
    }

    public function test_remove_from_cart_with_item_id()
    {
        $this->startSession();
        
        $cartItem = $this->cartService->addToCart($this->component, 2);
        
        $result = $this->cartService->removeFromCart($cartItem->id);
        
        $this->assertTrue($result);
        $this->assertDatabaseMissing('cart_items', ['id' => $cartItem->id]);
    }

    public function test_merge_guest_cart_merges_session_cart_with_user_cart()
    {
        // Create session cart with items
        $sessionCart = Cart::create(['session_id' => 'test-session', 'user_id' => null]);
        $sessionCart->items()->create([
            'component_id' => $this->component->id,
            'quantity' => 2,
            'price' => $this->component->price,
        ]);

        // Create another component for testing
        $component2 = Component::factory()->create([
            'name' => 'Test GPU',
            'category_id' => $this->component->category_id,
            'price' => 599.99,
            'stock' => 5,
            'is_active' => true,
        ]);
        
        $sessionCart->items()->create([
            'component_id' => $component2->id,
            'quantity' => 1,
            'price' => $component2->price,
        ]);

        // Create user cart with one existing item
        $userCart = Cart::create(['user_id' => $this->user->id]);
        $userCart->items()->create([
            'component_id' => $this->component->id,
            'quantity' => 1,
            'price' => $this->component->price,
        ]);

        $this->cartService->mergeGuestCart($this->user, 'test-session');

        // Check that session cart is deleted
        $this->assertDatabaseMissing('carts', ['id' => $sessionCart->id]);

        // Check that user cart has merged items
        $userCart->refresh();
        $items = $userCart->items;
        
        $this->assertEquals(2, $items->count());
        
        // First component should have quantity 3 (1 + 2)
        $firstItem = $items->where('component_id', $this->component->id)->first();
        $this->assertEquals(3, $firstItem->quantity);
        
        // Second component should have quantity 1
        $secondItem = $items->where('component_id', $component2->id)->first();
        $this->assertEquals(1, $secondItem->quantity);
    }

    public function test_merge_guest_cart_does_nothing_when_no_session_cart()
    {
        $this->cartService->mergeGuestCart($this->user, 'non-existent-session');
        
        // Should not create any cart or items
        $this->assertEquals(0, Cart::where('user_id', $this->user->id)->count());
    }

    public function test_get_item_count_returns_correct_count()
    {
        $this->startSession();
        
        $this->cartService->addToCart($this->component, 3);
        
        $component2 = Component::factory()->create([
            'category_id' => $this->component->category_id,
            'stock' => 5,
            'is_active' => true,
        ]);
        $this->cartService->addToCart($component2, 2);
        
        $count = $this->cartService->getItemCount();
        
        $this->assertEquals(5, $count);
    }

    public function test_get_total_returns_correct_total()
    {
        $this->startSession();
        
        $this->cartService->addToCart($this->component, 2); // 2 * 299.99 = 599.98
        
        $component2 = Component::factory()->create([
            'category_id' => $this->component->category_id,
            'price' => 199.99,
            'stock' => 5,
            'is_active' => true,
        ]);
        $this->cartService->addToCart($component2, 1); // 1 * 199.99 = 199.99
        
        $total = $this->cartService->getTotal();
        
        $this->assertEquals(799.97, $total);
    }

    public function test_clear_cart_removes_all_items()
    {
        $this->startSession();
        
        $this->cartService->addToCart($this->component, 2);
        
        $component2 = Component::factory()->create([
            'category_id' => $this->component->category_id,
            'stock' => 5,
            'is_active' => true,
        ]);
        $this->cartService->addToCart($component2, 1);
        
        $result = $this->cartService->clearCart();
        
        $this->assertTrue($result);
        $this->assertEquals(0, $this->cartService->getItemCount());
        $this->assertEquals(0, $this->cartService->getTotal());
    }

    public function test_legacy_add_item_method_works()
    {
        $this->startSession();
        
        $cartItem = $this->cartService->addItem($this->component->id, 2);
        
        $this->assertInstanceOf(CartItem::class, $cartItem);
        $this->assertEquals($this->component->id, $cartItem->component_id);
        $this->assertEquals(2, $cartItem->quantity);
    }

    public function test_legacy_add_item_throws_exception_for_invalid_component()
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Component not found');
        
        $this->cartService->addItem(999, 1);
    }

    public function test_legacy_remove_item_method_works()
    {
        $this->startSession();

        $cartItem = $this->cartService->addToCart($this->component, 2);

        $result = $this->cartService->removeItem($cartItem->id);

        $this->assertTrue($result);
        $this->assertDatabaseMissing('cart_items', ['id' => $cartItem->id]);
    }

    // Product-specific tests

    public function test_add_product_to_cart_creates_new_cart_item()
    {
        $this->startSession();

        $cartItem = $this->cartService->addToCart($this->product, 1);

        $this->assertInstanceOf(CartItem::class, $cartItem);
        $this->assertEquals($this->product->id, $cartItem->product_id);
        $this->assertNull($cartItem->component_id);
        $this->assertEquals(CartItem::TYPE_PRODUCT, $cartItem->item_type);
        $this->assertEquals(1, $cartItem->quantity);
        $this->assertEquals($this->product->effective_price, $cartItem->price);
    }

    public function test_add_product_to_cart_updates_existing_cart_item()
    {
        $this->startSession();

        // Add product first time
        $this->cartService->addToCart($this->product, 1);

        // Add same product again
        $cartItem = $this->cartService->addToCart($this->product, 2);

        $this->assertEquals(3, $cartItem->quantity);
        $this->assertEquals(CartItem::TYPE_PRODUCT, $cartItem->item_type);
    }

    public function test_add_product_throws_exception_for_unavailable_product()
    {
        $this->product->update(['status' => 'inactive']);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Product is not available or insufficient stock');

        $this->cartService->addToCart($this->product, 1);
    }

    public function test_add_product_throws_exception_for_insufficient_stock()
    {
        $this->product->update(['stock_quantity' => 2]);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Product is not available or insufficient stock');

        $this->cartService->addToCart($this->product, 5);
    }

    public function test_can_add_both_component_and_product_to_same_cart()
    {
        $this->startSession();

        // Add component
        $componentItem = $this->cartService->addToCart($this->component, 2);

        // Add product
        $productItem = $this->cartService->addToCart($this->product, 1);

        $this->assertEquals(CartItem::TYPE_COMPONENT, $componentItem->item_type);
        $this->assertEquals(CartItem::TYPE_PRODUCT, $productItem->item_type);

        // Verify both items exist in cart
        $cart = $this->cartService->getCart();
        $this->assertEquals(2, $cart->items()->count());
        $this->assertEquals(3, $this->cartService->getItemCount()); // 2 components + 1 product
    }

    public function test_add_invalid_item_type_throws_exception()
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Item must be either a Component or Product');

        // Create a generic object that's neither Component nor Product
        $invalidItem = new \stdClass();
        $this->cartService->addToCart($invalidItem, 1);
    }
}