<?php

namespace App\Traits;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

trait ImageManagement
{
    /**
     * Get the primary image of the item
     */
    public function getImage(): ?string
    {
        // Check if the model has an images array property
        if (isset($this->images) && is_array($this->images) && !empty($this->images)) {
            return $this->images[0];
        }
        
        // Fall back to single image property
        return $this->image ?? null;
    }

    /**
     * Get all images of the item
     */
    public function getImages(): array
    {
        // Check if the model has an images array property
        if (isset($this->images) && is_array($this->images)) {
            return $this->images;
        }
        
        // Fall back to single image property
        if ($this->image) {
            return [$this->image];
        }
        
        return [];
    }

    /**
     * Get the primary image with fallback to placeholder
     */
    public function getPrimaryImage(): string
    {
        return $this->getImage() ?? '/images/placeholder.jpg';
    }

    /**
     * Check if the item has any images
     */
    public function hasImages(): bool
    {
        return !empty($this->getImages());
    }

    /**
     * Get the first available image or placeholder
     */
    public function getImageWithFallback(): string
    {
        $image = $this->getImage();
        
        if ($image) {
            // Check if it's a full URL or relative path
            if (filter_var($image, FILTER_VALIDATE_URL)) {
                return $image;
            }
            
            // Ensure the image path starts with /
            return '/' . ltrim($image, '/');
        }
        
        return '/images/placeholder.jpg';
    }

    /**
     * Get all images with proper URL formatting
     */
    public function getFormattedImages(): array
    {
        $images = $this->getImages();
        
        return array_map(function ($image) {
            return $this->formatImageUrl($image);
        }, $images);
    }

    /**
     * Format a single image URL with proper validation and fallbacks
     */
    public function formatImageUrl(?string $image): string
    {
        if (!$image) {
            return $this->getPlaceholderImage();
        }

        // Handle full URLs
        if (filter_var($image, FILTER_VALIDATE_URL)) {
            return $this->validateImageUrl($image) ? $image : $this->getPlaceholderImage();
        }

        // Handle storage disk paths
        if ($this->isStoragePath($image)) {
            return $this->getStorageUrl($image);
        }

        // Handle relative paths
        $formattedPath = '/' . ltrim($image, '/');
        
        // Validate if file exists in public directory
        if ($this->imageExists($formattedPath)) {
            return $formattedPath;
        }

        Log::warning('Image not found', [
            'model' => get_class($this),
            'id' => $this->id ?? 'unknown',
            'image_path' => $image
        ]);

        return $this->getPlaceholderImage();
    }

    /**
     * Check if image path is a storage disk path
     */
    protected function isStoragePath(string $path): bool
    {
        return strpos($path, 'storage/') === 0 || 
               strpos($path, 'public/') === 0 ||
               !str_contains($path, '/') && !str_contains($path, '.');
    }

    /**
     * Get storage URL for a given path
     */
    protected function getStorageUrl(string $path): string
    {
        try {
            // Remove 'storage/' prefix if present
            $cleanPath = ltrim($path, 'storage/');
            
            if (Storage::disk('public')->exists($cleanPath)) {
                return Storage::disk('public')->url($cleanPath);
            }
            
            // Try default disk
            if (Storage::exists($path)) {
                return Storage::url($path);
            }
        } catch (\Exception $e) {
            Log::error('Error accessing storage image', [
                'path' => $path,
                'error' => $e->getMessage()
            ]);
        }

        return $this->getPlaceholderImage();
    }

    /**
     * Validate if an image URL is accessible
     */
    protected function validateImageUrl(string $url): bool
    {
        // Basic URL validation
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }

        // Check if it's a valid image extension
        $extension = strtolower(pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION));
        $validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
        
        return in_array($extension, $validExtensions);
    }

    /**
     * Check if image exists in public directory
     */
    protected function imageExists(string $path): bool
    {
        $publicPath = public_path(ltrim($path, '/'));
        return file_exists($publicPath) && is_file($publicPath);
    }

    /**
     * Get placeholder image based on item type
     */
    public function getPlaceholderImage(): string
    {
        // Try to get type-specific placeholder
        $itemType = $this->getImagePlaceholderType();
        
        $placeholders = [
            'component' => '/images/placeholders/component-placeholder.jpg',
            'product' => '/images/placeholders/product-placeholder.jpg',
            'default' => '/images/placeholder.jpg'
        ];

        $placeholder = $placeholders[$itemType] ?? $placeholders['default'];
        
        // Check if specific placeholder exists, otherwise use default
        if ($placeholder !== $placeholders['default'] && !$this->imageExists($placeholder)) {
            $placeholder = $placeholders['default'];
        }

        return $placeholder;
    }

    /**
     * Get the placeholder type for this model
     */
    protected function getImagePlaceholderType(): string
    {
        $className = strtolower(class_basename($this));
        
        if (str_contains($className, 'component')) {
            return 'component';
        }
        
        if (str_contains($className, 'product')) {
            return 'product';
        }
        
        return 'default';
    }

    /**
     * Get image with size variant (thumbnail, medium, large)
     */
    public function getImageVariant(string $size = 'medium'): string
    {
        $image = $this->getImage();
        
        if (!$image) {
            return $this->getPlaceholderImage();
        }

        // If it's already a full URL, return as is
        if (filter_var($image, FILTER_VALIDATE_URL)) {
            return $image;
        }

        // Try to get size variant
        $pathInfo = pathinfo($image);
        $variantPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_' . $size . '.' . $pathInfo['extension'];
        
        if ($this->imageExists($variantPath)) {
            return '/' . ltrim($variantPath, '/');
        }

        // Fall back to original image
        return $this->formatImageUrl($image);
    }

    /**
     * Get thumbnail image
     */
    public function getThumbnail(): string
    {
        return $this->getImageVariant('thumbnail');
    }

    /**
     * Get medium sized image
     */
    public function getMediumImage(): string
    {
        return $this->getImageVariant('medium');
    }

    /**
     * Get large sized image
     */
    public function getLargeImage(): string
    {
        return $this->getImageVariant('large');
    }

    /**
     * Get all available image variants
     */
    public function getImageVariants(): array
    {
        return [
            'thumbnail' => $this->getThumbnail(),
            'medium' => $this->getMediumImage(),
            'large' => $this->getLargeImage(),
            'original' => $this->formatImageUrl($this->getImage())
        ];
    }

    /**
     * Validate image format and size
     */
    public function validateImage(string $imagePath): array
    {
        $errors = [];
        
        if (!$this->imageExists($imagePath)) {
            $errors[] = 'Image file does not exist';
            return $errors;
        }

        try {
            $imageInfo = getimagesize(public_path(ltrim($imagePath, '/')));
            
            if (!$imageInfo) {
                $errors[] = 'Invalid image format';
                return $errors;
            }

            // Check image dimensions
            [$width, $height] = $imageInfo;
            
            if ($width < 100 || $height < 100) {
                $errors[] = 'Image dimensions too small (minimum 100x100)';
            }
            
            if ($width > 5000 || $height > 5000) {
                $errors[] = 'Image dimensions too large (maximum 5000x5000)';
            }

            // Check file size
            $fileSize = filesize(public_path(ltrim($imagePath, '/')));
            if ($fileSize > 5 * 1024 * 1024) { // 5MB
                $errors[] = 'Image file size too large (maximum 5MB)';
            }

        } catch (\Exception $e) {
            $errors[] = 'Error validating image: ' . $e->getMessage();
        }

        return $errors;
    }
}