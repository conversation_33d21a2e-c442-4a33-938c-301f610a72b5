<?php

namespace App\Traits;

use App\Services\CartService;
use Illuminate\Support\Facades\Log;

trait Cartable
{
    /**
     * Add this item to the cart with validation
     */
    public function addToCart(int $quantity = 1): bool
    {
        // Validate quantity
        if ($quantity <= 0) {
            Log::warning('Invalid quantity for cart addition', [
                'item_type' => get_class($this),
                'item_id' => $this->id,
                'quantity' => $quantity
            ]);
            return false;
        }

        // Check if item can be added to cart
        if (!$this->canAddToCart($quantity)) {
            Log::info('Item cannot be added to cart', [
                'item_type' => get_class($this),
                'item_id' => $this->id,
                'quantity' => $quantity,
                'available_stock' => $this->getStock(),
                'is_available' => $this->isAvailable($quantity)
            ]);
            return false;
        }

        // Check quantity limits
        $maxQuantity = $this->getMaxCartQuantity();
        if ($quantity > $maxQuantity) {
            Log::info('Quantity exceeds maximum allowed', [
                'item_type' => get_class($this),
                'item_id' => $this->id,
                'requested_quantity' => $quantity,
                'max_quantity' => $maxQuantity
            ]);
            return false;
        }

        $cartService = app(CartService::class);
        return $cartService->addItem($this, $quantity);
    }

    /**
     * Get the display name for cart
     */
    public function getCartDisplayName(): string
    {
        $brand = $this->getBrand();
        $model = $this->getModel();
        
        if ($brand && $model) {
            return "{$brand} {$model} - {$this->getName()}";
        }
        
        return $this->getName();
    }

    /**
     * Get the image for cart display
     */
    public function getCartImage(): ?string
    {
        return $this->getImage();
    }

    /**
     * Get the price for cart calculations
     */
    public function getCartPrice(): float
    {
        return $this->getEffectivePrice();
    }

    /**
     * Check if the item can be added to cart
     */
    public function canAddToCart(int $quantity = 1): bool
    {
        return $this->isValidCartQuantity($quantity) && $this->isAvailable($quantity);
    }

    /**
     * Get the maximum quantity that can be added to cart
     */
    public function getMaxCartQuantity(): int
    {
        $stock = $this->getStock();
        
        // Check if the model has a max_order_quantity property
        if (property_exists($this, 'max_order_quantity') && $this->max_order_quantity) {
            return min($stock, $this->max_order_quantity);
        }
        
        // Check if the model has a min_order_quantity property for validation
        if (property_exists($this, 'min_order_quantity') && $this->min_order_quantity > $stock) {
            return 0; // Cannot fulfill minimum order requirement
        }
        
        return $stock;
    }

    /**
     * Get the minimum quantity that must be ordered
     */
    public function getMinCartQuantity(): int
    {
        if (property_exists($this, 'min_order_quantity') && $this->min_order_quantity) {
            return $this->min_order_quantity;
        }
        
        return 1;
    }

    /**
     * Validate if a specific quantity can be added to cart
     */
    public function validateCartQuantity(int $quantity): array
    {
        $errors = [];
        
        if ($quantity <= 0) {
            $errors[] = 'Quantity must be greater than zero';
        }
        
        $minQuantity = $this->getMinCartQuantity();
        if ($quantity < $minQuantity) {
            $errors[] = "Minimum order quantity is {$minQuantity}";
        }
        
        $maxQuantity = $this->getMaxCartQuantity();
        if ($quantity > $maxQuantity) {
            $errors[] = "Maximum order quantity is {$maxQuantity}";
        }
        
        if (!$this->isAvailable($quantity)) {
            $errors[] = 'Item is not available in the requested quantity';
        }
        
        return $errors;
    }

    /**
     * Check if quantity is within valid range
     */
    public function isValidCartQuantity(int $quantity): bool
    {
        return empty($this->validateCartQuantity($quantity));
    }

    /**
     * Get cart item metadata for additional information
     */
    public function getCartMetadata(): array
    {
        return [
            'brand' => $this->getBrand(),
            'model' => $this->getModel(),
            'category' => $this->getCategory(),
            'item_type' => get_class($this),
            'min_quantity' => $this->getMinCartQuantity(),
            'max_quantity' => $this->getMaxCartQuantity(),
        ];
    }
}