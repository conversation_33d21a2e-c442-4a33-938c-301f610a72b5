@import './nexus-theme.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

[x-cloak] {
    display: none !important;
}

/* Prevent flash of unstyled content for Alpine.js components */
[x-data] {
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
}

[x-data].alpine-ready {
    opacity: 1;
}

/* Specific fix for navigation menu */
nav[x-data] {
    opacity: 1; /* Navigation should always be visible */
}

/* Fix for mobile menu transitions */
[x-show] {
    transition: all 0.3s ease-in-out;
}

/* Fix for fixed navigation overlapping content */
body {
    padding-top: 4rem; /* 64px - height of navigation bar */
}

/* Ensure main content doesn't overlap with fixed nav */
main, .main-content, #app {
    margin-top: 0;
    padding-top: 0;
}

/* Additional spacing for pages that need it */
.page-content {
    padding-top: 1rem;
}

/* Fix for checkout and other full-height pages */
.checkout-page, .full-height-page {
    min-height: calc(100vh - 4rem);
    padding-top: 0.5rem;
}

/* Specific fix for Livewire components */
[wire\:id] {
    position: relative;
    z-index: 1;
}

/* Ensure dropdowns and modals appear above fixed nav */
.dropdown-menu, .modal, [x-show] {
    z-index: 60;
}

/* Fix for any content that might still overlap */
.container, .max-w-7xl, .max-w-6xl, .max-w-5xl, .max-w-4xl {
    position: relative;
    z-index: 1;
}

/* Cart Animation Enhancements */
@keyframes cartBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes cartPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes slideInFromRight {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    0% {
        transform: translateY(20px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

.cart-bounce {
    animation: cartBounce 0.6s ease-in-out;
}

.cart-pulse {
    animation: cartPulse 0.3s ease-in-out;
}

.slide-in-right {
    animation: slideInFromRight 0.3s ease-out;
}

.fade-in-up {
    animation: fadeInUp 0.3s ease-out;
}

/* Enhanced hover effects for cart items */
.cart-item-hover {
    transition: all 0.2s ease-in-out;
}

.cart-item-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Scrollbar styling for webkit browsers */
.scrollbar-thin::-webkit-scrollbar {
    width: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
    border-radius: 2px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.7);
}
