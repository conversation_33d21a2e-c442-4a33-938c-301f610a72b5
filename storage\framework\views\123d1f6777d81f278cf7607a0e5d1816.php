<?php $__env->startSection('content'); ?>
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
            <div class="p-6 bg-white border-b border-gray-200">
                <div class="flex justify-between items-center mb-6">
                    <h1 class="text-2xl font-semibold text-gray-800">Order Details</h1>
                    <a href="<?php echo e(route('admin.orders.index')); ?>" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition">
                        Back to Orders
                    </a>
                </div>
                
                <div class="bg-gray-50 p-6 rounded-lg mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h2 class="text-lg font-medium text-gray-900 mb-2">Order Information</h2>
                            <p><span class="font-medium">Order Number:</span> <?php echo e($order->order_number); ?></p>
                            <p><span class="font-medium">Date:</span> <?php echo e($order->created_at->format('F j, Y, g:i a')); ?></p>
                            <p><span class="font-medium">Status:</span> 
                                <span class="px-2 py-1 rounded text-xs font-medium
                                    <?php if($order->status == 'pending'): ?> bg-yellow-100 text-yellow-800
                                    <?php elseif($order->status == 'processing'): ?> bg-blue-100 text-blue-800
                                    <?php elseif($order->status == 'completed'): ?> bg-green-100 text-green-800
                                    <?php elseif($order->status == 'canceled'): ?> bg-red-100 text-red-800
                                    <?php elseif($order->status == 'refunded'): ?> bg-purple-100 text-purple-800
                                    <?php endif; ?>">
                                    <?php echo e(ucfirst($order->status)); ?>

                                </span>
                            </p>
                            <p><span class="font-medium">Total:</span> $<?php echo e(number_format($order->total, 2)); ?></p>
                        </div>
                        <div>
                            <h2 class="text-lg font-medium text-gray-900 mb-2">Customer Information</h2>
                            <p><span class="font-medium">Name:</span> <?php echo e($order->user->name); ?></p>
                            <p><span class="font-medium">Email:</span> <?php echo e($order->user->email); ?></p>
                            <p><span class="font-medium">Phone:</span> <?php echo e($order->phone ?? 'N/A'); ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-50 p-6 rounded-lg mb-6">
                    <h2 class="text-lg font-medium text-gray-900 mb-4">Shipping Information</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <p><span class="font-medium">Address:</span> <?php echo e($order->address); ?></p>
                            <p><span class="font-medium">City:</span> <?php echo e($order->city); ?></p>
                            <p><span class="font-medium">State:</span> <?php echo e($order->state); ?></p>
                            <p><span class="font-medium">Zip Code:</span> <?php echo e($order->zip_code); ?></p>
                            <p><span class="font-medium">Country:</span> <?php echo e($order->country); ?></p>
                        </div>
                        <div>
                            <p><span class="font-medium">Tracking Number:</span> <?php echo e($order->tracking_number ?? 'Not available'); ?></p>
                            <p><span class="font-medium">Carrier:</span> <?php echo e($order->tracking_carrier ?? 'Not available'); ?></p>
                            <?php if($order->tracking_url): ?>
                                <p><span class="font-medium">Tracking Link:</span> 
                                    <a href="<?php echo e($order->tracking_url); ?>" target="_blank" class="text-blue-600 hover:underline">Track Package</a>
                                </p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-50 p-6 rounded-lg mb-6">
                    <h2 class="text-lg font-medium text-gray-900 mb-4">Order Items</h2>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subtotal</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php $__currentLoopData = $order->orderItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <?php if($item->component && $item->component->image): ?>
                                                <div class="flex-shrink-0 h-10 w-10 mr-4">
                                                    <img class="h-10 w-10 rounded-full object-cover" src="<?php echo e(asset('storage/' . $item->component->image)); ?>" alt="<?php echo e($item->component->name); ?>">
                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?php echo e($item->component ? $item->component->name : $item->name); ?>

                                                </div>
                                                <?php if($item->component): ?>
                                                <div class="text-sm text-gray-500">
                                                    <?php echo e($item->component->category->name); ?>

                                                </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        $<?php echo e(number_format($item->price, 2)); ?>

                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo e($item->quantity); ?>

                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        $<?php echo e(number_format($item->price * $item->quantity, 2)); ?>

                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                            <tfoot class="bg-gray-50">
                                <tr>
                                    <td colspan="3" class="px-6 py-4 text-right text-sm font-medium text-gray-900">Subtotal:</td>
                                    <td class="px-6 py-4 text-sm text-gray-900">$<?php echo e(number_format($order->subtotal, 2)); ?></td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="px-6 py-4 text-right text-sm font-medium text-gray-900">Tax:</td>
                                    <td class="px-6 py-4 text-sm text-gray-900">$<?php echo e(number_format($order->tax, 2)); ?></td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="px-6 py-4 text-right text-sm font-medium text-gray-900">Shipping:</td>
                                    <td class="px-6 py-4 text-sm text-gray-900">$<?php echo e(number_format($order->shipping, 2)); ?></td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="px-6 py-4 text-right text-sm font-bold text-gray-900">Total:</td>
                                    <td class="px-6 py-4 text-sm font-bold text-gray-900">$<?php echo e(number_format($order->total, 2)); ?></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                
                <div class="bg-gray-50 p-6 rounded-lg mb-6">
                    <h2 class="text-lg font-medium text-gray-900 mb-4">Update Order</h2>
                    <form action="<?php echo e(route('admin.orders.update-status', $order)); ?>" method="POST" class="space-y-4">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700">Order Status</label>
                            <select id="status" name="status" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                <option value="pending" <?php echo e($order->status == 'pending' ? 'selected' : ''); ?>>Pending</option>
                                <option value="processing" <?php echo e($order->status == 'processing' ? 'selected' : ''); ?>>Processing</option>
                                <option value="completed" <?php echo e($order->status == 'completed' ? 'selected' : ''); ?>>Completed</option>
                                <option value="canceled" <?php echo e($order->status == 'canceled' ? 'selected' : ''); ?>>Canceled</option>
                                <option value="refunded" <?php echo e($order->status == 'refunded' ? 'selected' : ''); ?>>Refunded</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="tracking_number" class="block text-sm font-medium text-gray-700">Tracking Number</label>
                            <input type="text" name="tracking_number" id="tracking_number" value="<?php echo e($order->tracking_number); ?>" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                        </div>
                        
                        <div>
                            <label for="tracking_carrier" class="block text-sm font-medium text-gray-700">Carrier</label>
                            <input type="text" name="tracking_carrier" id="tracking_carrier" value="<?php echo e($order->tracking_carrier); ?>" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                        </div>
                        
                        <div>
                            <label for="tracking_url" class="block text-sm font-medium text-gray-700">Tracking URL</label>
                            <input type="url" name="tracking_url" id="tracking_url" value="<?php echo e($order->tracking_url); ?>" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                        </div>
                        
                        <div>
                            <label for="notes" class="block text-sm font-medium text-gray-700">Order Notes</label>
                            <textarea id="notes" name="notes" rows="3" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"><?php echo e($order->notes); ?></textarea>
                        </div>
                        
                        <div>
                            <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Update Order
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\lara\www\pc-builder\resources\views/admin/orders/show.blade.php ENDPATH**/ ?>