<?php

namespace Tests\Feature\Controllers;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RequestValidationControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $adminUser;
    protected ComponentCategory $cpuCategory;
    protected ComponentCategory $motherboardCategory;
    protected ProductCategory $productCategory;
    protected Component $component;
    protected Product $product;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->adminUser = User::factory()->create([
            'role' => 'admin'
        ]);

        // Create component categories
        $this->cpuCategory = ComponentCategory::factory()->create(['name' => 'CPU', 'slug' => 'cpu']);
        $this->motherboardCategory = ComponentCategory::factory()->create(['name' => 'Motherboard', 'slug' => 'motherboard']);
        
        // Create product category
        $this->productCategory = ProductCategory::factory()->create();
        
        // Create a component
        $this->component = Component::factory()->create([
            'category_id' => $this->cpuCategory->id,
            'socket_type' => 'LGA1700',
            'power_consumption' => 65
        ]);
        
        // Create a product
        $this->product = Product::factory()->create([
            'category_id' => $this->productCategory->id
        ]);
    }

    /** @test */
    public function admin_component_controller_validates_requests()
    {
        $this->actingAs($this->adminUser);

        // Test store method with missing required fields
        $response = $this->post('/admin/components', []);
        $response->assertSessionHasErrors([
            'name',
            'slug',
            'category_id',
            'brand',
            'model',
            'price',
            'stock'
        ]);

        // Test store method with category-specific validation
        $response = $this->post('/admin/components', [
            'name' => 'Test CPU',
            'slug' => 'test-cpu',
            'category_id' => $this->cpuCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 299.99,
            'stock' => 10,
            // Missing socket_type and power_consumption
        ]);
        $response->assertSessionHasErrors([
            'socket_type',
            'power_consumption'
        ]);

        // Test update method with invalid data
        $response = $this->put("/admin/components/{$this->component->id}", [
            'name' => '', // empty
            'price' => -10, // negative
            'stock' => -5, // negative
        ]);
        $response->assertSessionHasErrors([
            'name',
            'price',
            'stock'
        ]);

        // Test update method with discontinued component validation
        $response = $this->put("/admin/components/{$this->component->id}", [
            'name' => 'Discontinued Component',
            'slug' => 'discontinued-component',
            'category_id' => $this->cpuCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 299.99,
            'stock' => 10,
            'socket_type' => 'LGA1700',
            'power_consumption' => 65,
            'is_active' => true,
            'discontinued_at' => now()->subDay()->format('Y-m-d')
        ]);
        $response->assertSessionHasErrors([
            'is_active'
        ]);
    }

    /** @test */
    public function admin_product_controller_validates_requests()
    {
        $this->actingAs($this->adminUser);

        // Test store method with missing required fields
        $response = $this->post('/admin/products', []);
        $response->assertSessionHasErrors([
            'name',
            'slug',
            'sku',
            'category_id',
            'brand',
            'price',
            'status',
            'type'
        ]);

        // Test store method with specific validation rules
        $response = $this->post('/admin/products', [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->productCategory->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'sale_price' => 109.99, // Higher than regular price
            'status' => 'published',
            'type' => 'simple',
            'manage_stock' => true,
            // Missing stock_quantity which is required when manage_stock is true
        ]);
        $response->assertSessionHasErrors([
            'sale_price',
            'stock_quantity'
        ]);

        // Test update method with invalid data
        $response = $this->put("/admin/products/{$this->product->id}", [
            'name' => '', // empty
            'price' => -10, // negative
            'sale_price' => 200, // higher than original price
            'status' => 'invalid-status'
        ]);
        $response->assertSessionHasErrors([
            'name',
            'price',
            'sale_price',
            'status'
        ]);

        // Test bulk pricing validation
        $response = $this->post('/admin/products', [
            'name' => 'Bulk Test Product',
            'slug' => 'bulk-test-product',
            'sku' => 'BULK-TEST-001',
            'category_id' => $this->productCategory->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'bulk_pricing_json' => [
                ['quantity' => 5, 'price' => 90],
                ['quantity' => 5, 'price' => 85], // Duplicate quantity
                ['quantity' => 20, 'price' => 80]
            ]
        ]);
        $response->assertSessionHasErrors([
            'bulk_pricing_json'
        ]);
    }

    /** @test */
    public function cart_controller_validates_requests()
    {
        $this->actingAs($this->adminUser);

        // Test add to cart with missing component_id
        $response = $this->post('/cart/add', [
            'quantity' => 1
        ]);
        $response->assertSessionHasErrors([
            'component_id'
        ]);

        // Test add to cart with invalid component_id
        $response = $this->post('/cart/add', [
            'component_id' => 9999, // non-existent
            'quantity' => 1
        ]);
        $response->assertSessionHasErrors([
            'component_id'
        ]);

        // Test add to cart with invalid quantity
        $response = $this->post('/cart/add', [
            'component_id' => $this->component->id,
            'quantity' => 0 // must be at least 1
        ]);
        $response->assertSessionHasErrors([
            'quantity'
        ]);

        // Test add to cart with quantity exceeding stock
        $response = $this->post('/cart/add', [
            'component_id' => $this->component->id,
            'quantity' => 100 // Assuming this exceeds available stock
        ]);
        $response->assertSessionHasErrors([
            'quantity'
        ]);
    }
}