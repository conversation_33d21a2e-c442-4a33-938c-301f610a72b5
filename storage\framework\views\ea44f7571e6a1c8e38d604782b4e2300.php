<!-- Sidebar -->
<aside
    class="fixed inset-y-0 left-0 z-40 w-64 overflow-y-auto bg-bg-light dark:bg-bg-dark transform lg:transform-none lg:opacity-100 duration-300 lg:sticky lg:top-0 lg:left-0 lg:inset-0 shadow-xl h-screen transition-all"
    :class="{ 'translate-x-0 ease-out': sidebarOpen, '-translate-x-full ease-in': !sidebarOpen }" x-cloak
    x-transition:enter="transition ease-out duration-300" x-transition:enter-start="-translate-x-full"
    x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in duration-200"
    x-transition:leave-start="translate-x-0" x-transition:leave-end="-translate-x-full" aria-label="Main navigation">

    <!-- Header -->
    <div class="flex items-center justify-center py-6 border-b border-border-light dark:border-border-dark">
        <div class="flex items-center">
            <svg class="h-8 w-8 text-accent-light dark:text-accent-dark" fill="none" stroke="currentColor"
                viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4">
                </path>
            </svg>
            <span
                class="ml-3 text-xl font-bold text-text-primary-light dark:text-text-primary-dark"><?php echo e(get_setting('site_name', 'Pincode Directory')); ?></span>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="mt-6 px-2" aria-label="Sidebar Navigation">
        <ul role="list" class="space-y-1">

            <!-- Dashboard -->
            <li>
                <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.dashboard') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                    href="<?php echo e(route('admin.dashboard')); ?>"
                    aria-current="<?php echo e(Request::routeIs('admin.dashboard') ? 'page' : 'false'); ?>">
                    <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z">
                        </path>
                    </svg>
                    <span class="ml-3 text-sm font-medium">Dashboard</span>
                </a>
            </li>

            <!-- Content & Blog Management Section -->
            <li class="pt-4 mt-4 border-t border-border-light dark:border-border-dark" x-data="{ open: false }">
                <button type="button" @click="open = !open"
                    class="w-full flex items-center justify-between px-4 py-2 text-xs font-semibold text-accent-light dark:text-accent-dark uppercase tracking-wider focus:outline-none">
                    <span>Content & Blog</span>
                    <svg :class="{ 'transform rotate-90': open }" class="w-3 h-3 transition-transform duration-200"
                        fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
                <ul x-show="open" x-transition class="space-y-1">

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.pages*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.pages.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.pages*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Pages</span>
                        </a>
                    </li>

                    

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.blog.posts*') || Request::routeIs('admin.blog.index') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.blog.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.blog.posts*') || Request::routeIs('admin.blog.index') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-1m-4-4l-3 3m0 0l-3-3m3 3V3">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Blog Posts</span>
                        </a>
                    </li>

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.blog.categories*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.blog.categories.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.blog.categories*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-1m-4-4l-3 3m0 0l-3-3m3 3V3">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Blog Categories</span>
                        </a>
                    </li>

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.blog.tags*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.blog.tags.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.blog.tags*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Blog Tags</span>
                        </a>
                    </li>

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.comments*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.comments.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.comments*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Comments</span>
                        </a>
                    </li>

                    

                    
                </ul>
            </li>

            <!-- Location Management Section -->
            

            <!-- Orders & Engagement Section -->
            

            <!-- User & Subscription Management Section -->
            <li class="pt-4 mt-4 border-t border-border-light dark:border-border-dark" x-data="{ open: false }">
                <button type="button" @click="open = !open"
                    class="w-full flex items-center justify-between px-4 py-2 text-xs font-semibold text-accent-light dark:text-accent-dark uppercase tracking-wider focus:outline-none">
                    <span>User & Subscription</span>
                    <svg :class="{ 'transform rotate-90': open }" class="w-3 h-3 transition-transform duration-200"
                        fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
                <ul x-show="open" x-transition class="space-y-1">

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.users*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.users.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.users*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Users</span>
                        </a>
                    </li>

                    
                </ul>
            </li>

            <!-- System Settings Section -->
            <li class="pt-4 mt-4 border-t border-border-light dark:border-border-dark" x-data="{ open: false }">
                <button type="button" @click="open = !open"
                    class="w-full flex items-center justify-between px-4 py-2 text-xs font-semibold text-accent-light dark:text-accent-dark uppercase tracking-wider focus:outline-none">
                    <span>System Settings</span>
                    <svg :class="{ 'transform rotate-90': open }" class="w-3 h-3 transition-transform duration-200"
                        fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
                <ul x-show="open" x-transition class="space-y-1">

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.settings*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.settings.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.settings*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z">
                                </path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">General Settings</span>
                        </a>
                    </li>

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.mail-config*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.mail-config.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.mail-config*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Mail Configuration</span>
                        </a>
                    </li>

                    

                    
                </ul>
            </li>
        </ul>

        <!-- Account Section -->
        
    </nav>
</aside>
<?php /**PATH C:\lara\www\pc-builder\resources\views/admin/layouts/sidebar.blade.php ENDPATH**/ ?>