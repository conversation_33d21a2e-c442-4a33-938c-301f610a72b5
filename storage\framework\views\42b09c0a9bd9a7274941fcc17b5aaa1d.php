<div class="bg-white rounded-lg border p-4">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
            <!-- Compatibility Icon -->
            <div class="flex-shrink-0">
                <!--[if BLOCK]><![endif]--><?php if($compatibilityStatus === 'compatible'): ?>
                    <svg class="h-6 w-6 <?php echo e($compatibilityColor); ?>" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                <?php elseif($compatibilityStatus === 'warning'): ?>
                    <svg class="h-6 w-6 <?php echo e($compatibilityColor); ?>" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                <?php elseif($compatibilityStatus === 'incompatible'): ?>
                    <svg class="h-6 w-6 <?php echo e($compatibilityColor); ?>" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                <?php else: ?>
                    <svg class="h-6 w-6 <?php echo e($compatibilityColor); ?>" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
            
            <!-- Compatibility Message -->
            <div>
                <h3 class="text-sm font-medium <?php echo e($compatibilityColor); ?>">
                    <?php echo e($compatibilityMessage); ?>

                </h3>
                <!--[if BLOCK]><![endif]--><?php if(!empty($compatibilityResult) && (($compatibilityResult['has_warnings'] ?? false) || !($compatibilityResult['compatible'] ?? false))): ?>
                    <p class="text-xs text-gray-500 mt-1">
                        Click to view details
                    </p>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>
        
        <!-- Toggle Details Button -->
        <!--[if BLOCK]><![endif]--><?php if(!empty($compatibilityResult) && (($compatibilityResult['has_warnings'] ?? false) || !($compatibilityResult['compatible'] ?? false))): ?>
            <button 
                wire:click="toggleDetails"
                class="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
            >
                <svg class="h-5 w-5 transform <?php echo e($showDetails ? 'rotate-180' : ''); ?> transition-transform" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
            </button>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
    
    <!-- Compatibility Details -->
    <!--[if BLOCK]><![endif]--><?php if($showDetails && !empty($compatibilityResult)): ?>
        <div class="mt-4 pt-4 border-t border-gray-200">
            <!-- Compatibility Issues -->
            <!--[if BLOCK]><![endif]--><?php if(!($compatibilityResult['compatible'] ?? false) && !empty($compatibilityResult['error_messages'] ?? [])): ?>
                <div class="mb-4">
                    <h4 class="text-sm font-medium text-red-800 mb-2">Compatibility Issues:</h4>
                    <ul class="space-y-1">
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $compatibilityResult['error_messages']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $issue): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li class="flex items-start space-x-2">
                                <svg class="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                                <span class="text-sm text-red-700"><?php echo e($issue); ?></span>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </ul>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            
            <!-- Compatibility Warnings -->
            <!--[if BLOCK]><![endif]--><?php if(($compatibilityResult['has_warnings'] ?? false) && !empty($compatibilityResult['warning_messages'] ?? [])): ?>
                <div class="mb-4">
                    <h4 class="text-sm font-medium text-yellow-800 mb-2">Warnings:</h4>
                    <ul class="space-y-1">
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $compatibilityResult['warning_messages']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $warning): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li class="flex items-start space-x-2">
                                <svg class="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                                <span class="text-sm text-yellow-700"><?php echo e($warning); ?></span>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </ul>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div><?php /**PATH C:\lara\www\pc-builder\resources\views/livewire/builder/compatibility-checker.blade.php ENDPATH**/ ?>