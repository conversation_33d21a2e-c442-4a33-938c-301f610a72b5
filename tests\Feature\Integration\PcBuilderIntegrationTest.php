<?php

namespace Tests\Feature\Integration;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\User;
use App\Models\PcBuild;
use App\Models\PcBuildItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PcBuilderIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected ComponentCategory $cpuCategory;
    protected ComponentCategory $motherboardCategory;
    protected ComponentCategory $ramCategory;
    protected ComponentCategory $gpuCategory;
    protected ComponentCategory $storageCategory;
    protected ComponentCategory $psuCategory;
    protected ComponentCategory $caseCategory;
    protected ComponentCategory $coolingCategory;
    protected Component $cpu;
    protected Component $motherboard;
    protected Component $ram;
    protected Component $gpu;
    protected Component $storage;
    protected Component $psu;
    protected Component $case;
    protected Component $cooling;

    protected function setUp(): void
    {
        parent::setUp();

        // Create user
        $this->user = User::factory()->create();

        // Create component categories
        $this->cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU', 
            'slug' => 'cpu'
        ]);

        $this->motherboardCategory = ComponentCategory::factory()->create([
            'name' => 'Motherboard', 
            'slug' => 'motherboard'
        ]);

        $this->ramCategory = ComponentCategory::factory()->create([
            'name' => 'RAM', 
            'slug' => 'ram'
        ]);

        $this->gpuCategory = ComponentCategory::factory()->create([
            'name' => 'GPU', 
            'slug' => 'gpu'
        ]);

        $this->storageCategory = ComponentCategory::factory()->create([
            'name' => 'Storage', 
            'slug' => 'storage'
        ]);

        $this->psuCategory = ComponentCategory::factory()->create([
            'name' => 'Power Supply', 
            'slug' => 'psu'
        ]);

        $this->caseCategory = ComponentCategory::factory()->create([
            'name' => 'Case', 
            'slug' => 'case'
        ]);

        $this->coolingCategory = ComponentCategory::factory()->create([
            'name' => 'Cooling', 
            'slug' => 'cooling'
        ]);

        // Create compatible components
        $this->cpu = Component::factory()->create([
            'name' => 'Intel Core i7-12700K',
            'slug' => 'intel-core-i7-12700k',
            'category_id' => $this->cpuCategory->id,
            'brand' => 'Intel',
            'model' => 'i7-12700K',
            'price' => 399.99,
            'stock' => 10,
            'socket_type' => 'LGA1700',
            'power_consumption' => 125,
            'is_active' => true
        ]);

        $this->motherboard = Component::factory()->create([
            'name' => 'ASUS ROG Strix Z690-A',
            'slug' => 'asus-rog-strix-z690-a',
            'category_id' => $this->motherboardCategory->id,
            'brand' => 'ASUS',
            'model' => 'ROG Strix Z690-A',
            'price' => 299.99,
            'stock' => 5,
            'socket_type' => 'LGA1700',
            'form_factor' => 'ATX',
            'memory_type' => 'DDR4',
            'is_active' => true
        ]);

        $this->ram = Component::factory()->create([
            'name' => 'Corsair Vengeance LPX 32GB',
            'slug' => 'corsair-vengeance-lpx-32gb',
            'category_id' => $this->ramCategory->id,
            'brand' => 'Corsair',
            'model' => 'Vengeance LPX',
            'price' => 149.99,
            'stock' => 15,
            'memory_type' => 'DDR4',
            'is_active' => true
        ]);

        $this->gpu = Component::factory()->create([
            'name' => 'NVIDIA GeForce RTX 3080',
            'slug' => 'nvidia-geforce-rtx-3080',
            'category_id' => $this->gpuCategory->id,
            'brand' => 'NVIDIA',
            'model' => 'GeForce RTX 3080',
            'price' => 699.99,
            'stock' => 3,
            'interface_type' => 'PCIe 4.0',
            'power_consumption' => 320,
            'is_active' => true
        ]);

        $this->storage = Component::factory()->create([
            'name' => 'Samsung 970 EVO Plus 1TB',
            'slug' => 'samsung-970-evo-plus-1tb',
            'category_id' => $this->storageCategory->id,
            'brand' => 'Samsung',
            'model' => '970 EVO Plus',
            'price' => 129.99,
            'stock' => 20,
            'interface_type' => 'M.2 NVMe',
            'is_active' => true
        ]);

        $this->psu = Component::factory()->create([
            'name' => 'Corsair RM850x',
            'slug' => 'corsair-rm850x',
            'category_id' => $this->psuCategory->id,
            'brand' => 'Corsair',
            'model' => 'RM850x',
            'price' => 149.99,
            'stock' => 8,
            'power_consumption' => 850,
            'is_active' => true
        ]);

        $this->case = Component::factory()->create([
            'name' => 'Fractal Design Meshify C',
            'slug' => 'fractal-design-meshify-c',
            'category_id' => $this->caseCategory->id,
            'brand' => 'Fractal Design',
            'model' => 'Meshify C',
            'price' => 99.99,
            'stock' => 7,
            'form_factor' => 'ATX',
            'is_active' => true
        ]);

        $this->cooling = Component::factory()->create([
            'name' => 'Noctua NH-D15',
            'slug' => 'noctua-nh-d15',
            'category_id' => $this->coolingCategory->id,
            'brand' => 'Noctua',
            'model' => 'NH-D15',
            'price' => 89.99,
            'stock' => 12,
            'cooling_type' => 'Air',
            'socket_type' => 'LGA1700',
            'is_active' => true
        ]);

        // Create incompatible components for testing
        Component::factory()->create([
            'name' => 'AMD Ryzen 7 5800X',
            'slug' => 'amd-ryzen-7-5800x',
            'category_id' => $this->cpuCategory->id,
            'brand' => 'AMD',
            'model' => 'Ryzen 7 5800X',
            'price' => 349.99,
            'stock' => 10,
            'socket_type' => 'AM4',
            'power_consumption' => 105,
            'is_active' => true
        ]);

        Component::factory()->create([
            'name' => 'Corsair Vengeance RGB Pro 32GB DDR5',
            'slug' => 'corsair-vengeance-rgb-pro-32gb-ddr5',
            'category_id' => $this->ramCategory->id,
            'brand' => 'Corsair',
            'model' => 'Vengeance RGB Pro',
            'price' => 199.99,
            'stock' => 8,
            'memory_type' => 'DDR5',
            'is_active' => true
        ]);
    }

    /** @test */
    public function user_can_view_pc_builder_page()
    {
        $this->actingAs($this->user);

        $response = $this->get('/pc-builder');

        $response->assertStatus(200);
        $response->assertSee('CPU');
        $response->assertSee('Motherboard');
        $response->assertSee('RAM');
        $response->assertSee('GPU');
        $response->assertSee('Storage');
        $response->assertSee('Power Supply');
        $response->assertSee('Case');
        $response->assertSee('Cooling');
    }

    /** @test */
    public function user_can_select_cpu_for_pc_build()
    {
        $this->actingAs($this->user);

        $response = $this->post('/pc-builder/add-component', [
            'category' => 'cpu',
            'component_id' => $this->cpu->id
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true
        ]);

        // Check if component was added to the build
        $this->assertDatabaseHas('pc_builds', [
            'user_id' => $this->user->id,
            'status' => 'in_progress'
        ]);

        $build = PcBuild::where('user_id', $this->user->id)->first();
        $this->assertDatabaseHas('pc_build_items', [
            'pc_build_id' => $build->id,
            'component_id' => $this->cpu->id,
            'category' => 'cpu'
        ]);
    }

    /** @test */
    public function user_can_add_all_components_to_build()
    {
        $this->actingAs($this->user);

        // Create a new build
        $build = PcBuild::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'in_progress'
        ]);

        // Add CPU
        $this->post('/pc-builder/add-component', [
            'category' => 'cpu',
            'component_id' => $this->cpu->id,
            'pc_build_id' => $build->id
        ]);

        // Add Motherboard
        $this->post('/pc-builder/add-component', [
            'category' => 'motherboard',
            'component_id' => $this->motherboard->id,
            'pc_build_id' => $build->id
        ]);

        // Add RAM
        $this->post('/pc-builder/add-component', [
            'category' => 'ram',
            'component_id' => $this->ram->id,
            'pc_build_id' => $build->id
        ]);

        // Add GPU
        $this->post('/pc-builder/add-component', [
            'category' => 'gpu',
            'component_id' => $this->gpu->id,
            'pc_build_id' => $build->id
        ]);

        // Add Storage
        $this->post('/pc-builder/add-component', [
            'category' => 'storage',
            'component_id' => $this->storage->id,
            'pc_build_id' => $build->id
        ]);

        // Add PSU
        $this->post('/pc-builder/add-component', [
            'category' => 'psu',
            'component_id' => $this->psu->id,
            'pc_build_id' => $build->id
        ]);

        // Add Case
        $this->post('/pc-builder/add-component', [
            'category' => 'case',
            'component_id' => $this->case->id,
            'pc_build_id' => $build->id
        ]);

        // Add Cooling
        $this->post('/pc-builder/add-component', [
            'category' => 'cooling',
            'component_id' => $this->cooling->id,
            'pc_build_id' => $build->id
        ]);

        // Check if all components were added
        $this->assertDatabaseCount('pc_build_items', 8);

        // View the build
        $response = $this->get('/pc-builder?build_id=' . $build->id);
        $response->assertStatus(200);
        $response->assertSee('Intel Core i7-12700K');
        $response->assertSee('ASUS ROG Strix Z690-A');
        $response->assertSee('Corsair Vengeance LPX 32GB');
        $response->assertSee('NVIDIA GeForce RTX 3080');
        $response->assertSee('Samsung 970 EVO Plus 1TB');
        $response->assertSee('Corsair RM850x');
        $response->assertSee('Fractal Design Meshify C');
        $response->assertSee('Noctua NH-D15');
    }

    /** @test */
    public function user_can_remove_component_from_build()
    {
        $this->actingAs($this->user);

        // Create a new build
        $build = PcBuild::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'in_progress'
        ]);

        // Add CPU
        $this->post('/pc-builder/add-component', [
            'category' => 'cpu',
            'component_id' => $this->cpu->id,
            'pc_build_id' => $build->id
        ]);

        // Get the build item
        $buildItem = PcBuildItem::where('pc_build_id', $build->id)
            ->where('component_id', $this->cpu->id)
            ->first();

        // Remove the CPU
        $response = $this->delete('/pc-builder/remove-component/' . $buildItem->id);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true
        ]);

        // Check if component was removed
        $this->assertDatabaseMissing('pc_build_items', [
            'id' => $buildItem->id
        ]);
    }

    /** @test */
    public function user_can_replace_component_in_build()
    {
        $this->actingAs($this->user);

        // Create a new build
        $build = PcBuild::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'in_progress'
        ]);

        // Add CPU
        $this->post('/pc-builder/add-component', [
            'category' => 'cpu',
            'component_id' => $this->cpu->id,
            'pc_build_id' => $build->id
        ]);

        // Create another CPU
        $anotherCpu = Component::factory()->create([
            'name' => 'Intel Core i9-12900K',
            'slug' => 'intel-core-i9-12900k',
            'category_id' => $this->cpuCategory->id,
            'brand' => 'Intel',
            'model' => 'i9-12900K',
            'price' => 599.99,
            'stock' => 5,
            'socket_type' => 'LGA1700',
            'power_consumption' => 150,
            'is_active' => true
        ]);

        // Replace the CPU
        $response = $this->post('/pc-builder/add-component', [
            'category' => 'cpu',
            'component_id' => $anotherCpu->id,
            'pc_build_id' => $build->id
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true
        ]);

        // Check if component was replaced
        $this->assertDatabaseMissing('pc_build_items', [
            'pc_build_id' => $build->id,
            'component_id' => $this->cpu->id
        ]);

        $this->assertDatabaseHas('pc_build_items', [
            'pc_build_id' => $build->id,
            'component_id' => $anotherCpu->id,
            'category' => 'cpu'
        ]);
    }

    /** @test */
    public function system_detects_incompatible_components()
    {
        $this->actingAs($this->user);

        // Create a new build
        $build = PcBuild::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'in_progress'
        ]);

        // Add AMD CPU (AM4 socket)
        $amdCpu = Component::where('socket_type', 'AM4')->first();
        $this->post('/pc-builder/add-component', [
            'category' => 'cpu',
            'component_id' => $amdCpu->id,
            'pc_build_id' => $build->id
        ]);

        // Try to add Intel motherboard (LGA1700 socket)
        $response = $this->post('/pc-builder/add-component', [
            'category' => 'motherboard',
            'component_id' => $this->motherboard->id,
            'pc_build_id' => $build->id
        ]);

        $response->assertStatus(422);
        $response->assertJson([
            'success' => false,
            'error' => 'Incompatible components'
        ]);

        // Check that motherboard was not added
        $this->assertDatabaseMissing('pc_build_items', [
            'pc_build_id' => $build->id,
            'component_id' => $this->motherboard->id
        ]);
    }

    /** @test */
    public function system_detects_memory_type_incompatibility()
    {
        $this->actingAs($this->user);

        // Create a new build
        $build = PcBuild::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'in_progress'
        ]);

        // Add motherboard (DDR4)
        $this->post('/pc-builder/add-component', [
            'category' => 'motherboard',
            'component_id' => $this->motherboard->id,
            'pc_build_id' => $build->id
        ]);

        // Try to add DDR5 RAM
        $ddr5Ram = Component::where('memory_type', 'DDR5')->first();
        $response = $this->post('/pc-builder/add-component', [
            'category' => 'ram',
            'component_id' => $ddr5Ram->id,
            'pc_build_id' => $build->id
        ]);

        $response->assertStatus(422);
        $response->assertJson([
            'success' => false,
            'error' => 'Incompatible components'
        ]);

        // Check that RAM was not added
        $this->assertDatabaseMissing('pc_build_items', [
            'pc_build_id' => $build->id,
            'component_id' => $ddr5Ram->id
        ]);
    }

    /** @test */
    public function system_detects_insufficient_power_supply()
    {
        $this->actingAs($this->user);

        // Create a new build
        $build = PcBuild::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'in_progress'
        ]);

        // Add CPU (125W)
        $this->post('/pc-builder/add-component', [
            'category' => 'cpu',
            'component_id' => $this->cpu->id,
            'pc_build_id' => $build->id
        ]);

        // Add GPU (320W)
        $this->post('/pc-builder/add-component', [
            'category' => 'gpu',
            'component_id' => $this->gpu->id,
            'pc_build_id' => $build->id
        ]);

        // Create a low-wattage PSU
        $lowWattagePsu = Component::factory()->create([
            'name' => 'Corsair VS450',
            'slug' => 'corsair-vs450',
            'category_id' => $this->psuCategory->id,
            'brand' => 'Corsair',
            'model' => 'VS450',
            'price' => 49.99,
            'stock' => 10,
            'power_consumption' => 450, // Not enough for CPU + GPU
            'is_active' => true
        ]);

        // Try to add insufficient PSU
        $response = $this->post('/pc-builder/add-component', [
            'category' => 'psu',
            'component_id' => $lowWattagePsu->id,
            'pc_build_id' => $build->id
        ]);

        $response->assertStatus(422);
        $response->assertJson([
            'success' => false,
            'error' => 'Insufficient power supply'
        ]);

        // Check that PSU was not added
        $this->assertDatabaseMissing('pc_build_items', [
            'pc_build_id' => $build->id,
            'component_id' => $lowWattagePsu->id
        ]);
    }

    /** @test */
    public function system_detects_case_form_factor_incompatibility()
    {
        $this->actingAs($this->user);

        // Create a new build
        $build = PcBuild::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'in_progress'
        ]);

        // Add ATX motherboard
        $this->post('/pc-builder/add-component', [
            'category' => 'motherboard',
            'component_id' => $this->motherboard->id,
            'pc_build_id' => $build->id
        ]);

        // Create a smaller case
        $microAtxCase = Component::factory()->create([
            'name' => 'Cooler Master MasterBox Q300L',
            'slug' => 'cooler-master-masterbox-q300l',
            'category_id' => $this->caseCategory->id,
            'brand' => 'Cooler Master',
            'model' => 'MasterBox Q300L',
            'price' => 59.99,
            'stock' => 15,
            'form_factor' => 'Micro-ATX', // Too small for ATX motherboard
            'is_active' => true
        ]);

        // Try to add incompatible case
        $response = $this->post('/pc-builder/add-component', [
            'category' => 'case',
            'component_id' => $microAtxCase->id,
            'pc_build_id' => $build->id
        ]);

        $response->assertStatus(422);
        $response->assertJson([
            'success' => false,
            'error' => 'Incompatible components'
        ]);

        // Check that case was not added
        $this->assertDatabaseMissing('pc_build_items', [
            'pc_build_id' => $build->id,
            'component_id' => $microAtxCase->id
        ]);
    }

    /** @test */
    public function user_can_save_pc_build()
    {
        $this->actingAs($this->user);

        // Create a new build
        $build = PcBuild::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'in_progress'
        ]);

        // Add components
        $this->post('/pc-builder/add-component', [
            'category' => 'cpu',
            'component_id' => $this->cpu->id,
            'pc_build_id' => $build->id
        ]);

        $this->post('/pc-builder/add-component', [
            'category' => 'motherboard',
            'component_id' => $this->motherboard->id,
            'pc_build_id' => $build->id
        ]);

        // Save the build
        $response = $this->post('/pc-builder/save', [
            'pc_build_id' => $build->id,
            'name' => 'My Gaming PC',
            'description' => 'My custom gaming PC build'
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true
        ]);

        // Check if build was saved
        $this->assertDatabaseHas('pc_builds', [
            'id' => $build->id,
            'name' => 'My Gaming PC',
            'description' => 'My custom gaming PC build',
            'status' => 'saved'
        ]);
    }

    /** @test */
    public function user_can_view_saved_builds()
    {
        $this->actingAs($this->user);

        // Create a saved build
        $build = PcBuild::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'My Gaming PC',
            'description' => 'My custom gaming PC build',
            'status' => 'saved'
        ]);

        // Add components
        PcBuildItem::factory()->create([
            'pc_build_id' => $build->id,
            'component_id' => $this->cpu->id,
            'category' => 'cpu'
        ]);

        PcBuildItem::factory()->create([
            'pc_build_id' => $build->id,
            'component_id' => $this->motherboard->id,
            'category' => 'motherboard'
        ]);

        // View saved builds
        $response = $this->get('/pc-builder/saved-builds');

        $response->assertStatus(200);
        $response->assertSee('My Gaming PC');
        $response->assertSee('My custom gaming PC build');
    }

    /** @test */
    public function user_can_load_saved_build()
    {
        $this->actingAs($this->user);

        // Create a saved build
        $build = PcBuild::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'My Gaming PC',
            'description' => 'My custom gaming PC build',
            'status' => 'saved'
        ]);

        // Add components
        PcBuildItem::factory()->create([
            'pc_build_id' => $build->id,
            'component_id' => $this->cpu->id,
            'category' => 'cpu'
        ]);

        PcBuildItem::factory()->create([
            'pc_build_id' => $build->id,
            'component_id' => $this->motherboard->id,
            'category' => 'motherboard'
        ]);

        // Load saved build
        $response = $this->get('/pc-builder?build_id=' . $build->id);

        $response->assertStatus(200);
        $response->assertSee('Intel Core i7-12700K');
        $response->assertSee('ASUS ROG Strix Z690-A');
    }

    /** @test */
    public function user_can_add_build_to_cart()
    {
        $this->actingAs($this->user);

        // Create a saved build
        $build = PcBuild::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'My Gaming PC',
            'description' => 'My custom gaming PC build',
            'status' => 'saved'
        ]);

        // Add components
        PcBuildItem::factory()->create([
            'pc_build_id' => $build->id,
            'component_id' => $this->cpu->id,
            'category' => 'cpu'
        ]);

        PcBuildItem::factory()->create([
            'pc_build_id' => $build->id,
            'component_id' => $this->motherboard->id,
            'category' => 'motherboard'
        ]);

        // Add build to cart
        $response = $this->post('/pc-builder/add-to-cart', [
            'pc_build_id' => $build->id
        ]);

        $response->assertStatus(302); // Redirect to cart
        $response->assertRedirect('/cart');

        // Check if components were added to cart
        $this->assertDatabaseHas('carts', [
            'user_id' => $this->user->id
        ]);

        $cart = $this->user->cart;

        $this->assertDatabaseHas('cart_items', [
            'cart_id' => $cart->id,
            'component_id' => $this->cpu->id
        ]);

        $this->assertDatabaseHas('cart_items', [
            'cart_id' => $cart->id,
            'component_id' => $this->motherboard->id
        ]);
    }

    /** @test */
    public function user_can_duplicate_build()
    {
        $this->actingAs($this->user);

        // Create a saved build
        $build = PcBuild::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'My Gaming PC',
            'description' => 'My custom gaming PC build',
            'status' => 'saved'
        ]);

        // Add components
        PcBuildItem::factory()->create([
            'pc_build_id' => $build->id,
            'component_id' => $this->cpu->id,
            'category' => 'cpu'
        ]);

        PcBuildItem::factory()->create([
            'pc_build_id' => $build->id,
            'component_id' => $this->motherboard->id,
            'category' => 'motherboard'
        ]);

        // Duplicate build
        $response = $this->post('/pc-builder/duplicate', [
            'pc_build_id' => $build->id
        ]);

        $response->assertStatus(302); // Redirect to PC builder with new build
        
        // Check if a new build was created
        $this->assertDatabaseCount('pc_builds', 2);
        
        $newBuild = PcBuild::where('user_id', $this->user->id)
            ->where('id', '!=', $build->id)
            ->first();
            
        $this->assertEquals('Copy of My Gaming PC', $newBuild->name);
        $this->assertEquals('My custom gaming PC build', $newBuild->description);
        $this->assertEquals('in_progress', $newBuild->status);
        
        // Check if components were copied
        $this->assertDatabaseHas('pc_build_items', [
            'pc_build_id' => $newBuild->id,
            'component_id' => $this->cpu->id,
            'category' => 'cpu'
        ]);
        
        $this->assertDatabaseHas('pc_build_items', [
            'pc_build_id' => $newBuild->id,
            'component_id' => $this->motherboard->id,
            'category' => 'motherboard'
        ]);
    }
}