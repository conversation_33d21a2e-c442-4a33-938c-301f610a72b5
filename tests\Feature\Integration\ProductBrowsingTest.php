<?php

namespace Tests\Feature\Integration;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\Brand;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProductBrowsingTest extends TestCase
{
    use RefreshDatabase;

    protected ComponentCategory $cpuCategory;
    protected ComponentCategory $gpuCategory;
    protected ProductCategory $productCategory;
    protected Brand $brand1;
    protected Brand $brand2;

    protected function setUp(): void
    {
        parent::setUp();

        // Create component categories
        $this->cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU', 
            'slug' => 'cpu'
        ]);

        $this->gpuCategory = ComponentCategory::factory()->create([
            'name' => 'GPU', 
            'slug' => 'gpu'
        ]);

        // Create product category
        $this->productCategory = ProductCategory::factory()->create([
            'name' => 'Gaming PC', 
            'slug' => 'gaming-pc'
        ]);

        // Create brands
        $this->brand1 = Brand::factory()->create([
            'name' => 'Brand One',
            'slug' => 'brand-one'
        ]);

        $this->brand2 = Brand::factory()->create([
            'name' => 'Brand Two',
            'slug' => 'brand-two'
        ]);

        // Create components
        Component::factory()->create([
            'name' => 'CPU Model A',
            'slug' => 'cpu-model-a',
            'category_id' => $this->cpuCategory->id,
            'brand' => $this->brand1->name,
            'model' => 'Model A',
            'price' => 299.99,
            'stock' => 10,
            'socket_type' => 'LGA1700',
            'power_consumption' => 65,
            'is_active' => true
        ]);

        Component::factory()->create([
            'name' => 'CPU Model B',
            'slug' => 'cpu-model-b',
            'category_id' => $this->cpuCategory->id,
            'brand' => $this->brand1->name,
            'model' => 'Model B',
            'price' => 399.99,
            'stock' => 5,
            'socket_type' => 'AM4',
            'power_consumption' => 95,
            'is_active' => true
        ]);

        Component::factory()->create([
            'name' => 'GPU Model X',
            'slug' => 'gpu-model-x',
            'category_id' => $this->gpuCategory->id,
            'brand' => $this->brand2->name,
            'model' => 'Model X',
            'price' => 499.99,
            'stock' => 8,
            'interface_type' => 'PCIe 4.0',
            'power_consumption' => 250,
            'is_active' => true
        ]);

        Component::factory()->create([
            'name' => 'GPU Model Y',
            'slug' => 'gpu-model-y',
            'category_id' => $this->gpuCategory->id,
            'brand' => $this->brand2->name,
            'model' => 'Model Y',
            'price' => 599.99,
            'stock' => 3,
            'interface_type' => 'PCIe 4.0',
            'power_consumption' => 300,
            'is_active' => true
        ]);

        // Create inactive component
        Component::factory()->create([
            'name' => 'Inactive CPU',
            'slug' => 'inactive-cpu',
            'category_id' => $this->cpuCategory->id,
            'brand' => $this->brand1->name,
            'model' => 'Inactive Model',
            'price' => 199.99,
            'stock' => 0,
            'socket_type' => 'LGA1200',
            'power_consumption' => 65,
            'is_active' => false
        ]);

        // Create products
        Product::factory()->create([
            'name' => 'Gaming PC Alpha',
            'slug' => 'gaming-pc-alpha',
            'category_id' => $this->productCategory->id,
            'brand' => $this->brand1->name,
            'sku' => 'PC-ALPHA-001',
            'price' => 1299.99,
            'sale_price' => 1199.99,
            'stock_quantity' => 5,
            'manage_stock' => true,
            'status' => 'published',
            'type' => 'physical',
            'is_featured' => true
        ]);

        Product::factory()->create([
            'name' => 'Gaming PC Beta',
            'slug' => 'gaming-pc-beta',
            'category_id' => $this->productCategory->id,
            'brand' => $this->brand2->name,
            'sku' => 'PC-BETA-002',
            'price' => 1499.99,
            'sale_price' => null,
            'stock_quantity' => 3,
            'manage_stock' => true,
            'status' => 'published',
            'type' => 'physical',
            'is_featured' => false
        ]);

        // Create draft product
        Product::factory()->create([
            'name' => 'Draft PC',
            'slug' => 'draft-pc',
            'category_id' => $this->productCategory->id,
            'brand' => $this->brand1->name,
            'sku' => 'PC-DRAFT-003',
            'price' => 999.99,
            'sale_price' => null,
            'stock_quantity' => 1,
            'manage_stock' => true,
            'status' => 'draft',
            'type' => 'physical',
            'is_featured' => false
        ]);
    }

    /** @test */
    public function user_can_view_component_listing_page()
    {
        $response = $this->get('/components');

        $response->assertStatus(200);
        $response->assertSee('CPU Model A');
        $response->assertSee('CPU Model B');
        $response->assertSee('GPU Model X');
        $response->assertSee('GPU Model Y');
        $response->assertDontSee('Inactive CPU'); // Inactive component should not be visible
    }

    /** @test */
    public function user_can_filter_components_by_category()
    {
        $response = $this->get('/components?category=cpu');

        $response->assertStatus(200);
        $response->assertSee('CPU Model A');
        $response->assertSee('CPU Model B');
        $response->assertDontSee('GPU Model X');
        $response->assertDontSee('GPU Model Y');
    }

    /** @test */
    public function user_can_filter_components_by_brand()
    {
        $response = $this->get('/components?brand=brand-one');

        $response->assertStatus(200);
        $response->assertSee('CPU Model A');
        $response->assertSee('CPU Model B');
        $response->assertDontSee('GPU Model X');
        $response->assertDontSee('GPU Model Y');
    }

    /** @test */
    public function user_can_filter_components_by_price_range()
    {
        $response = $this->get('/components?min_price=400&max_price=550');

        $response->assertStatus(200);
        $response->assertDontSee('CPU Model A'); // 299.99
        $response->assertDontSee('CPU Model B'); // 399.99 (edge case, might be included depending on implementation)
        $response->assertSee('GPU Model X'); // 499.99
        $response->assertDontSee('GPU Model Y'); // 599.99
    }

    /** @test */
    public function user_can_sort_components_by_price_ascending()
    {
        $response = $this->get('/components?sort=price_asc');

        $response->assertStatus(200);
        
        // Check that components appear in the correct order
        // This test assumes the HTML structure has components in order of appearance
        $responseContent = $response->getContent();
        $positionA = strpos($responseContent, 'CPU Model A'); // 299.99
        $positionB = strpos($responseContent, 'CPU Model B'); // 399.99
        $positionX = strpos($responseContent, 'GPU Model X'); // 499.99
        $positionY = strpos($responseContent, 'GPU Model Y'); // 599.99
        
        $this->assertTrue($positionA < $positionB);
        $this->assertTrue($positionB < $positionX);
        $this->assertTrue($positionX < $positionY);
    }

    /** @test */
    public function user_can_sort_components_by_price_descending()
    {
        $response = $this->get('/components?sort=price_desc');

        $response->assertStatus(200);
        
        // Check that components appear in the correct order
        $responseContent = $response->getContent();
        $positionA = strpos($responseContent, 'CPU Model A'); // 299.99
        $positionB = strpos($responseContent, 'CPU Model B'); // 399.99
        $positionX = strpos($responseContent, 'GPU Model X'); // 499.99
        $positionY = strpos($responseContent, 'GPU Model Y'); // 599.99
        
        $this->assertTrue($positionY < $positionX);
        $this->assertTrue($positionX < $positionB);
        $this->assertTrue($positionB < $positionA);
    }

    /** @test */
    public function user_can_view_component_detail_page()
    {
        $component = Component::where('slug', 'cpu-model-a')->first();

        $response = $this->get('/components/' . $component->slug);

        $response->assertStatus(200);
        $response->assertSee('CPU Model A');
        $response->assertSee('Brand One');
        $response->assertSee('299.99');
        $response->assertSee('LGA1700'); // Socket type
        $response->assertSee('65'); // Power consumption
    }

    /** @test */
    public function user_cannot_view_inactive_component_detail_page()
    {
        $inactiveComponent = Component::where('slug', 'inactive-cpu')->first();

        $response = $this->get('/components/' . $inactiveComponent->slug);

        $response->assertStatus(404);
    }

    /** @test */
    public function user_can_view_product_listing_page()
    {
        $response = $this->get('/products');

        $response->assertStatus(200);
        $response->assertSee('Gaming PC Alpha');
        $response->assertSee('Gaming PC Beta');
        $response->assertDontSee('Draft PC'); // Draft product should not be visible
    }

    /** @test */
    public function user_can_filter_products_by_category()
    {
        $response = $this->get('/products?category=gaming-pc');

        $response->assertStatus(200);
        $response->assertSee('Gaming PC Alpha');
        $response->assertSee('Gaming PC Beta');
    }

    /** @test */
    public function user_can_filter_products_by_brand()
    {
        $response = $this->get('/products?brand=brand-one');

        $response->assertStatus(200);
        $response->assertSee('Gaming PC Alpha');
        $response->assertDontSee('Gaming PC Beta');
    }

    /** @test */
    public function user_can_view_featured_products()
    {
        $response = $this->get('/products?featured=1');

        $response->assertStatus(200);
        $response->assertSee('Gaming PC Alpha'); // Featured
        $response->assertDontSee('Gaming PC Beta'); // Not featured
    }

    /** @test */
    public function user_can_view_products_on_sale()
    {
        $response = $this->get('/products?on_sale=1');

        $response->assertStatus(200);
        $response->assertSee('Gaming PC Alpha'); // Has sale price
        $response->assertDontSee('Gaming PC Beta'); // No sale price
    }

    /** @test */
    public function user_can_view_product_detail_page()
    {
        $product = Product::where('slug', 'gaming-pc-alpha')->first();

        $response = $this->get('/products/' . $product->slug);

        $response->assertStatus(200);
        $response->assertSee('Gaming PC Alpha');
        $response->assertSee('Brand One');
        $response->assertSee('1299.99'); // Regular price
        $response->assertSee('1199.99'); // Sale price
        $response->assertSee('PC-ALPHA-001'); // SKU
    }

    /** @test */
    public function user_cannot_view_draft_product_detail_page()
    {
        $draftProduct = Product::where('slug', 'draft-pc')->first();

        $response = $this->get('/products/' . $draftProduct->slug);

        $response->assertStatus(404);
    }

    /** @test */
    public function user_can_search_for_components()
    {
        $response = $this->get('/search?q=cpu');

        $response->assertStatus(200);
        $response->assertSee('CPU Model A');
        $response->assertSee('CPU Model B');
        $response->assertDontSee('GPU Model X');
        $response->assertDontSee('GPU Model Y');
    }

    /** @test */
    public function user_can_search_for_products()
    {
        $response = $this->get('/search?q=gaming');

        $response->assertStatus(200);
        $response->assertSee('Gaming PC Alpha');
        $response->assertSee('Gaming PC Beta');
        $response->assertDontSee('Draft PC');
    }

    /** @test */
    public function search_returns_empty_results_for_nonexistent_items()
    {
        $response = $this->get('/search?q=nonexistent');

        $response->assertStatus(200);
        $response->assertSee('No results found');
        $response->assertDontSee('CPU Model A');
        $response->assertDontSee('Gaming PC Alpha');
    }

    /** @test */
    public function user_can_view_components_by_technical_specifications()
    {
        $response = $this->get('/components?socket_type=LGA1700');

        $response->assertStatus(200);
        $response->assertSee('CPU Model A'); // LGA1700
        $response->assertDontSee('CPU Model B'); // AM4
    }

    /** @test */
    public function user_can_view_components_with_stock_availability_filter()
    {
        // Create a component with zero stock but is_active=true
        Component::factory()->create([
            'name' => 'Out of Stock CPU',
            'slug' => 'out-of-stock-cpu',
            'category_id' => $this->cpuCategory->id,
            'brand' => $this->brand1->name,
            'model' => 'Out of Stock Model',
            'price' => 249.99,
            'stock' => 0,
            'socket_type' => 'LGA1700',
            'power_consumption' => 65,
            'is_active' => true
        ]);

        $response = $this->get('/components?in_stock=1');

        $response->assertStatus(200);
        $response->assertSee('CPU Model A'); // In stock
        $response->assertSee('CPU Model B'); // In stock
        $response->assertDontSee('Out of Stock CPU'); // Out of stock
    }
}