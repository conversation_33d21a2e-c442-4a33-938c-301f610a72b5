<div class="relative">
    <!-- Modern Cart Icon Button -->
    <button
        wire:click="toggleDropdown"
        class="relative p-3 text-nexus-gray-600 dark:text-nexus-gray-300 
               hover:text-nexus-primary-600 dark:hover:text-nexus-primary-400 
               hover:bg-gradient-to-r hover:from-nexus-primary-50 hover:to-nexus-secondary-50 
               dark:hover:from-nexus-primary-900/20 dark:hover:to-nexus-secondary-900/20 
               rounded-xl transition-all duration-300 group transform hover:scale-105 
               focus:outline-none focus:ring-2 focus:ring-nexus-primary-500/30 
               dark:focus:ring-nexus-primary-400/30 shadow-lg hover:shadow-glow-blue"
        aria-label="Shopping cart"
        title="Shopping Cart">
        
        <!-- Modern Cart Icon -->
        <svg class="h-6 w-6 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m0 0h8m-8 0a2 2 0 100 4 2 2 0 000-4zm8 0a2 2 0 100 4 2 2 0 000-4z"></path>
        </svg>

        <!-- Enhanced Item Count Badge -->
        @if($itemCount > 0)
        <span class="absolute -top-1 -right-1 h-5 w-5 
                     bg-gradient-to-br from-nexus-error-500 to-nexus-error-600 
                     text-white text-xs font-bold rounded-full 
                     flex items-center justify-center animate-pulse 
                     shadow-lg shadow-nexus-error-500/40 
                     ring-2 ring-white dark:ring-nexus-dark-900">
            {{ $itemCount > 99 ? '99+' : $itemCount }}
        </span>
        @endif
    </button>

    <!-- Modern Dropdown Menu -->
    @if($showDropdown)
    <div class="absolute right-0 mt-3 w-96 
                bg-white dark:bg-nexus-dark-800 
                rounded-2xl shadow-2xl shadow-nexus-primary-500/10
                border border-nexus-primary-200/30 dark:border-nexus-primary-700/30
                backdrop-blur-sm z-50 overflow-hidden
                ring-1 ring-black/5 dark:ring-white/10">
        
        @if($itemCount === 0)
        <!-- Modern Empty Cart State -->
        <div class="p-8 text-center">
            <div class="mx-auto w-16 h-16 
                        bg-gradient-to-br from-nexus-primary-100 to-nexus-secondary-100 
                        dark:from-nexus-primary-900/30 dark:to-nexus-secondary-900/30 
                        rounded-full flex items-center justify-center mb-4 
                        shadow-lg shadow-nexus-primary-500/20">
                <svg class="w-8 h-8 text-nexus-gray-400 dark:text-nexus-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m0 0h8m-8 0a2 2 0 100 4 2 2 0 000-4zm8 0a2 2 0 100 4 2 2 0 000-4z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-nexus-gray-900 dark:text-nexus-gray-100 mb-2">
                Your cart is empty
            </h3>
            <p class="text-nexus-gray-500 dark:text-nexus-gray-400 text-sm mb-6">
                Discover amazing PC components and start building your dream setup!
            </p>
            <a href="{{ route('shop.index') }}"
               onclick="@this.hideDropdown()"
               class="inline-flex items-center px-6 py-3 
                      bg-gradient-to-r from-nexus-primary-600 via-nexus-secondary-600 to-nexus-accent-600 
                      hover:from-nexus-primary-700 hover:via-nexus-secondary-700 hover:to-nexus-accent-700 
                      text-white text-sm font-medium rounded-xl 
                      shadow-lg shadow-nexus-primary-500/30 hover:shadow-xl hover:shadow-nexus-primary-500/40
                      transition-all duration-300 transform hover:scale-105 
                      focus:outline-none focus:ring-2 focus:ring-nexus-primary-500/50">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                Start Shopping
            </a>
        </div>
        @else
        <!-- Modern Cart Header -->
        <div class="p-5 border-b border-nexus-primary-200/30 dark:border-nexus-primary-700/30 
                    bg-gradient-to-r from-nexus-primary-50/50 to-nexus-secondary-50/50 
                    dark:from-nexus-primary-900/10 dark:to-nexus-secondary-900/10">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gradient-to-br from-nexus-primary-500 to-nexus-secondary-500 
                                rounded-lg flex items-center justify-center shadow-lg">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m0 0h8m-8 0a2 2 0 100 4 2 2 0 000-4zm8 0a2 2 0 100 4 2 2 0 000-4z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-nexus-gray-900 dark:text-nexus-gray-100">
                            Shopping Cart
                        </h3>
                        <p class="text-sm text-nexus-gray-500 dark:text-nexus-gray-400">
                            {{ $this->getItemCountText() }}
                        </p>
                    </div>
                </div>
                <button
                    wire:click="hideDropdown"
                    class="p-2 text-nexus-gray-400 hover:text-nexus-gray-600 dark:hover:text-nexus-gray-300 
                           hover:bg-nexus-gray-100 dark:hover:bg-nexus-dark-700 
                           rounded-lg transition-colors duration-200">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- Recent Items Preview -->
        @if($recentItems->count() > 0)
        <div class="p-4 border-b border-nexus-primary-200/30 dark:border-nexus-primary-700/30">
            <h4 class="text-sm font-medium text-nexus-gray-700 dark:text-nexus-gray-300 mb-3">
                Recent Items
            </h4>
            <div class="space-y-2">
                @foreach($recentItems as $item)
                <div class="flex items-center space-x-3 p-2 rounded-lg 
                           hover:bg-nexus-primary-50 dark:hover:bg-nexus-dark-700/50 
                           transition-colors duration-200">
                    <div class="w-10 h-10 bg-nexus-gray-100 dark:bg-nexus-dark-700 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-nexus-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                        </svg>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-nexus-gray-900 dark:text-nexus-gray-100 truncate">
                            {{ $item->component->name ?? $item->product->name ?? 'Unknown Item' }}
                        </p>
                        <p class="text-xs text-nexus-gray-500 dark:text-nexus-gray-400">
                            Qty: {{ $item->quantity }} × ${{ number_format($item->price, 2) }}
                        </p>
                    </div>
                    <div class="flex items-center space-x-1">
                        <button wire:click="decrementQuantity({{ $item->id }})" 
                                class="w-6 h-6 rounded-full bg-nexus-gray-100 dark:bg-nexus-dark-700 
                                       hover:bg-nexus-error-100 dark:hover:bg-nexus-error-900/30 
                                       text-nexus-gray-600 hover:text-nexus-error-600 
                                       flex items-center justify-center transition-colors duration-200">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                            </svg>
                        </button>
                        <button wire:click="incrementQuantity({{ $item->id }})" 
                                class="w-6 h-6 rounded-full bg-nexus-gray-100 dark:bg-nexus-dark-700 
                                       hover:bg-nexus-success-100 dark:hover:bg-nexus-success-900/30 
                                       text-nexus-gray-600 hover:text-nexus-success-600 
                                       flex items-center justify-center transition-colors duration-200">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Modern Cart Summary & Actions -->
        <div class="p-5 bg-gradient-to-r from-nexus-primary-50/50 to-nexus-secondary-50/50 
                    dark:from-nexus-primary-900/10 dark:to-nexus-secondary-900/10">
            <div class="flex items-center justify-between mb-4">
                <span class="text-lg font-semibold text-nexus-gray-700 dark:text-nexus-gray-300">
                    Total:
                </span>
                <span class="text-2xl font-bold bg-gradient-to-r from-nexus-primary-600 to-nexus-secondary-600 
                           bg-clip-text text-transparent">
                    ${{ $this->getFormattedTotal() }}
                </span>
            </div>
            
            <div class="space-y-3">
                <a href="{{ route('cart.index') }}" 
                   onclick="@this.hideDropdown()"
                   class="block w-full px-4 py-3 
                          bg-gradient-to-r from-nexus-primary-600 to-nexus-secondary-600 
                          hover:from-nexus-primary-700 hover:to-nexus-secondary-700 
                          text-white text-sm font-semibold rounded-xl text-center 
                          shadow-lg shadow-nexus-primary-500/30 hover:shadow-xl hover:shadow-nexus-primary-500/40
                          transition-all duration-300 transform hover:scale-105
                          focus:outline-none focus:ring-2 focus:ring-nexus-primary-500/50">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m0 0h8m-8 0a2 2 0 100 4 2 2 0 000-4zm8 0a2 2 0 100 4 2 2 0 000-4z"></path>
                    </svg>
                    View Full Cart
                </a>
                
                <a href="{{ route('checkout.index') }}" 
                   onclick="@this.hideDropdown()"
                   class="block w-full px-4 py-3 
                          bg-gradient-to-r from-nexus-success-600 to-nexus-success-700 
                          hover:from-nexus-success-700 hover:to-nexus-success-800 
                          text-white text-sm font-semibold rounded-xl text-center 
                          shadow-lg shadow-nexus-success-500/30 hover:shadow-xl hover:shadow-nexus-success-500/40
                          transition-all duration-300 transform hover:scale-105
                          focus:outline-none focus:ring-2 focus:ring-nexus-success-500/50">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    Checkout Now
                </a>
            </div>
        </div>
        @endif
    </div>
    @endif
</div>