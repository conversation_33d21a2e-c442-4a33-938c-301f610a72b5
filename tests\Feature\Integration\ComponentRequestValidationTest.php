<?php

namespace Tests\Feature\Integration;

use App\Http\Requests\ComponentRequest;
use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use Tests\TestCase;

class ComponentRequestValidationTest extends TestCase
{
    use RefreshDatabase;

    protected User $adminUser;
    protected ComponentCategory $cpuCategory;
    protected ComponentCategory $motherboardCategory;
    protected ComponentCategory $ramCategory;
    protected ComponentCategory $gpuCategory;
    protected ComponentCategory $storageCategory;
    protected ComponentCategory $psuCategory;
    protected ComponentCategory $caseCategory;
    protected ComponentCategory $coolingCategory;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->adminUser = User::factory()->create([
            'role' => 'admin'
        ]);

        // Create component categories
        $this->cpuCategory = ComponentCategory::factory()->create(['name' => 'CPU', 'slug' => 'cpu']);
        $this->motherboardCategory = ComponentCategory::factory()->create(['name' => 'Motherboard', 'slug' => 'motherboard']);
        $this->ramCategory = ComponentCategory::factory()->create(['name' => 'RAM', 'slug' => 'ram']);
        $this->gpuCategory = ComponentCategory::factory()->create(['name' => 'Graphics Card', 'slug' => 'gpu']);
        $this->storageCategory = ComponentCategory::factory()->create(['name' => 'Storage', 'slug' => 'storage']);
        $this->psuCategory = ComponentCategory::factory()->create(['name' => 'Power Supply', 'slug' => 'psu']);
        $this->caseCategory = ComponentCategory::factory()->create(['name' => 'Case', 'slug' => 'case']);
        $this->coolingCategory = ComponentCategory::factory()->create(['name' => 'Cooling', 'slug' => 'cooling']);
    }

    /** @test */
    public function it_validates_basic_component_fields()
    {
        $this->actingAs($this->adminUser);

        // Test with missing required fields
        $response = $this->postJson('/api/admin/components', []);
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'name',
            'slug',
            'category_id',
            'brand',
            'model',
            'price',
            'stock'
        ]);

        // Test with invalid data
        $response = $this->postJson('/api/admin/components', [
            'name' => 'ab', // too short
            'slug' => 'invalid slug with spaces',
            'category_id' => 999, // non-existent
            'brand' => 'a', // too short
            'model' => '',
            'price' => -10, // negative
            'stock' => -5, // negative
        ]);
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'name',
            'slug',
            'category_id',
            'brand',
            'model',
            'price',
            'stock'
        ]);

        // Test with valid data
        $validData = [
            'name' => 'Test Component',
            'slug' => 'test-component',
            'category_id' => $this->cpuCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 99.99,
            'stock' => 10,
            'socket_type' => 'LGA1700', // Required for CPU
            'power_consumption' => 65, // Required for CPU
            'is_active' => true
        ];

        $response = $this->postJson('/api/admin/components', $validData);
        $response->assertStatus(201);
    }

    /** @test */
    public function it_validates_cpu_specific_fields()
    {
        $this->actingAs($this->adminUser);

        // Missing required CPU fields
        $response = $this->postJson('/api/admin/components', [
            'name' => 'Test CPU',
            'slug' => 'test-cpu',
            'category_id' => $this->cpuCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 299.99,
            'stock' => 10,
            'is_active' => true
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'socket_type',
            'power_consumption'
        ]);

        // Valid CPU data
        $validData = [
            'name' => 'Test CPU',
            'slug' => 'test-cpu',
            'category_id' => $this->cpuCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 299.99,
            'stock' => 10,
            'socket_type' => 'LGA1700',
            'power_consumption' => 65,
            'is_active' => true
        ];

        $response = $this->postJson('/api/admin/components', $validData);
        $response->assertStatus(201);
    }

    /** @test */
    public function it_validates_motherboard_specific_fields()
    {
        $this->actingAs($this->adminUser);

        // Missing required motherboard fields
        $response = $this->postJson('/api/admin/components', [
            'name' => 'Test Motherboard',
            'slug' => 'test-motherboard',
            'category_id' => $this->motherboardCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 199.99,
            'stock' => 10,
            'is_active' => true
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'socket_type',
            'form_factor',
            'memory_type'
        ]);

        // Valid motherboard data
        $validData = [
            'name' => 'Test Motherboard',
            'slug' => 'test-motherboard',
            'category_id' => $this->motherboardCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 199.99,
            'stock' => 10,
            'socket_type' => 'LGA1700',
            'form_factor' => 'ATX',
            'memory_type' => 'DDR4',
            'is_active' => true
        ];

        $response = $this->postJson('/api/admin/components', $validData);
        $response->assertStatus(201);
    }

    /** @test */
    public function it_validates_ram_specific_fields()
    {
        $this->actingAs($this->adminUser);

        // Missing required RAM fields
        $response = $this->postJson('/api/admin/components', [
            'name' => 'Test RAM',
            'slug' => 'test-ram',
            'category_id' => $this->ramCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 89.99,
            'stock' => 20,
            'is_active' => true
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'memory_type'
        ]);

        // Valid RAM data
        $validData = [
            'name' => 'Test RAM',
            'slug' => 'test-ram',
            'category_id' => $this->ramCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 89.99,
            'stock' => 20,
            'memory_type' => 'DDR4',
            'is_active' => true
        ];

        $response = $this->postJson('/api/admin/components', $validData);
        $response->assertStatus(201);
    }

    /** @test */
    public function it_validates_gpu_specific_fields()
    {
        $this->actingAs($this->adminUser);

        // Missing required GPU fields
        $response = $this->postJson('/api/admin/components', [
            'name' => 'Test GPU',
            'slug' => 'test-gpu',
            'category_id' => $this->gpuCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 399.99,
            'stock' => 5,
            'is_active' => true
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'interface_type',
            'power_consumption'
        ]);

        // Valid GPU data
        $validData = [
            'name' => 'Test GPU',
            'slug' => 'test-gpu',
            'category_id' => $this->gpuCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 399.99,
            'stock' => 5,
            'interface_type' => 'PCIe 4.0',
            'power_consumption' => 250,
            'is_active' => true
        ];

        $response = $this->postJson('/api/admin/components', $validData);
        $response->assertStatus(201);
    }

    /** @test */
    public function it_validates_storage_specific_fields()
    {
        $this->actingAs($this->adminUser);

        // Missing required storage fields
        $response = $this->postJson('/api/admin/components', [
            'name' => 'Test SSD',
            'slug' => 'test-ssd',
            'category_id' => $this->storageCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 129.99,
            'stock' => 15,
            'is_active' => true
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'interface_type'
        ]);

        // Valid storage data
        $validData = [
            'name' => 'Test SSD',
            'slug' => 'test-ssd',
            'category_id' => $this->storageCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 129.99,
            'stock' => 15,
            'interface_type' => 'NVMe',
            'is_active' => true
        ];

        $response = $this->postJson('/api/admin/components', $validData);
        $response->assertStatus(201);
    }

    /** @test */
    public function it_validates_psu_specific_fields()
    {
        $this->actingAs($this->adminUser);

        // Missing required PSU fields
        $response = $this->postJson('/api/admin/components', [
            'name' => 'Test PSU',
            'slug' => 'test-psu',
            'category_id' => $this->psuCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 89.99,
            'stock' => 10,
            'is_active' => true
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'power_consumption'
        ]);

        // Invalid PSU wattage
        $response = $this->postJson('/api/admin/components', [
            'name' => 'Test PSU',
            'slug' => 'test-psu',
            'category_id' => $this->psuCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 89.99,
            'stock' => 10,
            'power_consumption' => 150, // Less than minimum 200W
            'is_active' => true
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'power_consumption'
        ]);

        // Valid PSU data
        $validData = [
            'name' => 'Test PSU',
            'slug' => 'test-psu',
            'category_id' => $this->psuCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 89.99,
            'stock' => 10,
            'power_consumption' => 650, // Valid wattage
            'is_active' => true
        ];

        $response = $this->postJson('/api/admin/components', $validData);
        $response->assertStatus(201);
    }

    /** @test */
    public function it_validates_case_specific_fields()
    {
        $this->actingAs($this->adminUser);

        // Missing required case fields
        $response = $this->postJson('/api/admin/components', [
            'name' => 'Test Case',
            'slug' => 'test-case',
            'category_id' => $this->caseCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 79.99,
            'stock' => 8,
            'is_active' => true
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'form_factor'
        ]);

        // Valid case data
        $validData = [
            'name' => 'Test Case',
            'slug' => 'test-case',
            'category_id' => $this->caseCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 79.99,
            'stock' => 8,
            'form_factor' => 'ATX',
            'is_active' => true
        ];

        $response = $this->postJson('/api/admin/components', $validData);
        $response->assertStatus(201);
    }

    /** @test */
    public function it_validates_cooling_specific_fields()
    {
        $this->actingAs($this->adminUser);

        // Missing required cooling fields
        $response = $this->postJson('/api/admin/components', [
            'name' => 'Test Cooler',
            'slug' => 'test-cooler',
            'category_id' => $this->coolingCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 59.99,
            'stock' => 12,
            'is_active' => true
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'cooling_type',
            'socket_type'
        ]);

        // Valid cooling data
        $validData = [
            'name' => 'Test Cooler',
            'slug' => 'test-cooler',
            'category_id' => $this->coolingCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 59.99,
            'stock' => 12,
            'cooling_type' => 'Air',
            'socket_type' => 'LGA1700',
            'is_active' => true
        ];

        $response = $this->postJson('/api/admin/components', $validData);
        $response->assertStatus(201);
    }

    /** @test */
    public function it_validates_compatibility_requirements()
    {
        $this->actingAs($this->adminUser);

        // Test discontinued component cannot be active
        $response = $this->postJson('/api/admin/components', [
            'name' => 'Discontinued Component',
            'slug' => 'discontinued-component',
            'category_id' => $this->cpuCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 99.99,
            'stock' => 10,
            'socket_type' => 'LGA1700',
            'power_consumption' => 65,
            'is_active' => true,
            'discontinued_at' => now()->subDay()->format('Y-m-d')
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'is_active'
        ]);

        // Test discontinuation date cannot be before release date
        $response = $this->postJson('/api/admin/components', [
            'name' => 'Invalid Dates Component',
            'slug' => 'invalid-dates-component',
            'category_id' => $this->cpuCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 99.99,
            'stock' => 10,
            'socket_type' => 'LGA1700',
            'power_consumption' => 65,
            'is_active' => false,
            'release_date' => now()->format('Y-m-d'),
            'discontinued_at' => now()->subDays(2)->format('Y-m-d')
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'discontinued_at'
        ]);

        // Test dimensions consistency
        $response = $this->postJson('/api/admin/components', [
            'name' => 'Incomplete Dimensions',
            'slug' => 'incomplete-dimensions',
            'category_id' => $this->cpuCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 99.99,
            'stock' => 10,
            'socket_type' => 'LGA1700',
            'power_consumption' => 65,
            'is_active' => true,
            'dimensions_json' => [
                'length' => 100,
                'width' => 50
                // Missing height
            ]
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'dimensions_json'
        ]);
    }

    /** @test */
    public function it_validates_technical_specifications_directly()
    {
        // This test directly tests the validation logic without going through HTTP
        $request = new ComponentRequest();
        
        // Test CPU validation
        $cpuData = [
            'category_id' => $this->cpuCategory->id,
            // Missing socket_type and power_consumption
        ];
        
        $validator = Validator::make($cpuData, $request->rules());
        $request->withValidator($validator);
        $validator->validate();
        
        $this->assertTrue($validator->errors()->has('socket_type'));
        $this->assertTrue($validator->errors()->has('power_consumption'));
        
        // Test motherboard validation
        $motherboardData = [
            'category_id' => $this->motherboardCategory->id,
            // Missing socket_type, form_factor, and memory_type
        ];
        
        $validator = Validator::make($motherboardData, $request->rules());
        $request->withValidator($validator);
        $validator->validate();
        
        $this->assertTrue($validator->errors()->has('socket_type'));
        $this->assertTrue($validator->errors()->has('form_factor'));
        $this->assertTrue($validator->errors()->has('memory_type'));
    }
}