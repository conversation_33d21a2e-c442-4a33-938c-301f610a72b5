<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-900">My Saved Builds</h1>
        <a 
            href="<?php echo e(route('builder.index')); ?>"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
            Create New Build
        </a>
    </div>
    
    <!--[if BLOCK]><![endif]--><?php if(auth()->guard()->check()): ?>
        <!-- Filters and Search -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Search -->
                <div class="md:col-span-2">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search Builds</label>
                    <div class="relative">
                        <input 
                            type="text" 
                            id="search"
                            wire:model.debounce.300ms="search"
                            placeholder="Search by name or description..."
                            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                        >
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                            </svg>
                        </div>
                    </div>
                </div>
                
                <!-- Sort By -->
                <div>
                    <label for="sortBy" class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                    <select 
                        id="sortBy"
                        wire:model="sortBy"
                        class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    >
                        <option value="updated_at">Last Modified</option>
                        <option value="created_at">Date Created</option>
                        <option value="name">Name</option>
                        <option value="total_price">Price</option>
                    </select>
                </div>
                
                <!-- Sort Direction & Filters -->
                <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700">Options</label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input 
                                type="checkbox" 
                                wire:model="showPublicOnly"
                                class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                            >
                            <span class="ml-2 text-sm text-gray-700">Public only</span>
                        </label>
                        
                        <div class="flex items-center space-x-2">
                            <button 
                                wire:click="$set('sortDirection', 'asc')"
                                class="px-2 py-1 text-xs border rounded <?php echo e($sortDirection === 'asc' ? 'bg-blue-100 border-blue-300 text-blue-700' : 'border-gray-300 text-gray-700'); ?>"
                            >
                                Asc
                            </button>
                            <button 
                                wire:click="$set('sortDirection', 'desc')"
                                class="px-2 py-1 text-xs border rounded <?php echo e($sortDirection === 'desc' ? 'bg-blue-100 border-blue-300 text-blue-700' : 'border-gray-300 text-gray-700'); ?>"
                            >
                                Desc
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Builds Grid -->
        <!--[if BLOCK]><![endif]--><?php if($builds->isEmpty()): ?>
            <div class="bg-white rounded-lg shadow p-12 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No builds found</h3>
                <p class="mt-1 text-sm text-gray-500">
                    <!--[if BLOCK]><![endif]--><?php if(!empty($search)): ?>
                        Try adjusting your search terms.
                    <?php else: ?>
                        Get started by creating your first PC build.
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </p>
                <div class="mt-6">
                    <a 
                        href="<?php echo e(route('builder.index')); ?>"
                        class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                        </svg>
                        Create New Build
                    </a>
                </div>
            </div>
        <?php else: ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $builds; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $build): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-white rounded-lg shadow overflow-hidden hover:shadow-lg transition-shadow">
                        <!-- Build Header -->
                        <div class="p-6 border-b border-gray-200">
                            <div class="flex justify-between items-start">
                                <div class="flex-1 min-w-0">
                                    <h3 class="text-lg font-medium text-gray-900 truncate">
                                        <?php echo e($build->name); ?>

                                    </h3>
                                    <!--[if BLOCK]><![endif]--><?php if($build->description): ?>
                                        <p class="mt-1 text-sm text-gray-500 line-clamp-2">
                                            <?php echo e($build->description); ?>

                                        </p>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                                
                                <!-- Build Status -->
                                <div class="flex flex-col items-end space-y-1">
                                    <!--[if BLOCK]><![endif]--><?php if($build->is_complete): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Complete
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            Incomplete
                                        </span>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    
                                    <!--[if BLOCK]><![endif]--><?php if($build->is_public): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            Public
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            Private
                                        </span>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                            
                            <!-- Build Meta -->
                            <div class="mt-2 text-sm text-gray-500">
                                Updated <?php echo e($build->updated_at->diffForHumans()); ?>

                            </div>
                        </div>
                        
                        <!-- Build Components -->
                        <div class="p-6">
                            <div class="space-y-2">
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $build->components->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $buildComponent): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex justify-between items-center text-sm">
                                        <span class="text-gray-600"><?php echo e($buildComponent->component->category->name ?? 'Component'); ?>:</span>
                                        <span class="font-medium text-gray-900 truncate ml-2">
                                            <?php echo e($buildComponent->component->name); ?>

                                        </span>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                
                                <!--[if BLOCK]><![endif]--><?php if($build->components->count() > 3): ?>
                                    <div class="text-sm text-gray-500">
                                        +<?php echo e($build->components->count() - 3); ?> more components
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                            
                            <!-- Build Price -->
                            <div class="mt-4 pt-4 border-t border-gray-200">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm font-medium text-gray-900">Total Price:</span>
                                    <span class="text-lg font-bold text-gray-900">
                                        $<?php echo e(number_format($build->total_price, 2)); ?>

                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Build Actions -->
                        <div class="px-6 py-3 bg-gray-50 border-t border-gray-200">
                            <div class="flex justify-between space-x-2">
                                <!-- Primary Actions -->
                                <div class="flex space-x-2">
                                    <button 
                                        wire:click="editBuild(<?php echo e($build->id); ?>)"
                                        class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                                    >
                                        Edit
                                    </button>
                                    
                                    <button 
                                        wire:click="cloneBuild(<?php echo e($build->id); ?>)"
                                        class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                                    >
                                        Clone
                                    </button>
                                </div>
                                
                                <!-- Secondary Actions -->
                                <div class="flex space-x-1">
                                    <!-- Share Button -->
                                    <button 
                                        wire:click="copyShareUrl(<?php echo e($build->id); ?>)"
                                        class="p-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
                                        title="Copy share URL"
                                    >
                                        <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                        </svg>
                                    </button>
                                    
                                    <!-- Visibility Toggle -->
                                    <button 
                                        wire:click="toggleVisibility(<?php echo e($build->id); ?>)"
                                        class="p-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
                                        title="<?php echo e($build->is_public ? 'Make private' : 'Make public'); ?>"
                                    >
                                        <!--[if BLOCK]><![endif]--><?php if($build->is_public): ?>
                                            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                            </svg>
                                        <?php else: ?>
                                            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                                            </svg>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </button>
                                    
                                    <!-- Delete Button -->
                                    <button 
                                        wire:click="deleteBuild(<?php echo e($build->id); ?>)"
                                        onclick="return confirm('Are you sure you want to delete this build?')"
                                        class="p-2 text-red-400 hover:text-red-600 focus:outline-none focus:text-red-600"
                                        title="Delete build"
                                    >
                                        <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </div>
            
            <!-- Pagination -->
            <div class="mt-6">
                <?php echo e($builds->links()); ?>

            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    <?php else: ?>
        <!-- Not Authenticated -->
        <div class="bg-white rounded-lg shadow p-12 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">Please log in</h3>
            <p class="mt-1 text-sm text-gray-500">You need to be logged in to view your saved builds.</p>
            <div class="mt-6">
                <a 
                    href="<?php echo e(route('login')); ?>"
                    class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                    Log In
                </a>
            </div>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    
    <!-- Flash Messages -->
    <!--[if BLOCK]><![endif]--><?php if(session()->has('message')): ?>
        <div class="p-4 bg-green-50 border border-green-200 rounded-md">
            <div class="flex">
                <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <p class="ml-3 text-sm text-green-700"><?php echo e(session('message')); ?></p>
            </div>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <?php if(session()->has('error')): ?>
        <div class="p-4 bg-red-50 border border-red-200 rounded-md">
            <div class="flex">
                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
                <p class="ml-3 text-sm text-red-700"><?php echo e(session('error')); ?></p>
            </div>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>

<script>
    document.addEventListener('livewire:load', function () {
        Livewire.on('copyToClipboard', function (text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text);
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
            }
        });
    });
</script><?php /**PATH C:\lara\www\pc-builder\resources\views/livewire/builder/saved-builds.blade.php ENDPATH**/ ?>