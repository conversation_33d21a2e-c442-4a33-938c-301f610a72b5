<?php

namespace App\Livewire\Shop;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\Product;
use App\Services\SearchService;
use Livewire\Component as LivewireComponent;
use Livewire\WithPagination;
use Livewire\Attributes\On;
use Livewire\Attributes\Computed;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class ProductList extends LivewireComponent
{
    use WithPagination;

    public $categories = [];
    public $selectedCategory = null;
    public $search = '';
    public $selectedBrands = [];
    public $priceMin = null;
    public $priceMax = null;
    public $inStockOnly = false;
    public $sortBy = 'relevance';
    public $sortDirection = 'desc';
    public $perPage = 12;
    public $availableBrands = [];
    public $selectedSpecs = [];
    public $minRating = null;
    public $availableSpecs = [];
    public $searchSuggestions = [];
    public $showSuggestions = false;
    public $priceRange = ['min' => 0, 'max' => 0];
    public $availableCategories = [];

    // New features
    public $wishlistItems = [];
    public $comparisonItems = [];
    public $selectedItems = [];
    public $viewMode = 'grid'; // grid or list
    public $showFilters = true;
    public $advancedSearch = false;
    public $searchFilters = [
        'name' => true,
        'description' => true,
        'brand' => true,
        'model' => false,
        'sku' => false,
        'specs' => false
    ];
    public $bulkAction = '';
    public $isLoading = false;
    public $errorMessage = '';
    public $featuredFirst = false;

    protected $queryString = [
        'search' => ['except' => ''],
        'selectedCategory' => ['except' => null, 'as' => 'category'],
        'selectedBrands' => ['except' => [], 'as' => 'brands'],
        'priceMin' => ['except' => null, 'as' => 'min_price'],
        'priceMax' => ['except' => null, 'as' => 'max_price'],
        'inStockOnly' => ['except' => false, 'as' => 'in_stock'],
        'sortBy' => ['except' => 'relevance', 'as' => 'sort'],
        'sortDirection' => ['except' => 'desc', 'as' => 'dir'],
        'selectedSpecs' => ['except' => [], 'as' => 'specs'],
        'minRating' => ['except' => null, 'as' => 'rating'],
        'perPage' => ['except' => 12, 'as' => 'per_page'],
    ];

    public function mount()
    {
        try {
            $this->loadCategories();
            $this->loadFilterOptions();
        } catch (\Exception $e) {
            Log::error('ProductList mount error: ' . $e->getMessage());
            $this->categories = [];
            $this->availableBrands = [];
            $this->availableSpecs = [];
            $this->priceRange = ['min' => 0, 'max' => 0];
            $this->availableCategories = [];
        }
    }

    public function loadCategories()
    {
        try {
            // Simplified category loading - just get component categories for now
            $this->categories = ComponentCategory::select(['name', 'slug'])
                ->orderBy('name')
                ->get()
                ->map(function ($category) {
                    return [
                        'name' => $category->name,
                        'slug' => $category->slug,
                        'type' => 'component'
                    ];
                })
                ->toArray();
        } catch (\Exception $e) {
            Log::error('Error loading categories: ' . $e->getMessage());
            $this->categories = [];
        }
    }

    public function loadFilterOptions()
    {
        try {
            // Get brands from both components and products
            $componentBrands = Component::where('is_active', true)
                ->whereNotNull('brand')
                ->distinct()
                ->pluck('brand')
                ->toArray();

            $productBrands = Product::where('status', 'active')
                ->whereNotNull('brand')
                ->distinct()
                ->pluck('brand')
                ->toArray();

            // Merge and sort brands
            $allBrands = array_unique(array_merge($componentBrands, $productBrands));
            $this->availableBrands = array_values(array_filter($allBrands));
            sort($this->availableBrands);

            // Get price range from both components and products
            $componentPriceStats = Component::where('is_active', true)
                ->selectRaw('MIN(price) as min_price, MAX(price) as max_price')
                ->first();

            $productPriceStats = Product::where('status', 'active')
                ->selectRaw('MIN(price) as min_price, MAX(price) as max_price')
                ->first();

            $minPrice = min(
                $componentPriceStats->min_price ?? PHP_INT_MAX,
                $productPriceStats->min_price ?? PHP_INT_MAX
            );

            $maxPrice = max(
                $componentPriceStats->max_price ?? 0,
                $productPriceStats->max_price ?? 0
            );

            $this->priceRange = [
                'min' => $minPrice === PHP_INT_MAX ? 0 : $minPrice,
                'max' => $maxPrice
            ];

            // For now, disable specs to avoid complexity
            $this->availableSpecs = [];
        } catch (\Exception $e) {
            Log::error('Error loading filter options: ' . $e->getMessage());
            $this->availableBrands = [];
            $this->availableSpecs = [];
            $this->priceRange = ['min' => 0, 'max' => 0];
            $this->availableCategories = [];
        }
    }

    public function getComponentsProperty()
    {
        try {
            // Get components
            $componentQuery = Component::query()
                ->where('is_active', true)
                ->with('category');

            // Get products
            $productQuery = Product::query()
                ->where('status', 'active');

            $this->applyFiltersToQueries($componentQuery, $productQuery);

            // Execute queries and get results
            $components = $componentQuery->get();
            $products = $productQuery->get();

            // Transform products to match component structure
            $transformedProducts = $this->transformProducts($products);

            // Merge and sort items
            $allItems = $this->mergeAndSortItems($components, $transformedProducts);

            // Create manual pagination
            $page = request()->get('page', 1);
            $total = $allItems->count();
            $items = $allItems->forPage($page, $this->perPage);

            return new \Illuminate\Pagination\LengthAwarePaginator(
                $items,
                $total,
                $this->perPage,
                $page,
                [
                    'path' => request()->url(),
                    'pageName' => 'page',
                ]
            );
        } catch (\Exception $e) {
            Log::error('Error in getComponentsProperty: ' . $e->getMessage());
            // Return empty pagination if there's an error
            return new \Illuminate\Pagination\LengthAwarePaginator(
                collect([]),
                0,
                $this->perPage,
                1
            );
        }
    }

    protected function applyFiltersToQueries($componentQuery, $productQuery)
    {
        // Apply search filter
        if ($this->search) {
            $componentQuery->where(function ($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                    ->orWhere('description', 'like', '%' . $this->search . '%')
                    ->orWhere('brand', 'like', '%' . $this->search . '%');
            });

            $productQuery->where(function ($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                    ->orWhere('description', 'like', '%' . $this->search . '%')
                    ->orWhere('brand', 'like', '%' . $this->search . '%');
            });
        }

        // Apply category filter
        if ($this->selectedCategory) {
            $category = ComponentCategory::where('slug', $this->selectedCategory)->first();
            if ($category) {
                $componentQuery->where('category_id', $category->id);
            }
            // For products, match by category name/slug
            $productQuery->where(function ($q) {
                $q->where('category', $this->selectedCategory)
                    ->orWhere('category', 'LIKE', '%' . str_replace('-', ' ', $this->selectedCategory) . '%');
            });
        }

        // Apply brand filter
        if ($this->selectedBrands) {
            $componentQuery->whereIn('brand', $this->selectedBrands);
            $productQuery->whereIn('brand', $this->selectedBrands);
        }

        // Apply price filters
        if ($this->priceMin) {
            $componentQuery->where('price', '>=', $this->priceMin);
            $productQuery->where('price', '>=', $this->priceMin);
        }

        if ($this->priceMax) {
            $componentQuery->where('price', '<=', $this->priceMax);
            $productQuery->where('price', '<=', $this->priceMax);
        }

        // Apply stock filter
        if ($this->inStockOnly) {
            $componentQuery->where('stock', '>', 0);
            $productQuery->where('in_stock', true)
                ->where(function ($q) {
                    $q->whereNull('stock_quantity')
                        ->orWhere('stock_quantity', '>', 0);
                });
        }
    }

    /**
     * Apply filters to product query efficiently
     */
    protected function applyProductFilters($productQuery)
    {
        // Apply category filter to products
        if ($this->selectedCategory) {
            // Use cached category check
            $isProductCategory = Cache::remember(
                'is_product_category_' . $this->selectedCategory,
                1800,
                fn() => Product::where('category', $this->selectedCategory)->exists()
            );

            if ($isProductCategory) {
                $productQuery->where('category', $this->selectedCategory);
            } else {
                // It's a component category, exclude all products
                $productQuery->where('id', 0);
            }
        }

        // Apply search filter to products
        if ($this->search) {
            $productQuery->where(function ($q) {
                $searchTerm = '%' . $this->search . '%';
                $q->where('name', 'like', $searchTerm)
                    ->orWhere('description', 'like', $searchTerm)
                    ->orWhere('sku', 'like', $searchTerm);
            });
        }

        // Apply brand filter to products
        if ($this->selectedBrands) {
            $productQuery->whereIn('brand', $this->selectedBrands);
        }

        // Apply price filters to products
        if ($this->priceMin) {
            $productQuery->where('price', '>=', $this->priceMin);
        }

        if ($this->priceMax) {
            $productQuery->where('price', '<=', $this->priceMax);
        }

        // Apply stock filter to products
        if ($this->inStockOnly) {
            $productQuery->where('in_stock', true)
                ->where(function ($q) {
                    $q->whereNull('stock_quantity')
                        ->orWhere('stock_quantity', '>', 0);
                });
        }

        // Apply specifications filter to products
        if ($this->selectedSpecs) {
            foreach ($this->selectedSpecs as $specKey => $specValues) {
                if (is_array($specValues) && !empty($specValues)) {
                    $productQuery->where(function ($q) use ($specKey, $specValues) {
                        foreach ($specValues as $specValue) {
                            $q->orWhereJsonContains('attributes->' . $specKey, $specValue);
                        }
                    });
                }
            }
        }

        // Apply rating filter to products (if you have a rating system)
        if ($this->minRating) {
            // This would need to be implemented based on your rating system
            // $productQuery->whereHas('reviews', function($q) {
            //     $q->havingRaw('AVG(rating) >= ?', [$this->minRating]);
            // });
        }
    }

    /**
     * Transform products to match component structure efficiently
     */
    protected function transformProducts($products)
    {
        return $products->map(function ($product) {
            return (object) [
                'id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
                'brand' => $product->brand,
                'model' => $product->model ?? '',
                'price' => $product->price,
                'stock' => $product->stock_quantity ?? 0,
                'image' => $product->primary_image,
                'specs' => $product->attributes ?? [],
                'category' => (object) ['name' => $product->category ?? 'Product'],
                'type' => 'product',
                'created_at' => $product->created_at,
                'average_rating' => method_exists($product, 'getAverageRating') ? $product->getAverageRating() : 0,
                'is_featured' => $product->featured ?? false,
                'in_stock' => $product->in_stock ?? true
            ];
        });
    }

    /**
     * Merge and sort items efficiently
     */
    protected function mergeAndSortItems($components, $transformedProducts)
    {
        // Add type to components
        $componentsWithType = $components->map(function ($component) {
            $component->type = 'component';
            return $component;
        });

        // For better mixing, interleave components and products instead of concat
        if ($this->sortBy === 'relevance') {
            return $this->interleaveItems($componentsWithType, $transformedProducts);
        } else {
            $allItems = $componentsWithType->concat($transformedProducts);
            return $this->applySorting($allItems);
        }
    }

    protected function applySorting($items)
    {
        switch ($this->sortBy) {
            case 'name':
                return $this->sortDirection === 'asc'
                    ? $items->sortBy('name')
                    : $items->sortByDesc('name');
            case 'price':
                return $this->sortDirection === 'asc'
                    ? $items->sortBy('price')
                    : $items->sortByDesc('price');
            case 'stock':
                return $this->sortDirection === 'asc'
                    ? $items->sortBy('stock')
                    : $items->sortByDesc('stock');
            case 'created_at':
                return $this->sortDirection === 'asc'
                    ? $items->sortBy('created_at')
                    : $items->sortByDesc('created_at');
            case 'rating':
                // Sort by rating if available
                return $this->sortDirection === 'asc'
                    ? $items->sortBy(function ($item) {
                        return $item->average_rating ?? 0;
                    })
                    : $items->sortByDesc(function ($item) {
                        return $item->average_rating ?? 0;
                    });
            case 'popularity':
                // Sort by some popularity metric (you can customize this)
                return $this->sortDirection === 'asc'
                    ? $items->sortBy('id')
                    : $items->sortByDesc('id');
            case 'relevance':
            default:
                // For relevance, items are already mixed by interleaveItems
                return $items;
        }
    }

    protected function interleaveItems($components, $products)
    {
        $result = collect();
        $componentIndex = 0;
        $productIndex = 0;
        $componentCount = $components->count();
        $productCount = $products->count();

        // Calculate ratio for interleaving (roughly 2:1 components to products)
        $componentRatio = 2;
        $productRatio = 1;

        while ($componentIndex < $componentCount || $productIndex < $productCount) {
            // Add components
            for ($i = 0; $i < $componentRatio && $componentIndex < $componentCount; $i++) {
                $result->push($components[$componentIndex]);
                $componentIndex++;
            }

            // Add products
            for ($i = 0; $i < $productRatio && $productIndex < $productCount; $i++) {
                $result->push($products[$productIndex]);
                $productIndex++;
            }
        }

        return $result;
    }

    protected function getSearchParams(): array
    {
        return [
            'search' => $this->search,
            'category' => $this->selectedCategory,
            'brands' => $this->selectedBrands,
            'price_min' => $this->priceMin,
            'price_max' => $this->priceMax,
            'specs' => $this->selectedSpecs,
            'in_stock_only' => $this->inStockOnly,
            'min_rating' => $this->minRating,
            'sort_by' => $this->sortBy,
            'sort_direction' => $this->sortDirection,
        ];
    }

    public function selectCategory($categorySlug)
    {
        $this->selectedCategory = $categorySlug;
        $this->resetPage();
        $this->loadFilterOptions();
    }

    public function clearCategory()
    {
        $this->selectedCategory = null;
        $this->resetPage();
        $this->loadFilterOptions();
    }


    public function getSearchSuggestions()
    {
        if (strlen($this->search) >= 2) {
            $searchService = app(SearchService::class);
            $this->searchSuggestions = $searchService->getSearchSuggestions($this->search, 8)->toArray();
            $this->showSuggestions = true;
        } else {
            $this->searchSuggestions = [];
            $this->showSuggestions = false;
        }
    }

    public function selectSuggestion($suggestion)
    {
        $this->search = $suggestion;
        $this->showSuggestions = false;
        $this->resetPage();
    }

    public function hideSuggestions()
    {
        $this->showSuggestions = false;
    }

    public function clearFilters()
    {
        $this->search = '';
        $this->selectedCategory = null;
        $this->selectedBrands = [];
        $this->priceMin = null;
        $this->priceMax = null;
        $this->selectedSpecs = [];
        $this->minRating = null;
        $this->inStockOnly = false;
        $this->resetPage();
        $this->loadFilterOptions();
    }

    public function removeSpecFilter($specKey, $specValue)
    {
        if (isset($this->selectedSpecs[$specKey])) {
            if (is_array($this->selectedSpecs[$specKey])) {
                $this->selectedSpecs[$specKey] = array_values(array_filter(
                    $this->selectedSpecs[$specKey],
                    fn($value) => $value !== $specValue
                ));

                if (empty($this->selectedSpecs[$specKey])) {
                    unset($this->selectedSpecs[$specKey]);
                }
            } else {
                unset($this->selectedSpecs[$specKey]);
            }
        }

        $this->resetPage();
        $this->loadFilterOptions();
    }

    public function removeBrandFilter($brand)
    {
        $this->selectedBrands = array_values(array_filter($this->selectedBrands, fn($b) => $b !== $brand));
        $this->resetPage();
        $this->loadFilterOptions();
    }

    public function nextPage()
    {
        $this->setPage($this->getPage() + 1);
    }

    public function previousPage()
    {
        $this->setPage($this->getPage() - 1);
    }

    public function addToCart($itemId, $type = 'component')
    {
        if ($type === 'product') {
            $item = Product::find($itemId);
            $stockCheck = $item && $item->in_stock && ($item->stock_quantity ?? 0) > 0;
        } else {
            $item = Component::find($itemId);
            $stockCheck = $item && $item->stock > 0;
        }

        if (!$item || !$stockCheck) {
            session()->flash('error', 'Product is out of stock.');
            return;
        }

        try {
            // Add to cart using the CartService
            $cartService = app(\App\Services\CartService::class);
            $cartService->addToCart($item, 1);

            $this->dispatch('cartUpdated');
            session()->flash('message', 'Product added to cart successfully!');
        } catch (\InvalidArgumentException $e) {
            session()->flash('error', $e->getMessage());
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to add product to cart.');
        }
    }

    /**
     * Clear cache when filters change significantly
     */
    public function clearCache()
    {
        Cache::forget('product_list_categories');

        // Clear filter options cache
        $cacheKey = 'filter_options_' . md5(serialize($this->getSearchParams()));
        Cache::forget($cacheKey);

        // Clear product list cache
        $searchParams = $this->getSearchParams();
        $searchParams['page'] = request()->get('page', 1);
        $searchParams['per_page'] = $this->perPage;
        $cacheKey = 'product_list_' . md5(serialize($searchParams));
        Cache::forget($cacheKey);
    }

    /**
     * Add cache invalidation when updating filters
     */
    public function updatedSelectedCategory()
    {
        $this->resetPage();
        $this->clearCache();
        $this->loadFilterOptions();
    }

    /**
     * Add cache invalidation when updating brands
     */
    public function updatedSelectedBrands()
    {
        $this->resetPage();
        $this->clearCache();
        $this->loadFilterOptions();
    }

    /**
     * Add cache invalidation when updating price filters
     */
    public function updatedPriceMin()
    {
        $this->resetPage();
        $this->clearCache();
        $this->loadFilterOptions();
    }

    public function updatedPriceMax()
    {
        $this->resetPage();
        $this->clearCache();
        $this->loadFilterOptions();
    }

    /**
     * Add cache invalidation when updating specs
     */
    public function updatedSelectedSpecs()
    {
        $this->resetPage();
        $this->clearCache();
        $this->loadFilterOptions();
    }

    /**
     * Add cache invalidation when updating stock filter
     */
    public function updatedInStockOnly()
    {
        $this->resetPage();
        $this->clearCache();
        $this->loadFilterOptions();
    }

    /**
     * Add cache invalidation when updating rating filter
     */
    public function updatedMinRating()
    {
        $this->resetPage();
        $this->clearCache();
    }

    /**
     * Add cache invalidation when updating search
     */
    public function updatedSearch()
    {
        $this->resetPage();
        $this->clearCache();
    }

    /**
     * Add cache invalidation when updating sort
     */
    public function updatedSortBy()
    {
        $this->resetPage();
        $this->clearCache();
    }

    public function updatedSortDirection()
    {
        $this->resetPage();
        $this->clearCache();
    }

    public function updatedPerPage()
    {
        $this->resetPage();
        $this->clearCache();
    }

    // New feature methods

    /**
     * Toggle wishlist for an item
     */
    public function toggleWishlist($itemId, $type = 'component')
    {
        try {
            $key = $type . '_' . $itemId;

            if (in_array($key, $this->wishlistItems)) {
                $this->wishlistItems = array_values(array_filter(
                    $this->wishlistItems,
                    fn($item) => $item !== $key
                ));
                session()->flash('message', 'Removed from wishlist');
            } else {
                $this->wishlistItems[] = $key;
                session()->flash('message', 'Added to wishlist');
            }

            $this->dispatch('wishlistUpdated', count($this->wishlistItems));
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to update wishlist');
        }
    }

    /**
     * Add item to comparison
     */
    public function addToComparison($itemId, $type = 'component')
    {
        try {
            $key = $type . '_' . $itemId;

            if (count($this->comparisonItems) >= 4) {
                session()->flash('error', 'You can only compare up to 4 items');
                return;
            }

            if (in_array($key, $this->comparisonItems)) {
                session()->flash('error', 'Item already in comparison');
                return;
            }

            $this->comparisonItems[] = $key;
            session()->flash('message', 'Added to comparison');
            $this->dispatch('comparisonUpdated', count($this->comparisonItems));
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to add to comparison');
        }
    }

    /**
     * Remove from comparison
     */
    public function removeFromComparison($itemId, $type = 'component')
    {
        $key = $type . '_' . $itemId;
        $this->comparisonItems = array_values(array_filter(
            $this->comparisonItems,
            fn($item) => $item !== $key
        ));
        $this->dispatch('comparisonUpdated', count($this->comparisonItems));
    }

    /**
     * Clear all comparison items
     */
    public function clearComparison()
    {
        $this->comparisonItems = [];
        $this->dispatch('comparisonUpdated', 0);
        session()->flash('message', 'Comparison cleared');
    }

    /**
     * Toggle item selection for bulk actions
     */
    public function toggleItemSelection($itemId, $type = 'component')
    {
        $key = $type . '_' . $itemId;

        if (in_array($key, $this->selectedItems)) {
            $this->selectedItems = array_values(array_filter(
                $this->selectedItems,
                fn($item) => $item !== $key
            ));
        } else {
            $this->selectedItems[] = $key;
        }
    }

    /**
     * Select all visible items
     */
    public function selectAllItems()
    {
        $this->selectedItems = [];
        foreach ($this->components as $component) {
            $type = $component->type ?? 'component';
            $this->selectedItems[] = $type . '_' . $component->id;
        }
    }

    /**
     * Clear all selected items
     */
    public function clearSelectedItems()
    {
        $this->selectedItems = [];
    }

    /**
     * Execute bulk action on selected items
     */
    public function executeBulkAction()
    {
        if (empty($this->selectedItems) || empty($this->bulkAction)) {
            session()->flash('error', 'Please select items and an action');
            return;
        }

        try {
            switch ($this->bulkAction) {
                case 'add_to_cart':
                    $this->bulkAddToCart();
                    break;
                case 'add_to_wishlist':
                    $this->bulkAddToWishlist();
                    break;
                case 'add_to_comparison':
                    $this->bulkAddToComparison();
                    break;
                default:
                    session()->flash('error', 'Invalid bulk action');
            }
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to execute bulk action');
        }
    }

    protected function bulkAddToCart()
    {
        $successCount = 0;
        $failCount = 0;

        foreach ($this->selectedItems as $key) {
            [$type, $id] = explode('_', $key, 2);

            try {
                if ($type === 'product') {
                    $item = Product::find($id);
                    $stockCheck = $item && $item->in_stock && ($item->stock_quantity ?? 0) > 0;
                } else {
                    $item = Component::find($id);
                    $stockCheck = $item && $item->stock > 0;
                }

                if ($item && $stockCheck) {
                    $cartService = app(\App\Services\CartService::class);
                    $cartService->addToCart($item, 1);
                    $successCount++;
                } else {
                    $failCount++;
                }
            } catch (\Exception $e) {
                $failCount++;
            }
        }

        if ($successCount > 0) {
            session()->flash('message', "Added {$successCount} items to cart");
            $this->dispatch('cartUpdated');
        }

        if ($failCount > 0) {
            session()->flash('error', "{$failCount} items could not be added (out of stock or error)");
        }

        $this->selectedItems = [];
        $this->bulkAction = '';
    }

    protected function bulkAddToWishlist()
    {
        $addedCount = 0;

        foreach ($this->selectedItems as $key) {
            if (!in_array($key, $this->wishlistItems)) {
                $this->wishlistItems[] = $key;
                $addedCount++;
            }
        }

        if ($addedCount > 0) {
            session()->flash('message', "Added {$addedCount} items to wishlist");
            $this->dispatch('wishlistUpdated', count($this->wishlistItems));
        }

        $this->selectedItems = [];
        $this->bulkAction = '';
    }

    protected function bulkAddToComparison()
    {
        $addedCount = 0;
        $maxItems = 4 - count($this->comparisonItems);

        foreach (array_slice($this->selectedItems, 0, $maxItems) as $key) {
            if (!in_array($key, $this->comparisonItems)) {
                $this->comparisonItems[] = $key;
                $addedCount++;
            }
        }

        if ($addedCount > 0) {
            session()->flash('message', "Added {$addedCount} items to comparison");
            $this->dispatch('comparisonUpdated', count($this->comparisonItems));
        }

        if (count($this->selectedItems) > $maxItems) {
            session()->flash('error', 'Some items not added - comparison limit is 4 items');
        }

        $this->selectedItems = [];
        $this->bulkAction = '';
    }

    /**
     * Toggle view mode between grid and list
     */
    public function toggleViewMode()
    {
        $this->viewMode = $this->viewMode === 'grid' ? 'list' : 'grid';
    }

    /**
     * Set specific view mode
     */
    public function setViewMode($mode)
    {
        if (in_array($mode, ['grid', 'list'])) {
            $this->viewMode = $mode;
        }
    }

    /**
     * Toggle filters visibility
     */
    public function toggleFilters()
    {
        $this->showFilters = !$this->showFilters;
    }

    /**
     * Toggle advanced search
     */
    public function toggleAdvancedSearch()
    {
        $this->advancedSearch = !$this->advancedSearch;
    }

    /**
     * Quick filter by price range
     */
    public function quickPriceFilter($range)
    {
        switch ($range) {
            case 'under_50':
                $this->priceMin = null;
                $this->priceMax = 50;
                break;
            case '50_100':
                $this->priceMin = 50;
                $this->priceMax = 100;
                break;
            case '100_200':
                $this->priceMin = 100;
                $this->priceMax = 200;
                break;
            case '200_500':
                $this->priceMin = 200;
                $this->priceMax = 500;
                break;
            case 'over_500':
                $this->priceMin = 500;
                $this->priceMax = null;
                break;
        }

        $this->resetPage();
        $this->clearCache();
        $this->loadFilterOptions();
    }

    /**
     * Reset to default sorting
     */
    public function resetSorting()
    {
        $this->sortBy = 'relevance';
        $this->sortDirection = 'desc';
        $this->featuredFirst = false;
        $this->resetPage();
        $this->clearCache();
    }

    /**
     * Toggle featured first
     */
    public function toggleFeaturedFirst()
    {
        $this->featuredFirst = !$this->featuredFirst;
        $this->resetPage();
        $this->clearCache();
    }

    /**
     * Export selected items (placeholder - implement based on your needs)
     */
    public function exportSelected($format = 'csv')
    {
        if (empty($this->selectedItems)) {
            session()->flash('error', 'No items selected for export');
            return;
        }

        // Implementation would depend on your export requirements
        session()->flash('message', 'Export feature coming soon');
    }

    /**
     * Save current search/filter state as preset
     */
    public function saveAsPreset($name)
    {
        if (empty($name)) {
            session()->flash('error', 'Please provide a preset name');
            return;
        }

        // This would typically save to user preferences/database
        $preset = [
            'name' => $name,
            'search' => $this->search,
            'selectedCategory' => $this->selectedCategory,
            'selectedBrands' => $this->selectedBrands,
            'priceMin' => $this->priceMin,
            'priceMax' => $this->priceMax,
            'selectedSpecs' => $this->selectedSpecs,
            'minRating' => $this->minRating,
            'inStockOnly' => $this->inStockOnly,
            'sortBy' => $this->sortBy,
            'sortDirection' => $this->sortDirection
        ];

        // Store in session for now - implement database storage as needed
        $presets = session()->get('search_presets', []);
        $presets[$name] = $preset;
        session()->put('search_presets', $presets);

        session()->flash('message', "Preset '{$name}' saved successfully");
    }

    /**
     * Load a saved preset
     */
    public function loadPreset($name)
    {
        $presets = session()->get('search_presets', []);

        if (!isset($presets[$name])) {
            session()->flash('error', 'Preset not found');
            return;
        }

        $preset = $presets[$name];

        $this->search = $preset['search'] ?? '';
        $this->selectedCategory = $preset['selectedCategory'] ?? null;
        $this->selectedBrands = $preset['selectedBrands'] ?? [];
        $this->priceMin = $preset['priceMin'] ?? null;
        $this->priceMax = $preset['priceMax'] ?? null;
        $this->selectedSpecs = $preset['selectedSpecs'] ?? [];
        $this->minRating = $preset['minRating'] ?? null;
        $this->inStockOnly = $preset['inStockOnly'] ?? false;
        $this->sortBy = $preset['sortBy'] ?? 'relevance';
        $this->sortDirection = $preset['sortDirection'] ?? 'desc';

        $this->resetPage();
        $this->clearCache();
        $this->loadFilterOptions();

        session()->flash('message', "Preset '{$name}' loaded successfully");
    }

    /**
     * Get saved presets
     */
    public function getSavedPresets()
    {
        return array_keys(session()->get('search_presets', []));
    }

    /**
     * Delete a saved preset
     */
    public function deletePreset($name)
    {
        $presets = session()->get('search_presets', []);
        unset($presets[$name]);
        session()->put('search_presets', $presets);

        session()->flash('message', "Preset '{$name}' deleted successfully");
    }

    /**
     * Handle errors gracefully
     */
    protected function handleError(\Exception $e, $context = 'operation')
    {
        Log::error("ProductList {$context} error: " . $e->getMessage(), [
            'trace' => $e->getTraceAsString(),
            'user_id' => auth()->id(),
            'request_data' => request()->all()
        ]);

        $this->errorMessage = "An error occurred during {$context}. Please try again.";
        session()->flash('error', $this->errorMessage);
    }

    /**
     * Check if item is in wishlist
     */
    public function isInWishlist($itemId, $type = 'component')
    {
        $key = $type . '_' . $itemId;
        return in_array($key, $this->wishlistItems);
    }

    /**
     * Check if item is in comparison
     */
    public function isInComparison($itemId, $type = 'component')
    {
        $key = $type . '_' . $itemId;
        return in_array($key, $this->comparisonItems);
    }

    /**
     * Check if item is selected
     */
    public function isSelected($itemId, $type = 'component')
    {
        $key = $type . '_' . $itemId;
        return in_array($key, $this->selectedItems);
    }

    public function render()
    {
        return view('livewire.shop.product-list', [
            'components' => $this->components,
            'savedPresets' => $this->getSavedPresets(),
        ]);
    }
}
