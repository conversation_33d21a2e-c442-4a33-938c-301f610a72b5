<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization should be handled by middleware/policies
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $productId = $this->route('product')?->id ?? $this->product?->id;

        return [
            // Basic product information
            'name' => [
                'required',
                'string',
                'max:255',
                'min:3'
            ],
            'slug' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-z0-9]+(?:-[a-z0-9]+)*$/',
                Rule::unique('products', 'slug')->ignore($productId)
            ],
            'description' => [
                'nullable',
                'string',
                'max:2000'
            ],
            'short_description' => [
                'nullable',
                'string',
                'max:500'
            ],
            'sku' => [
                'required',
                'string',
                'max:100',
                Rule::unique('products', 'sku')->ignore($productId)
            ],
            'category_id' => [
                'required',
                'integer',
                'exists:product_categories,id'
            ],
            'brand' => [
                'required',
                'string',
                'max:100',
                'min:2'
            ],
            'model' => [
                'nullable',
                'string',
                'max:100'
            ],

            // Pricing and inventory
            'price' => [
                'required',
                'numeric',
                'min:0',
                'max:999999.99'
            ],
            'sale_price' => [
                'nullable',
                'numeric',
                'min:0',
                'max:999999.99',
                'lt:price'
            ],
            'stock_quantity' => [
                'required_if:manage_stock,true',
                'nullable',
                'integer',
                'min:0',
                'max:999999'
            ],
            'manage_stock' => [
                'boolean'
            ],
            'in_stock' => [
                'boolean'
            ],
            'low_stock_threshold' => [
                'nullable',
                'integer',
                'min:1',
                'max:1000'
            ],

            // Product status and type
            'status' => [
                'required',
                'string',
                'in:draft,published,archived'
            ],
            'type' => [
                'required',
                'string',
                'in:simple,variable,bundle'
            ],
            'featured' => [
                'boolean'
            ],
            'sort_order' => [
                'nullable',
                'integer',
                'min:0'
            ],

            // Media and SEO
            'images' => [
                'nullable',
                'array'
            ],
            'images.*' => [
                'string',
                'max:500'
            ],
            'seo_title' => [
                'nullable',
                'string',
                'max:70'
            ],
            'seo_description' => [
                'nullable',
                'string',
                'max:160'
            ],
            'tags_json' => [
                'nullable',
                'array'
            ],
            'tags_json.*' => [
                'string',
                'max:50'
            ],

            // Product attributes
            'attributes' => [
                'nullable',
                'array'
            ],
            'attributes.*.name' => [
                'required',
                'string',
                'max:100'
            ],
            'attributes.*.value' => [
                'required',
                'string',
                'max:255'
            ],

            // Physical properties
            'weight' => [
                'nullable',
                'numeric',
                'min:0',
                'max:1000'
            ],
            'dimensions' => [
                'nullable',
                'array'
            ],
            'dimensions.length' => [
                'nullable',
                'numeric',
                'min:0',
                'max:1000'
            ],
            'dimensions.width' => [
                'nullable',
                'numeric',
                'min:0',
                'max:1000'
            ],
            'dimensions.height' => [
                'nullable',
                'numeric',
                'min:0',
                'max:1000'
            ],

            // Shipping information
            'shipping_weight_grams' => [
                'nullable',
                'integer',
                'min:0',
                'max:50000'
            ],
            'shipping_dimensions_json' => [
                'nullable',
                'array'
            ],
            'shipping_dimensions_json.length' => [
                'nullable',
                'numeric',
                'min:0',
                'max:1000'
            ],
            'shipping_dimensions_json.width' => [
                'nullable',
                'numeric',
                'min:0',
                'max:1000'
            ],
            'shipping_dimensions_json.height' => [
                'nullable',
                'numeric',
                'min:0',
                'max:1000'
            ],
            'shipping_cost' => [
                'nullable',
                'numeric',
                'min:0',
                'max:10000'
            ],
            'free_shipping' => [
                'boolean'
            ],

            // Marketplace fields
            'warranty_months' => [
                'nullable',
                'integer',
                'min:0',
                'max:120'
            ],
            'condition' => [
                'nullable',
                'string',
                'in:new,used,refurbished,open_box'
            ],
            'origin_country' => [
                'nullable',
                'string',
                'max:2',
                'regex:/^[A-Z]{2}$/'
            ],
            'min_order_quantity' => [
                'nullable',
                'integer',
                'min:1',
                'max:1000'
            ],
            'max_order_quantity' => [
                'nullable',
                'integer',
                'min:1',
                'max:10000',
                'gte:min_order_quantity'
            ],
            
            // Bulk pricing validation
            'bulk_pricing_json' => [
                'nullable',
                'array'
            ],
            'bulk_pricing_json.*.quantity' => [
                'required',
                'integer',
                'min:2'
            ],
            'bulk_pricing_json.*.price' => [
                'required',
                'numeric',
                'min:0'
            ],
            
            // Product identification
            'manufacturer_part_number' => [
                'nullable',
                'string',
                'max:100'
            ],
            'ean_code' => [
                'nullable',
                'string',
                'regex:/^[0-9]{8,13}$/'
            ],
            
            // Dates
            'release_date' => [
                'nullable',
                'date',
                'before_or_equal:today'
            ],
            'discontinued_at' => [
                'nullable',
                'date',
                'after_or_equal:release_date'
            ],
            
            // Additional metadata
            'meta_data' => [
                'nullable',
                'array'
            ],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Product name is required.',
            'name.min' => 'Product name must be at least 3 characters.',
            'slug.required' => 'Product slug is required.',
            'slug.regex' => 'Slug format is invalid. Use only lowercase letters, numbers, and hyphens.',
            'slug.unique' => 'This slug is already in use by another product.',
            'sku.required' => 'SKU is required.',
            'sku.unique' => 'This SKU is already in use by another product.',
            'category_id.required' => 'Product category is required.',
            'category_id.exists' => 'The selected category does not exist.',
            'brand.required' => 'Brand name is required.',
            'price.required' => 'Price is required.',
            'price.numeric' => 'Price must be a valid number.',
            'price.min' => 'Price cannot be negative.',
            'sale_price.lt' => 'Sale price must be less than the regular price.',
            'stock_quantity.required_if' => 'Stock quantity is required when stock management is enabled.',
            'status.required' => 'Product status is required.',
            'status.in' => 'Invalid product status selected.',
            'type.required' => 'Product type is required.',
            'type.in' => 'Invalid product type selected.',
            'seo_title.max' => 'SEO title should not exceed 70 characters for optimal display in search results.',
            'seo_description.max' => 'SEO description should not exceed 160 characters for optimal display in search results.',
            'bulk_pricing_json.*.quantity.required' => 'Quantity is required for bulk pricing tiers.',
            'bulk_pricing_json.*.quantity.min' => 'Bulk pricing quantity must be at least 2.',
            'bulk_pricing_json.*.price.required' => 'Price is required for bulk pricing tiers.',
            'bulk_pricing_json.*.price.min' => 'Bulk pricing price cannot be negative.',
            'max_order_quantity.gte' => 'Maximum order quantity must be greater than or equal to minimum order quantity.',
            'condition.in' => 'Invalid product condition selected.',
            'origin_country.regex' => 'Country code must be in ISO 2-letter format (e.g., US, GB, IN).',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => 'product name',
            'slug' => 'product slug',
            'description' => 'description',
            'short_description' => 'short description',
            'sku' => 'SKU',
            'category_id' => 'category',
            'brand' => 'brand',
            'model' => 'model',
            'price' => 'price',
            'sale_price' => 'sale price',
            'stock_quantity' => 'stock quantity',
            'manage_stock' => 'stock management',
            'in_stock' => 'stock status',
            'low_stock_threshold' => 'low stock threshold',
            'status' => 'product status',
            'type' => 'product type',
            'featured' => 'featured status',
            'sort_order' => 'sort order',
            'images' => 'product images',
            'seo_title' => 'SEO title',
            'seo_description' => 'SEO description',
            'tags_json' => 'product tags',
            'attributes' => 'product attributes',
            'weight' => 'weight',
            'dimensions' => 'dimensions',
            'shipping_weight_grams' => 'shipping weight',
            'shipping_dimensions_json' => 'shipping dimensions',
            'shipping_cost' => 'shipping cost',
            'free_shipping' => 'free shipping',
            'warranty_months' => 'warranty period',
            'condition' => 'product condition',
            'origin_country' => 'country of origin',
            'min_order_quantity' => 'minimum order quantity',
            'max_order_quantity' => 'maximum order quantity',
            'bulk_pricing_json' => 'bulk pricing',
            'manufacturer_part_number' => 'manufacturer part number',
            'ean_code' => 'EAN code',
            'release_date' => 'release date',
            'discontinued_at' => 'discontinuation date',
            'meta_data' => 'additional metadata',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate bulk pricing tiers are in ascending order by quantity
            if ($this->has('bulk_pricing_json') && is_array($this->bulk_pricing_json)) {
                $bulkPricing = collect($this->bulk_pricing_json);
                
                // Check if quantities are unique
                $quantities = $bulkPricing->pluck('quantity')->toArray();
                if (count($quantities) !== count(array_unique($quantities))) {
                    $validator->errors()->add(
                        'bulk_pricing_json', 
                        'Bulk pricing tiers must have unique quantities.'
                    );
                }
                
                // Check if quantities are in ascending order
                $sortedQuantities = $quantities;
                sort($sortedQuantities);
                if ($quantities !== $sortedQuantities) {
                    $validator->errors()->add(
                        'bulk_pricing_json', 
                        'Bulk pricing tiers must be in ascending order by quantity.'
                    );
                }
                
                // Check if prices are decreasing as quantity increases
                $previousPrice = null;
                $previousQuantity = null;
                
                foreach ($bulkPricing->sortBy('quantity') as $tier) {
                    if ($previousPrice !== null && $previousQuantity !== null) {
                        $currentUnitPrice = $tier['price'] / $tier['quantity'];
                        $previousUnitPrice = $previousPrice / $previousQuantity;
                        
                        if ($currentUnitPrice >= $previousUnitPrice) {
                            $validator->errors()->add(
                                'bulk_pricing_json', 
                                'Bulk pricing tiers must offer better per-unit prices as quantity increases.'
                            );
                            break;
                        }
                    }
                    
                    $previousPrice = $tier['price'];
                    $previousQuantity = $tier['quantity'];
                }
            }
        });
    }
}