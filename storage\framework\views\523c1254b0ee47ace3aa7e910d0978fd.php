<?php $__env->startSection('title', 'Comment Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="container px-6 mx-auto grid">
    <div class="flex justify-between items-center">
        <h2 class="my-6 text-2xl font-semibold text-text-primary-light dark:text-text-primary-dark">
            Comment Details
        </h2>
        <a href="<?php echo e(route('admin.comments.index')); ?>" class="px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-primary-light dark:bg-primary-dark border border-transparent rounded-lg hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-offset-2 focus:ring-offset-bg-light dark:focus:ring-offset-bg-dark">
            Back to Comments
        </a>
    </div>

    <!-- Comment Details -->
    <div class="px-4 py-3 mb-8 bg-bg-light dark:bg-bg-dark rounded-lg shadow-md border border-border-light dark:border-border-dark">
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-2">Comment Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark mb-1">Status:</p>
                    <p class="font-medium">
                        <?php if($comment->is_approved): ?>
                            <span class="px-2 py-1 font-semibold leading-tight text-green-800 dark:text-green-200 bg-green-100 dark:bg-green-800 rounded-full">
                                Approved
                            </span>
                        <?php elseif($comment->rejected_reason): ?>
                            <span class="px-2 py-1 font-semibold leading-tight text-red-800 dark:text-red-200 bg-red-100 dark:bg-red-800 rounded-full">
                                Rejected
                            </span>
                        <?php else: ?>
                            <span class="px-2 py-1 font-semibold leading-tight text-orange-800 dark:text-orange-200 bg-orange-100 dark:bg-orange-800 rounded-full">
                                Pending
                            </span>
                        <?php endif; ?>
                    </p>
                </div>
                <div>
                    <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark mb-1">Date Posted:</p>
                    <p class="font-medium text-text-primary-light dark:text-text-primary-dark"><?php echo e($comment->created_at->format('F d, Y h:i A')); ?></p>
                </div>
                <?php if($comment->moderated_at): ?>
                <div>
                    <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark mb-1">Moderated By:</p>
                    <p class="font-medium text-text-primary-light dark:text-text-primary-dark"><?php echo e($comment->moderator->name ?? 'Unknown'); ?> (<?php echo e($comment->moderated_at->format('F d, Y h:i A')); ?>)</p>
                </div>
                <?php endif; ?>
                <?php if($comment->rejected_reason): ?>
                <div>
                    <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark mb-1">Rejection Reason:</p>
                    <p class="font-medium text-red-600 dark:text-red-400"><?php echo e($comment->rejected_reason); ?></p>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="mb-6">
            <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-2">Author Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark mb-1">Name:</p>
                    <p class="font-medium text-text-primary-light dark:text-text-primary-dark"><?php echo e($comment->author->name ?? 'Unknown'); ?></p>
                </div>
                <div>
                    <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark mb-1">Email:</p>
                    <p class="font-medium text-text-primary-light dark:text-text-primary-dark"><?php echo e($comment->author->email ?? 'Unknown'); ?></p>
                </div>
            </div>
        </div>

        <div class="mb-6">
            <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-2">Blog Post</h3>
            <div>
                <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark mb-1">Title:</p>
                <p class="font-medium">
                    <a href="<?php echo e(route('admin.blog.posts.edit', $comment->blogPost->id)); ?>" class="text-primary-light dark:text-primary-dark hover:text-opacity-80 transition-colors duration-200">
                        <?php echo e($comment->blogPost->title); ?>

                    </a>
                </p>
            </div>
        </div>

        <?php if($comment->parent): ?>
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-2">Parent Comment</h3>
            <div class="p-4 bg-gray-50 dark:bg-slate-800 rounded-lg border border-border-light dark:border-border-dark">
                <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark mb-1">Author:</p>
                <p class="font-medium text-text-primary-light dark:text-text-primary-dark mb-2"><?php echo e($comment->parent->author->name ?? 'Unknown'); ?></p>
                <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark mb-1">Content:</p>
                <p class="font-medium text-text-primary-light dark:text-text-primary-dark"><?php echo e($comment->parent->content); ?></p>
            </div>
        </div>
        <?php endif; ?>

        <div class="mb-6">
            <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-2">Comment Content</h3>
            <div class="p-4 bg-gray-50 dark:bg-slate-800 rounded-lg border border-border-light dark:border-border-dark">
                <p class="text-text-primary-light dark:text-text-primary-dark leading-relaxed"><?php echo e($comment->content); ?></p>
            </div>
        </div>

        <?php if($comment->replies->count() > 0): ?>
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-2">Replies (<?php echo e($comment->replies->count()); ?>)</h3>
            <div class="space-y-4">
                <?php $__currentLoopData = $comment->replies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reply): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="p-4 bg-gray-50 dark:bg-slate-800 rounded-lg border border-border-light dark:border-border-dark">
                    <div class="flex justify-between items-start mb-2">
                        <div>
                            <p class="font-medium text-text-primary-light dark:text-text-primary-dark"><?php echo e($reply->author->name ?? 'Unknown'); ?></p>
                            <p class="text-xs text-text-secondary-light dark:text-text-secondary-dark"><?php echo e($reply->created_at->format('F d, Y h:i A')); ?></p>
                        </div>
                        <div>
                            <?php if($reply->is_approved): ?>
                                <span class="px-2 py-1 text-xs font-semibold leading-tight text-green-800 dark:text-green-200 bg-green-100 dark:bg-green-800 rounded-full">
                                    Approved
                                </span>
                            <?php elseif($reply->rejected_reason): ?>
                                <span class="px-2 py-1 text-xs font-semibold leading-tight text-red-800 dark:text-red-200 bg-red-100 dark:bg-red-800 rounded-full">
                                    Rejected
                                </span>
                            <?php else: ?>
                                <span class="px-2 py-1 text-xs font-semibold leading-tight text-orange-800 dark:text-orange-200 bg-orange-100 dark:bg-orange-800 rounded-full">
                                    Pending
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <p class="text-text-primary-light dark:text-text-primary-dark"><?php echo e($reply->content); ?></p>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Moderation Actions -->
        <div class="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
            <?php if(!$comment->is_approved && !$comment->rejected_reason): ?>
            <form action="<?php echo e(route('admin.comments.approve', $comment->id)); ?>" method="POST" class="w-full md:w-auto">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PATCH'); ?>
                <button type="submit" class="w-full px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-green-600 dark:bg-green-700 border border-transparent rounded-lg hover:bg-green-700 dark:hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-bg-light dark:focus:ring-offset-bg-dark">
                    Approve Comment
                </button>
            </form>
            
            <button type="button" class="w-full md:w-auto px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-red-600 dark:bg-red-700 border border-transparent rounded-lg hover:bg-red-700 dark:hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-bg-light dark:focus:ring-offset-bg-dark" onclick="document.getElementById('reject-modal').classList.remove('hidden')">
                Reject Comment
            </button>
            <?php endif; ?>
            
            <form action="<?php echo e(route('admin.comments.destroy', $comment->id)); ?>" method="POST" class="w-full md:w-auto" onsubmit="return confirm('Are you sure you want to delete this comment?');">
                <?php echo csrf_field(); ?>
                <?php echo method_field('DELETE'); ?>
                <button type="submit" class="w-full px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-gray-600 dark:bg-gray-700 border border-transparent rounded-lg hover:bg-gray-700 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-bg-light dark:focus:ring-offset-bg-dark">
                    Delete Comment
                </button>
            </form>
        </div>
    </div>
</div>

<!-- Reject Modal -->
<div id="reject-modal" class="fixed inset-0 z-30 flex items-center justify-center hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="fixed inset-0 bg-black bg-opacity-50 dark:bg-opacity-70 transition-opacity"></div>

    <div class="relative bg-bg-light dark:bg-bg-dark rounded-lg max-w-md w-full mx-auto p-6 shadow-xl transform transition-all border border-border-light dark:border-border-dark">
        <div class="absolute top-0 right-0 pt-4 pr-4">
            <button type="button" class="text-text-secondary-light dark:text-text-secondary-dark hover:text-text-primary-light dark:hover:text-text-primary-dark transition-colors duration-200" onclick="document.getElementById('reject-modal').classList.add('hidden')">
                <span class="sr-only">Close</span>
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <div class="mt-3 text-center sm:mt-0 sm:text-left">
            <h3 class="text-lg leading-6 font-medium text-text-primary-light dark:text-text-primary-dark" id="modal-title">Reject Comment</h3>
            <div class="mt-4">
                <form action="<?php echo e(route('admin.comments.reject', $comment->id)); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PATCH'); ?>
                    <div class="mb-4">
                        <label for="rejected_reason" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">Reason for Rejection</label>
                        <textarea id="rejected_reason" name="rejected_reason" rows="3" class="shadow-sm bg-bg-light dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark placeholder-text-secondary-light dark:placeholder-text-secondary-dark focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark block w-full sm:text-sm border border-border-light dark:border-border-dark rounded-md" placeholder="Provide a reason for rejecting this comment" required></textarea>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" class="px-4 py-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark bg-bg-light dark:bg-bg-dark border border-border-light dark:border-border-dark rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-offset-bg-light dark:focus:ring-offset-bg-dark transition-colors duration-200" onclick="document.getElementById('reject-modal').classList.add('hidden')">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-red-600 dark:bg-red-700 border border-transparent rounded-md shadow-sm hover:bg-red-700 dark:hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 focus:ring-offset-bg-light dark:focus:ring-offset-bg-dark transition-colors duration-200">
                            Reject
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\lara\www\pc-builder\resources\views/admin/comments/show.blade.php ENDPATH**/ ?>