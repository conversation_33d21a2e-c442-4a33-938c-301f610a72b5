<?php $__env->startSection('title', 'Blog Posts'); ?>

<?php $__env->startSection('content'); ?>
<div class="px-4 w-full max-w-7xl mx-auto">
    <div class="mb-6">
        <h1 class="text-3xl font-bold text-text-primary-light dark:text-text-primary-dark">Blog Posts</h1>
        <ol class="flex flex-wrap list-none rounded mb-4 bg-transparent overflow-x-auto whitespace-nowrap">
            <li class="text-sm leading-normal text-text-secondary-light dark:text-text-secondary-dark">
                <a href="<?php echo e(route('admin.dashboard')); ?>" class="text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light transition-colors">Dashboard</a>
            </li>
            <li class="text-sm text-text-secondary-light dark:text-text-secondary-dark mx-2">/</li>
            <li class="text-sm font-semibold text-text-primary-light dark:text-text-primary-dark">Blog Posts</li>
        </ol>
    </div>

    <div class="bg-white dark:bg-bg-dark rounded-xl shadow-lg mb-6 overflow-hidden border border-border-light dark:border-border-dark">
        <div class="flex flex-col sm:flex-row justify-between items-center px-6 py-4 border-b border-border-light dark:border-border-dark bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
            <div class="flex items-center mb-3 sm:mb-0 w-full sm:w-auto justify-center sm:justify-start">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-3 text-primary-light dark:text-primary-dark" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                </svg>
                <span class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">Blog Posts</span>
            </div>
            <a href="<?php echo e(route('admin.blog.posts.create')); ?>" class="bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light text-white font-medium py-2.5 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-offset-2 dark:focus:ring-offset-bg-dark flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Add New Post
            </a>
        </div>
        <div class="p-6">
            <!-- Filters -->
            <div class="mb-6">
                <form action="<?php echo e(route('admin.blog.posts.index')); ?>" method="GET">
                    <div class="grid grid-cols-1 gap-4">
                        <!-- Search bar -->
                        <div class="flex">
                            <input type="text" placeholder="Search posts..." name="search" value="<?php echo e(request('search')); ?>" 
                                class="w-full rounded-l-lg border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors">
                            <button type="submit" class="bg-gray-100 dark:bg-gray-800 border border-border-light dark:border-border-dark border-l-0 px-4 rounded-r-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-text-secondary-light dark:text-text-secondary-dark" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                            </button>
                        </div>
                        
                        <!-- Filter controls in a responsive grid -->
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                            <select name="category" class="rounded-lg border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors">
                                <option value="">All Categories</option>
                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($category->id); ?>" <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                                        <?php echo e($category->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            
                            <select name="status" class="rounded-lg border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors">
                                <option value="">All Status</option>
                                <option value="published" <?php echo e(request('status') == 'published' ? 'selected' : ''); ?>>Published</option>
                                <option value="draft" <?php echo e(request('status') == 'draft' ? 'selected' : ''); ?>>Draft</option>
                            </select>
                            
                            <select name="sort" class="rounded-lg border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors">
                                <option value="latest" <?php echo e(request('sort') == 'latest' ? 'selected' : ''); ?>>Latest First</option>
                                <option value="oldest" <?php echo e(request('sort') == 'oldest' ? 'selected' : ''); ?>>Oldest First</option>
                                <option value="title_asc" <?php echo e(request('sort') == 'title_asc' ? 'selected' : ''); ?>>Title A-Z</option>
                                <option value="title_desc" <?php echo e(request('sort') == 'title_desc' ? 'selected' : ''); ?>>Title Z-A</option>
                            </select>

                            <button type="submit" class="bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light text-white font-medium py-2.5 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-offset-2 dark:focus:ring-offset-bg-dark">
                                Apply Filters
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Responsive Table -->
            <div class="overflow-x-auto -mx-6">
                <div class="inline-block min-w-full py-2 align-middle px-6">
                    <div class="overflow-hidden border border-border-light dark:border-border-dark rounded-lg">
                        <table class="min-w-full divide-y divide-border-light dark:divide-border-dark hidden md:table">
                            <thead class="bg-gray-50 dark:bg-gray-800">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">ID</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Title</th>
                                    <th scope="col" class="hidden sm:table-cell px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Category</th>
                                    <th scope="col" class="hidden md:table-cell px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Author</th>
                                    <th scope="col" class="hidden sm:table-cell px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Status</th>
                                    <th scope="col" class="hidden md:table-cell px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Published</th>
                                    <th scope="col" class="hidden lg:table-cell px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Views</th>
                                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-bg-dark divide-y divide-border-light dark:divide-border-dark">
                                <?php $__empty_1 = true; $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark"><?php echo e($post->id); ?></td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <?php if($post->featured_image): ?>
                                                    <img src="<?php echo e(uploads_url($post->featured_image)); ?>" alt="<?php echo e($post->title); ?>" class="h-10 w-10 rounded-lg object-cover mr-3 shadow-sm">
                                                <?php else: ?>
                                                    <div class="h-10 w-10 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center mr-3">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                        </svg>
                                                    </div>
                                                <?php endif; ?>
                                                <div>
                                                    <div class="text-sm font-medium text-text-primary-light dark:text-text-primary-dark"><?php echo e(Str::limit($post->title, 20)); ?></div>
                                                    <div class="hidden sm:block text-sm text-text-secondary-light dark:text-text-secondary-dark"><?php echo e(Str::limit($post->excerpt, 30)); ?></div>
                                                    <!-- Mobile-only category and status badges -->
                                                    <div class="sm:hidden flex flex-wrap gap-1 mt-1">
                                                        <span class="px-2 py-0.5 inline-flex text-xs leading-4 font-semibold rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                                                            <?php echo e($post->category->name ?? 'Uncategorized'); ?>

                                                        </span>
                                                        <?php if($post->is_published): ?>
                                                            <span class="px-2 py-0.5 inline-flex text-xs leading-4 font-semibold rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400">
                                                                Published
                                                            </span>
                                                        <?php else: ?>
                                                            <span class="px-2 py-0.5 inline-flex text-xs leading-4 font-semibold rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400">
                                                                Draft
                                                            </span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="hidden sm:table-cell px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark"><?php echo e($post->category->name ?? 'Uncategorized'); ?></td>
                                        <td class="hidden md:table-cell px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark"><?php echo e($post->author->name); ?></td>
                                        <td class="hidden sm:table-cell px-6 py-4 whitespace-nowrap">
                                            <?php if($post->is_published): ?>
                                                <span class="px-2.5 py-0.5 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400">
                                                    Published
                                                </span>
                                            <?php else: ?>
                                                <span class="px-2.5 py-0.5 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400">
                                                    Draft
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="hidden md:table-cell px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                            <?php echo e($post->published_at ? $post->published_at->format('M d, Y') : 'Not published'); ?>

                                        </td>
                                        <td class="hidden lg:table-cell px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                            <?php echo e($post->views_count); ?>

                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex justify-center space-x-3">
                                                <a href="<?php echo e(route('admin.blog.posts.edit', $post->id)); ?>" class="text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light transition-colors">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                    </svg>
                                                </a>
                                                <a href="<?php echo e(route('blog.show', $post->slug)); ?>" target="_blank" class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 transition-colors">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                    </svg>
                                                </a>
                                                <button type="button" onclick="document.getElementById('deleteModal<?php echo e($post->id); ?>').classList.remove('hidden')" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 transition-colors">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                    </svg>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- Delete Modal -->
                                    <div id="deleteModal<?php echo e($post->id); ?>" class="fixed inset-0 bg-gray-500 bg-opacity-75 dark:bg-opacity-90 flex items-center justify-center z-50 hidden p-4">
                                        <div class="bg-white dark:bg-bg-dark rounded-xl overflow-hidden shadow-xl max-w-md w-full border border-border-light dark:border-border-dark">
                                            <div class="px-6 py-4 border-b border-border-light dark:border-border-dark bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20">
                                                <div class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark">Confirm Delete</div>
                                            </div>
                                            <div class="p-6">
                                                <p class="text-base text-text-secondary-light dark:text-text-secondary-dark">Are you sure you want to delete "<?php echo e(Str::limit($post->title, 40)); ?>"? This action cannot be undone.</p>
                                            </div>
                                            <div class="px-6 py-4 bg-gray-50 dark:bg-gray-800 flex justify-end space-x-3">
                                                <button type="button" onclick="document.getElementById('deleteModal<?php echo e($post->id); ?>').classList.add('hidden')" class="bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-text-primary-light dark:text-text-primary-dark font-medium py-2 px-4 border border-border-light dark:border-border-dark rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-400 focus:ring-offset-2 dark:focus:ring-offset-bg-dark">
                                                    Cancel
                                                </button>
                                                <form action="<?php echo e(route('admin.blog.posts.destroy', $post->id)); ?>" method="POST" class="inline">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="bg-red-600 dark:bg-red-500 hover:bg-red-700 dark:hover:bg-red-600 text-white font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 dark:focus:ring-red-400 focus:ring-offset-2 dark:focus:ring-offset-bg-dark">
                                                        Delete
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="8" class="px-6 py-10 text-center">
                                            <div class="flex flex-col items-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                                                </svg>
                                                <h3 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark mb-1">No posts found</h3>
                                                <p class="text-text-secondary-light dark:text-text-secondary-dark mb-4">Try adjusting your search or filter criteria</p>
                                                <a href="<?php echo e(route('admin.blog.posts.create')); ?>" class="bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light text-white py-2.5 px-4 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-offset-2 dark:focus:ring-offset-bg-dark flex items-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                                    </svg>
                                                    Create New Post
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                        <!-- Mobile Card View -->
                        <div class="md:hidden">
                            <?php $__empty_1 = true; $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <div class="mb-4 p-4 bg-white dark:bg-bg-dark rounded-lg shadow border border-border-light dark:border-border-dark">
                                    <div class="flex items-center mb-2">
                                        <?php if($post->featured_image): ?>
                                            <img src="<?php echo e(uploads_url($post->featured_image)); ?>" alt="<?php echo e($post->title); ?>" class="h-12 w-12 rounded-lg object-cover mr-3 shadow-sm">
                                        <?php else: ?>
                                            <div class="h-12 w-12 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center mr-3">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                </svg>
                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <div class="text-base font-medium text-text-primary-light dark:text-text-primary-dark"><?php echo e($post->title); ?></div>
                                            <div class="text-xs text-text-secondary-light dark:text-text-secondary-dark mb-1"><?php echo e(Str::limit($post->excerpt, 40)); ?></div>
                                            <div class="flex flex-wrap gap-1 mt-1">
                                                <span class="px-2 py-0.5 inline-flex text-xs leading-4 font-semibold rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                                                    <?php echo e($post->category->name ?? 'Uncategorized'); ?>

                                                </span>
                                                <?php if($post->is_published): ?>
                                                    <span class="px-2 py-0.5 inline-flex text-xs leading-4 font-semibold rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400">
                                                        Published
                                                    </span>
                                                <?php else: ?>
                                                    <span class="px-2 py-0.5 inline-flex text-xs leading-4 font-semibold rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400">
                                                        Draft
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex flex-wrap gap-2 text-xs text-text-secondary-light dark:text-text-secondary-dark mb-2">
                                        <span><span class="font-medium">Author:</span> <?php echo e($post->author->name); ?></span>
                                        <span><span class="font-medium">Published:</span> <?php echo e($post->published_at ? $post->published_at->format('M d, Y') : 'Not published'); ?></span>
                                        <span><span class="font-medium">Views:</span> <?php echo e($post->views_count); ?></span>
                                    </div>
                                    <div class="flex space-x-3 mt-2">
                                        <a href="<?php echo e(route('admin.blog.posts.edit', $post->id)); ?>" class="text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light transition-colors">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                            </svg>
                                        </a>
                                        <a href="<?php echo e(route('blog.show', $post->slug)); ?>" target="_blank" class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 transition-colors">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                            </svg>
                                        </a>
                                        <button type="button" onclick="document.getElementById('deleteModal<?php echo e($post->id); ?>').classList.remove('hidden')" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 transition-colors">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <div class="p-4 text-center text-text-secondary-light dark:text-text-secondary-dark">No posts found.</div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            <?php if($posts->hasPages()): ?>
                <div class="mt-6">
                    <?php echo e($posts->links()); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    // Highlight search results
    document.addEventListener('DOMContentLoaded', function() {
        let searchTerm = '<?php echo e(request('search')); ?>';
        if (searchTerm) {
            let regex = new RegExp('(' + searchTerm + ')', 'gi');
            document.querySelectorAll('td').forEach(function(el) {
                let text = el.textContent;
                if (text.match(regex)) {
                    el.innerHTML = text.replace(regex, '<mark class="bg-accent-light dark:bg-accent-dark px-1 rounded">$1</mark>');
                }
            });
        }
        
        // Close modals when clicking outside
        window.addEventListener('click', function(e) {
            document.querySelectorAll('[id^="deleteModal"]').forEach(function(modal) {
                if (e.target === modal) {
                    modal.classList.add('hidden');
                }
            });
        });
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\lara\www\pc-builder\resources\views/admin/blog/posts/blog-index.blade.php ENDPATH**/ ?>