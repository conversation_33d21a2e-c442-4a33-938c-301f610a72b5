<?php

namespace App\Http\Controllers;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Services\CompatibilityService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class ComponentController extends Controller
{
    protected CompatibilityService $compatibilityService;

    public function __construct(CompatibilityService $compatibilityService)
    {
        $this->compatibilityService = $compatibilityService;
    }

    /**
     * Display a listing of the components.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request): View
    {   
        $query = Component::active();

        // Apply filters using Purchasable interface methods
        if ($request->filled('category')) {
            $query->whereHas('category', function ($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        if ($request->filled('brand')) {
            $query->where('brand', $request->brand);
        }

        if ($request->filled('socket_type')) {
            $query->where('socket_type', $request->socket_type);
        }

        if ($request->filled('form_factor')) {
            $query->where('form_factor', $request->form_factor);
        }

        if ($request->filled('price_min')) {
            $query->where('price', '>=', $request->price_min);
        }

        if ($request->filled('price_max')) {
            $query->where('price', '<=', $request->price_max);
        }

        if ($request->filled('in_stock')) {
            $query->where('stock', '>', 0);
        }

        // Apply search using interface methods
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('brand', 'like', "%{$search}%")
                  ->orWhere('model', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortBy = $request->get('sort', 'name');
        $sortDirection = $request->get('direction', 'asc');

        switch ($sortBy) {
            case 'price':
                $query->orderBy('price', $sortDirection);
                break;
            case 'brand':
                $query->orderBy('brand', $sortDirection)
                      ->orderBy('name', $sortDirection);
                break;
            case 'stock':
                $query->orderBy('stock', $sortDirection);
                break;
            default:
                $query->orderBy('name', $sortDirection);
        }

        $components = $query->with(['category'])->paginate(12);

        // Get filter options
        $categories = ComponentCategory::orderBy('name')->get();
        $brands = Component::active()->distinct()->pluck('brand')->filter()->sort();
        $socketTypes = Component::active()->whereNotNull('socket_type')->distinct()->pluck('socket_type')->filter()->sort();
        $formFactors = Component::active()->whereNotNull('form_factor')->distinct()->pluck('form_factor')->filter()->sort();

        return view('shop.components.index', compact('components', 'categories', 'brands', 'socketTypes', 'formFactors'));
    }
    
    /**
     * Display the specified component by ID.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function showById($id): View
    {   
        $component = Component::with(['category'])->findOrFail($id);
        
        // Get compatible components for suggestions
        $compatibleComponents = $this->getCompatibleComponentsForDisplay($component);
        
        return view('shop.components.show', compact('component', 'compatibleComponents'));
    }

    /**
     * Display the specified component by slug.
     *
     * @param  string  $slug
     * @return \Illuminate\View\View
     */
    public function show($slug): View
    {   
        $component = Component::where('slug', $slug)
            ->with(['category'])
            ->firstOrFail();
            
        // Get compatible components for suggestions
        $compatibleComponents = $this->getCompatibleComponentsForDisplay($component);
        
        // Get related components in same category
        $relatedComponents = Component::active()
            ->where('id', '!=', $component->id)
            ->where('category_id', $component->category_id)
            ->limit(4)
            ->get();

        return view('shop.components.show', compact('component', 'compatibleComponents', 'relatedComponents'));
    }

    /**
     * Display components by category.
     *
     * @param  string  $slug
     * @return \Illuminate\View\View
     */
    public function category($slug): View
    {   
        $category = ComponentCategory::where('slug', $slug)->firstOrFail();
        
        $components = Component::active()
            ->where('category_id', $category->id)
            ->with(['category'])
            ->orderBy('name')
            ->paginate(12);
            
        return view('shop.components.category', compact('category', 'components'));
    }

    /**
     * Check compatibility between components.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function checkCompatibility(Request $request): JsonResponse
    {
        $request->validate([
            'component_ids' => 'required|array|min:2',
            'component_ids.*' => 'required|integer|exists:components,id'
        ]);

        $components = Component::whereIn('id', $request->component_ids)->get();
        
        if ($components->count() !== count($request->component_ids)) {
            return response()->json([
                'error' => 'One or more components not found'
            ], 404);
        }

        $result = $this->compatibilityService->checkCompatibility($components->toArray());

        return response()->json([
            'compatible' => $result->isCompatible(),
            'issues' => $result->getIssues(),
            'warnings' => $result->getWarnings(),
            'components' => $components->map(function ($component) {
                return [
                    'id' => $component->id,
                    'name' => $component->getName(),
                    'category' => $component->getCategory(),
                    'brand' => $component->getBrand(),
                ];
            })
        ]);
    }

    /**
     * Get compatible components for a specific component and target category.
     *
     * @param Request $request
     * @param int $componentId
     * @return JsonResponse
     */
    public function getCompatibleComponents(Request $request, int $componentId): JsonResponse
    {
        $request->validate([
            'target_category' => 'required|string|exists:component_categories,slug'
        ]);

        $component = Component::findOrFail($componentId);
        $compatibleComponents = $this->compatibilityService->getCompatibleComponents(
            $component, 
            $request->target_category
        );

        return response()->json([
            'base_component' => [
                'id' => $component->id,
                'name' => $component->getName(),
                'category' => $component->getCategory(),
            ],
            'target_category' => $request->target_category,
            'compatible_components' => $compatibleComponents->map(function ($comp) {
                return [
                    'id' => $comp->id,
                    'name' => $comp->getName(),
                    'price' => $comp->getPrice(),
                    'brand' => $comp->getBrand(),
                    'model' => $comp->getModel(),
                    'image' => $comp->getImage(),
                    'stock' => $comp->getStock(),
                    'available' => $comp->isAvailable(),
                    'url' => $comp->getUrl(),
                ];
            })->values()
        ]);
    }

    /**
     * Get component specifications and technical details.
     *
     * @param int $componentId
     * @return JsonResponse
     */
    public function getSpecifications(int $componentId): JsonResponse
    {
        $component = Component::findOrFail($componentId);

        return response()->json([
            'id' => $component->id,
            'name' => $component->getName(),
            'brand' => $component->getBrand(),
            'model' => $component->getModel(),
            'category' => $component->getCategory(),
            'price' => $component->getPrice(),
            'stock' => $component->getStock(),
            'available' => $component->isAvailable(),
            'specifications' => [
                'socket_type' => $component->getSocketType(),
                'form_factor' => $component->getFormFactor(),
                'power_consumption' => $component->getPowerRequirement(),
                'chipset' => $component->chipset,
                'cooling_type' => $component->cooling_type,
                'memory_type' => $component->memory_type,
                'interface_type' => $component->interface_type,
                'warranty_months' => $component->warranty_months,
                'weight_grams' => $component->weight_grams,
                'dimensions' => $component->dimensions_json,
                'specs' => $component->specs,
            ],
            'images' => $component->getImages(),
            'url' => $component->getUrl(),
        ]);
    }

    /**
     * Get compatible components for display purposes.
     *
     * @param Component $component
     * @return array
     */
    protected function getCompatibleComponentsForDisplay(Component $component): array
    {
        $compatibleComponents = [];
        
        // Get common categories to check compatibility with
        $commonCategories = ['motherboard', 'cpu', 'memory-ram', 'graphics-card', 'power-supply'];
        
        foreach ($commonCategories as $categorySlug) {
            if ($component->category->slug !== $categorySlug) {
                $compatible = $this->compatibilityService->getCompatibleComponents($component, $categorySlug);
                if ($compatible->isNotEmpty()) {
                    $compatibleComponents[$categorySlug] = $compatible->take(3)->map(function ($comp) {
                        return [
                            'id' => $comp->id,
                            'name' => $comp->getName(),
                            'price' => $comp->getPrice(),
                            'brand' => $comp->getBrand(),
                            'image' => $comp->getImage(),
                            'url' => $comp->getUrl(),
                        ];
                    });
                }
            }
        }
        
        return $compatibleComponents;
    }
}