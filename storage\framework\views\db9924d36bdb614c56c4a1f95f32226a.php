<div
    class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 transition-colors duration-300">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-6">
        <!--[if BLOCK]><![endif]--><?php if($orderComplete): ?>
            <!-- Order Complete -->
            <div class="max-w-2xl mx-auto">
                <div
                    class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl border border-green-200/50 dark:border-green-400/20 rounded-2xl p-8 text-center shadow-2xl shadow-green-500/10 dark:shadow-green-400/10">
                    <div
                        class="w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center shadow-lg animate-pulse">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M5 13l4 4L19 7">
                            </path>
                        </svg>
                    </div>
                    <h2
                        class="text-3xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 dark:from-green-300 dark:to-emerald-300 bg-clip-text text-transparent mb-3">
                        Order Complete!</h2>
                    <p class="text-gray-600 dark:text-gray-300 mb-6 text-lg">Thank you for your purchase. Your order has
                        been successfully placed.</p>

                    <!--[if BLOCK]><![endif]--><?php if($completedOrder): ?>
                        <div
                            class="bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm border border-gray-200/50 dark:border-gray-600/50 rounded-xl p-6 mb-6">
                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                                Order Number</p>
                            <p class="text-xl font-bold text-gray-900 dark:text-white mt-1">
                                <?php echo e($completedOrder->order_number); ?></p>
                        </div>

                        <div class="space-y-3 text-gray-600 dark:text-gray-300 mb-8">
                            <p class="text-lg font-semibold">Total: <span
                                    class="text-gray-900 dark:text-white">$<?php echo e(number_format($completedOrder->total, 2)); ?></span>
                            </p>
                            <p>A confirmation email has been sent to <span
                                    class="font-medium text-gray-900 dark:text-white"><?php echo e($completedOrder->billing_email); ?></span>
                            </p>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="<?php echo e(route('shop.index')); ?>"
                            class="group inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl">
                            <svg class="w-5 h-5 mr-2 group-hover:translate-x-0.5 transition-transform" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                            </svg>
                            Continue Shopping
                        </a>
                        <!--[if BLOCK]><![endif]--><?php if(auth()->guard()->check()): ?>
                            <a href="<?php echo e(route('orders.index')); ?>"
                                class="group inline-flex items-center justify-center px-6 py-3 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                <svg class="w-5 h-5 mr-2 group-hover:translate-x-0.5 transition-transform" fill="none"
                                    stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                    </path>
                                </svg>
                                View Orders
                            </a>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- Checkout Process -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Checkout Form -->
                <div class="lg:col-span-2">
                    <!-- Progress Steps -->
                    <div
                        class="mb-8 bg-white/60 dark:bg-gray-800/60 backdrop-blur-xl rounded-2xl p-6 border border-gray-200/50 dark:border-gray-700/50 shadow-xl">
                        <div class="flex items-center justify-between">
                            <!--[if BLOCK]><![endif]--><?php for($i = 1; $i <= $maxSteps; $i++): ?>
                                <div class="flex items-center <?php echo e($i < $maxSteps ? 'flex-1' : ''); ?>">
                                    <div class="relative">
                                        <div
                                            class="flex items-center justify-center w-10 h-10 rounded-full transition-all duration-300 <?php echo e($currentStep >= $i ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg scale-110' : 'bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300'); ?>">
                                            <!--[if BLOCK]><![endif]--><?php if($currentStep > $i): ?>
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2.5" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                            <?php else: ?>
                                                <?php echo e($i); ?>

                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                        <!--[if BLOCK]><![endif]--><?php if($currentStep === $i): ?>
                                            <div
                                                class="absolute -inset-1 bg-blue-400 rounded-full animate-ping opacity-75">
                                            </div>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                    <div class="ml-3 hidden sm:block">
                                        <div
                                            class="text-sm font-medium transition-colors duration-200 <?php echo e($currentStep >= $i ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'); ?>">
                                            <!--[if BLOCK]><![endif]--><?php switch($i):
                                                case (1): ?>
                                                    Shipping
                                                <?php break; ?>

                                                <?php case (2): ?>
                                                    Billing
                                                <?php break; ?>

                                                <?php case (3): ?>
                                                    Delivery
                                                <?php break; ?>

                                                <?php case (4): ?>
                                                    Payment
                                                <?php break; ?>
                                            <?php endswitch; ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                    </div>
                                    <!--[if BLOCK]><![endif]--><?php if($i < $maxSteps): ?>
                                        <div
                                            class="flex-1 h-0.5 mx-4 transition-colors duration-300 <?php echo e($currentStep > $i ? 'bg-gradient-to-r from-blue-500 to-blue-600' : 'bg-gray-200 dark:bg-gray-600'); ?>">
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            <?php endfor; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>

                    <!-- Step Content -->
                    <div
                        class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-2xl shadow-2xl border border-gray-200/50 dark:border-gray-700/50 p-8 transition-all duration-300">
                        <!--[if BLOCK]><![endif]--><?php if($currentStep === 1): ?>
                            <!-- Step 1: Shipping Information -->
                            <div class="space-y-6">
                                <div class="flex items-center space-x-3 mb-6">
                                    <div
                                        class="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4">
                                            </path>
                                        </svg>
                                    </div>
                                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white">Shipping Information
                                    </h3>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="space-y-2">
                                        <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300">Full
                                            Name</label>
                                        <input type="text" wire:model="shipping.name"
                                            class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-transparent px-0 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500 dark:focus:border-blue-400 transition-colors duration-200">
                                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['shipping.name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                <?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>

                                    <div class="space-y-2">
                                        <label
                                            class="block text-sm font-semibold text-gray-700 dark:text-gray-300">Email</label>
                                        <input type="email" wire:model="shipping.email"
                                            class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-transparent px-0 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500 dark:focus:border-blue-400 transition-colors duration-200">
                                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['shipping.email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                <?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>

                                    <div class="space-y-2">
                                        <label
                                            class="block text-sm font-semibold text-gray-700 dark:text-gray-300">Phone</label>
                                        <input type="tel" wire:model="shipping.phone"
                                            class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-transparent px-0 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500 dark:focus:border-blue-400 transition-colors duration-200">
                                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['shipping.phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                <?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>

                                    <div class="space-y-2">
                                        <label
                                            class="block text-sm font-semibold text-gray-700 dark:text-gray-300">Country</label>
                                        <select wire:model="shipping.country"
                                            class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-gray-800 px-0 py-3 text-gray-900 dark:text-white focus:outline-none focus:border-blue-500 dark:focus:border-blue-400 transition-colors duration-200">
                                            <option value="US">United States</option>
                                            <option value="CA">Canada</option>
                                            <option value="MX">Mexico</option>
                                        </select>
                                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['shipping.country'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                <?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>

                                    <div class="md:col-span-2 space-y-2">
                                        <label
                                            class="block text-sm font-semibold text-gray-700 dark:text-gray-300">Address</label>
                                        <input type="text" wire:model="shipping.address"
                                            class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-transparent px-0 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500 dark:focus:border-blue-400 transition-colors duration-200">
                                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['shipping.address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                <?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>

                                    <div class="space-y-2">
                                        <label
                                            class="block text-sm font-semibold text-gray-700 dark:text-gray-300">City</label>
                                        <input type="text" wire:model="shipping.city"
                                            class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-transparent px-0 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500 dark:focus:border-blue-400 transition-colors duration-200">
                                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['shipping.city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                <?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>

                                    <div class="space-y-2">
                                        <label
                                            class="block text-sm font-semibold text-gray-700 dark:text-gray-300">State</label>
                                        <input type="text" wire:model="shipping.state"
                                            class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-transparent px-0 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500 dark:focus:border-blue-400 transition-colors duration-200">
                                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['shipping.state'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                <?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>

                                    <div class="space-y-2">
                                        <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300">ZIP
                                            Code</label>
                                        <input type="text" wire:model="shipping.zipcode"
                                            class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-transparent px-0 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500 dark:focus:border-blue-400 transition-colors duration-200">
                                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['shipping.zipcode'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                <?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                </div>
                            </div>
                        <?php elseif($currentStep === 2): ?>
                            <!-- Step 2: Billing Information -->
                            <div class="space-y-6">
                                <div class="flex items-center space-x-3 mb-6">
                                    <div
                                        class="w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                            </path>
                                        </svg>
                                    </div>
                                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white">Billing Information
                                    </h3>
                                </div>

                                <div class="mb-6">
                                    <label class="flex items-center group cursor-pointer">
                                        <input type="checkbox" wire:model.live="billing.same_as_shipping"
                                            class="sr-only">
                                        <div class="relative">
                                            <div
                                                class="w-6 h-6 rounded-md border-2 border-gray-300 dark:border-gray-600 transition-all duration-200 <?php echo e($billing['same_as_shipping'] ? 'bg-blue-500 border-blue-500' : 'bg-white dark:bg-gray-700'); ?>">
                                            </div>
                                            <!--[if BLOCK]><![endif]--><?php if($billing['same_as_shipping']): ?>
                                                <svg class="absolute inset-0.5 w-5 h-5 text-white" fill="none"
                                                    stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="3" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                        <span
                                            class="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white transition-colors">Same
                                            as shipping address</span>
                                    </label>
                                </div>

                                <!--[if BLOCK]><![endif]--><?php if(!$billing['same_as_shipping']): ?>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 animate-fade-in">
                                        <div class="space-y-2">
                                            <label
                                                class="block text-sm font-semibold text-gray-700 dark:text-gray-300">Full
                                                Name</label>
                                            <input type="text" wire:model="billing.name"
                                                class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-transparent px-0 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-purple-500 dark:focus:border-purple-400 transition-colors duration-200">
                                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['billing.name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                    <?php echo e($message); ?></p>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>

                                        <div class="space-y-2">
                                            <label
                                                class="block text-sm font-semibold text-gray-700 dark:text-gray-300">Email</label>
                                            <input type="email" wire:model="billing.email"
                                                class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-transparent px-0 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-purple-500 dark:focus:border-purple-400 transition-colors duration-200">
                                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['billing.email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                    <?php echo e($message); ?></p>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>

                                        <div class="space-y-2">
                                            <label
                                                class="block text-sm font-semibold text-gray-700 dark:text-gray-300">Phone</label>
                                            <input type="tel" wire:model="billing.phone"
                                                class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-transparent px-0 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-purple-500 dark:focus:border-purple-400 transition-colors duration-200">
                                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['billing.phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                    <?php echo e($message); ?></p>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>

                                        <div class="space-y-2">
                                            <label
                                                class="block text-sm font-semibold text-gray-700 dark:text-gray-300">Country</label>
                                            <select wire:model="billing.country"
                                                class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-gray-800 px-0 py-3 text-gray-900 dark:text-white focus:outline-none focus:border-purple-500 dark:focus:border-purple-400 transition-colors duration-200">
                                                <option value="US">United States</option>
                                                <option value="CA">Canada</option>
                                                <option value="MX">Mexico</option>
                                            </select>
                                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['billing.country'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                    <?php echo e($message); ?></p>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>

                                        <div class="md:col-span-2 space-y-2">
                                            <label
                                                class="block text-sm font-semibold text-gray-700 dark:text-gray-300">Address</label>
                                            <input type="text" wire:model="billing.address"
                                                class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-transparent px-0 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-purple-500 dark:focus:border-purple-400 transition-colors duration-200">
                                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['billing.address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                    <?php echo e($message); ?></p>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>

                                        <div class="space-y-2">
                                            <label
                                                class="block text-sm font-semibold text-gray-700 dark:text-gray-300">City</label>
                                            <input type="text" wire:model="billing.city"
                                                class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-transparent px-0 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-purple-500 dark:focus:border-purple-400 transition-colors duration-200">
                                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['billing.city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                    <?php echo e($message); ?></p>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>

                                        <div class="space-y-2">
                                            <label
                                                class="block text-sm font-semibold text-gray-700 dark:text-gray-300">State</label>
                                            <input type="text" wire:model="billing.state"
                                                class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-transparent px-0 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-purple-500 dark:focus:border-purple-400 transition-colors duration-200">
                                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['billing.state'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                    <?php echo e($message); ?></p>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>

                                        <div class="space-y-2">
                                            <label
                                                class="block text-sm font-semibold text-gray-700 dark:text-gray-300">ZIP
                                                Code</label>
                                            <input type="text" wire:model="billing.zipcode"
                                                class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-transparent px-0 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-purple-500 dark:focus:border-purple-400 transition-colors duration-200">
                                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['billing.zipcode'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                    <?php echo e($message); ?></p>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        <?php elseif($currentStep === 3): ?>
                            <!-- Step 3: Shipping Method -->
                            <div class="space-y-6">
                                <div class="flex items-center space-x-3 mb-6">
                                    <div
                                        class="w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white">Delivery Options</h3>
                                </div>

                                <!--[if BLOCK]><![endif]--><?php if(!empty($shippingOptions)): ?>
                                    <div class="space-y-4">
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $shippingOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <label
                                                class="group flex items-center p-6 border-2 rounded-xl cursor-pointer transition-all duration-300 hover:bg-gray-50/50 dark:hover:bg-gray-700/30 <?php echo e($selectedShippingMethod === $option['id'] ? 'border-green-500 dark:border-green-400 bg-green-50/50 dark:bg-green-900/20 shadow-lg shadow-green-500/10' : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'); ?>">
                                                <div class="relative">
                                                    <input type="radio" wire:model.live="selectedShippingMethod"
                                                        value="<?php echo e($option['id']); ?>" class="sr-only">
                                                    <div
                                                        class="w-5 h-5 rounded-full border-2 transition-all duration-200 <?php echo e($selectedShippingMethod === $option['id'] ? 'border-green-500 dark:border-green-400 bg-green-500 dark:bg-green-400' : 'border-gray-300 dark:border-gray-600 group-hover:border-gray-400 dark:group-hover:border-gray-500'); ?>">
                                                        <!--[if BLOCK]><![endif]--><?php if($selectedShippingMethod === $option['id']): ?>
                                                            <div
                                                                class="absolute inset-1 bg-white dark:bg-gray-900 rounded-full">
                                                            </div>
                                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                    </div>
                                                </div>
                                                <div class="ml-4 flex-1">
                                                    <div class="flex justify-between items-start">
                                                        <div class="flex-1">
                                                            <div class="flex items-center space-x-2 mb-1">
                                                                <svg class="w-5 h-5 text-green-500 dark:text-green-400"
                                                                    fill="none" stroke="currentColor"
                                                                    viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round"
                                                                        stroke-linejoin="round" stroke-width="2"
                                                                        d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4">
                                                                    </path>
                                                                </svg>
                                                                <p class="font-semibold text-gray-900 dark:text-white">
                                                                    <?php echo e($option['name']); ?></p>
                                                            </div>
                                                            <p
                                                                class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                                                                <?php echo e($option['description']); ?></p>
                                                        </div>
                                                        <div class="text-right ml-4">
                                                            <p class="font-bold text-lg text-gray-900 dark:text-white">
                                                                <!--[if BLOCK]><![endif]--><?php if($option['cost'] > 0): ?>
                                                                    $<?php echo e(number_format($option['cost'], 2)); ?>

                                                                <?php else: ?>
                                                                    <span
                                                                        class="text-green-600 dark:text-green-400">Free</span>
                                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                            </p>
                                                            <p
                                                                class="text-sm text-gray-500 dark:text-gray-400 font-medium">
                                                                <?php echo e($option['estimated_days']); ?> days</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </label>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                <?php else: ?>
                                    <div class="flex items-center justify-center py-12">
                                        <div class="text-center">
                                            <div
                                                class="w-12 h-12 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center animate-pulse">
                                                <svg class="w-6 h-6 text-gray-400 dark:text-gray-500" fill="none"
                                                    stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4">
                                                    </path>
                                                </svg>
                                            </div>
                                            <p class="text-gray-600 dark:text-gray-400 font-medium">Loading shipping
                                                options...</p>
                                        </div>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['selectedShippingMethod'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 dark:text-red-400 text-sm mt-4 animate-shake">
                                        <?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        <?php elseif($currentStep === 4): ?>
                            <!-- Step 4: Payment -->
                            <div class="space-y-6">
                                <div class="flex items-center space-x-3 mb-6">
                                    <div
                                        class="w-10 h-10 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z">
                                            </path>
                                        </svg>
                                    </div>
                                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white">Payment Information
                                    </h3>
                                </div>

                                <!-- Payment Method Selection -->
                                <div class="space-y-4">
                                    <label
                                        class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-4">Choose
                                        Payment Method</label>
                                    <div class="space-y-3">
                                        <label
                                            class="group flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-300 hover:bg-gray-50/50 dark:hover:bg-gray-700/30 <?php echo e($payment['payment_method'] === 'credit_card' ? 'border-yellow-500 dark:border-yellow-400 bg-yellow-50/50 dark:bg-yellow-900/20 shadow-lg shadow-yellow-500/10' : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'); ?>">
                                            <div class="relative">
                                                <input type="radio" wire:model.live="payment.payment_method"
                                                    value="credit_card" class="sr-only">
                                                <div
                                                    class="w-5 h-5 rounded-full border-2 transition-all duration-200 <?php echo e($payment['payment_method'] === 'credit_card' ? 'border-yellow-500 dark:border-yellow-400 bg-yellow-500 dark:bg-yellow-400' : 'border-gray-300 dark:border-gray-600 group-hover:border-gray-400 dark:group-hover:border-gray-500'); ?>">
                                                    <!--[if BLOCK]><![endif]--><?php if($payment['payment_method'] === 'credit_card'): ?>
                                                        <div
                                                            class="absolute inset-1 bg-white dark:bg-gray-900 rounded-full">
                                                        </div>
                                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                </div>
                                            </div>
                                            <div class="ml-4 flex items-center space-x-3">
                                                <svg class="w-6 h-6 text-yellow-500 dark:text-yellow-400"
                                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z">
                                                    </path>
                                                </svg>
                                                <span class="font-semibold text-gray-900 dark:text-white">Credit
                                                    Card</span>
                                            </div>
                                        </label>

                                        <label
                                            class="group flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-300 hover:bg-gray-50/50 dark:hover:bg-gray-700/30 <?php echo e($payment['payment_method'] === 'paypal' ? 'border-blue-500 dark:border-blue-400 bg-blue-50/50 dark:bg-blue-900/20 shadow-lg shadow-blue-500/10' : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'); ?>">
                                            <div class="relative">
                                                <input type="radio" wire:model.live="payment.payment_method"
                                                    value="paypal" class="sr-only">
                                                <div
                                                    class="w-5 h-5 rounded-full border-2 transition-all duration-200 <?php echo e($payment['payment_method'] === 'paypal' ? 'border-blue-500 dark:border-blue-400 bg-blue-500 dark:bg-blue-400' : 'border-gray-300 dark:border-gray-600 group-hover:border-gray-400 dark:group-hover:border-gray-500'); ?>">
                                                    <!--[if BLOCK]><![endif]--><?php if($payment['payment_method'] === 'paypal'): ?>
                                                        <div
                                                            class="absolute inset-1 bg-white dark:bg-gray-900 rounded-full">
                                                        </div>
                                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                </div>
                                            </div>
                                            <div class="ml-4 flex items-center space-x-3">
                                                <svg class="w-6 h-6 text-blue-500 dark:text-blue-400"
                                                    fill="currentColor" viewBox="0 0 24 24">
                                                    <path
                                                        d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.421c-.315-.178-.7-.284-1.16-.284H12.12l-.69 4.378h2.19c2.570 0 4.578-.543 5.69-1.81.505-.576.808-1.284.912-2.063z" />
                                                </svg>
                                                <span class="font-semibold text-gray-900 dark:text-white">PayPal</span>
                                            </div>
                                        </label>

                                        <label
                                            class="group flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-300 hover:bg-gray-50/50 dark:hover:bg-gray-700/30 <?php echo e($payment['payment_method'] === 'bank_transfer' ? 'border-purple-500 dark:border-purple-400 bg-purple-50/50 dark:bg-purple-900/20 shadow-lg shadow-purple-500/10' : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'); ?>">
                                            <div class="relative">
                                                <input type="radio" wire:model.live="payment.payment_method"
                                                    value="bank_transfer" class="sr-only">
                                                <div
                                                    class="w-5 h-5 rounded-full border-2 transition-all duration-200 <?php echo e($payment['payment_method'] === 'bank_transfer' ? 'border-purple-500 dark:border-purple-400 bg-purple-500 dark:bg-purple-400' : 'border-gray-300 dark:border-gray-600 group-hover:border-gray-400 dark:group-hover:border-gray-500'); ?>">
                                                    <!--[if BLOCK]><![endif]--><?php if($payment['payment_method'] === 'bank_transfer'): ?>
                                                        <div
                                                            class="absolute inset-1 bg-white dark:bg-gray-900 rounded-full">
                                                        </div>
                                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                </div>
                                            </div>
                                            <div class="ml-4 flex items-center space-x-3">
                                                <svg class="w-6 h-6 text-purple-500 dark:text-purple-400"
                                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                                    </path>
                                                </svg>
                                                <span class="font-semibold text-gray-900 dark:text-white">Bank
                                                    Transfer</span>
                                            </div>
                                        </label>
                                    </div>
                                </div>

                                <!-- Payment Details -->
                                <!--[if BLOCK]><![endif]--><?php if($payment['payment_method'] === 'credit_card'): ?>
                                    <div
                                        class="bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm border border-gray-200/50 dark:border-gray-600/50 rounded-xl p-6 space-y-6">
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div class="md:col-span-2 space-y-2">
                                                <label
                                                    class="block text-sm font-semibold text-gray-700 dark:text-gray-300">Card
                                                    Number</label>
                                                <div class="relative">
                                                    <input type="text" wire:model="payment.card_number"
                                                        placeholder="1234 5678 9012 3456"
                                                        class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-transparent px-0 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-yellow-500 dark:focus:border-yellow-400 transition-colors duration-200 font-mono text-lg tracking-wider">
                                                    <svg class="absolute right-0 top-3 w-6 h-6 text-gray-400 dark:text-gray-500"
                                                        fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z">
                                                        </path>
                                                    </svg>
                                                </div>
                                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['payment.card_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                        <?php echo e($message); ?></p>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>

                                            <div class="space-y-2">
                                                <label
                                                    class="block text-sm font-semibold text-gray-700 dark:text-gray-300">Expiry
                                                    Date</label>
                                                <input type="text" wire:model="payment.card_expiry"
                                                    placeholder="MM/YY"
                                                    class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-transparent px-0 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-yellow-500 dark:focus:border-yellow-400 transition-colors duration-200 font-mono">
                                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['payment.card_expiry'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                        <?php echo e($message); ?></p>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>

                                            <div class="space-y-2">
                                                <label
                                                    class="block text-sm font-semibold text-gray-700 dark:text-gray-300">CVV</label>
                                                <div class="relative">
                                                    <input type="text" wire:model="payment.card_cvv"
                                                        placeholder="123"
                                                        class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-transparent px-0 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-yellow-500 dark:focus:border-yellow-400 transition-colors duration-200 font-mono">
                                                    <svg class="absolute right-0 top-3 w-5 h-5 text-gray-400 dark:text-gray-500"
                                                        fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                                                        </path>
                                                    </svg>
                                                </div>
                                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['payment.card_cvv'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                        <?php echo e($message); ?></p>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>

                                            <div class="md:col-span-2 space-y-2">
                                                <label
                                                    class="block text-sm font-semibold text-gray-700 dark:text-gray-300">Cardholder
                                                    Name</label>
                                                <input type="text" wire:model="payment.card_name"
                                                    placeholder="John Doe"
                                                    class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-transparent px-0 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-yellow-500 dark:focus:border-yellow-400 transition-colors duration-200">
                                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['payment.card_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                        <?php echo e($message); ?></p>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>
                                        </div>

                                        <div
                                            class="bg-yellow-50/50 dark:bg-yellow-900/20 border border-yellow-200/50 dark:border-yellow-400/20 rounded-lg p-4">
                                            <div class="flex items-start space-x-3">
                                                <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0"
                                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
                                                    </path>
                                                </svg>
                                                <div>
                                                    <p
                                                        class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                                                        Secure Payment</p>
                                                    <p class="text-xs text-yellow-700 dark:text-yellow-300 mt-1">Your
                                                        payment information is encrypted and secure. We never store your
                                                        card details.</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php elseif($payment['payment_method'] === 'paypal'): ?>
                                    <div
                                        class="bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm border border-gray-200/50 dark:border-gray-600/50 rounded-xl p-8">
                                        <div class="text-center">
                                            <div
                                                class="w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-blue-500 to-blue-600 dark:from-blue-400 dark:to-blue-500 rounded-full flex items-center justify-center shadow-lg animate-pulse">
                                                <svg class="w-10 h-10 text-white" fill="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path
                                                        d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.421c-.315-.178-.7-.284-1.16-.284H12.12l-.69 4.378h2.19c2.570 0 4.578-.543 5.69-1.81.505-.576.808-1.284.912-2.063z" />
                                                </svg>
                                            </div>
                                            <h4 class="text-xl font-bold text-gray-900 dark:text-white mb-3">PayPal
                                                Payment</h4>
                                            <p class="text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">You will
                                                be securely redirected to PayPal to complete your payment. Your order
                                                will be processed immediately after payment confirmation.</p>

                                            <div class="max-w-sm mx-auto space-y-2">
                                                <label
                                                    class="block text-sm font-semibold text-gray-700 dark:text-gray-300 text-left">PayPal
                                                    Email (Optional)</label>
                                                <input type="email" wire:model="payment.paypal_email"
                                                    placeholder="<EMAIL>"
                                                    class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-transparent px-0 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500 dark:focus:border-blue-400 transition-colors duration-200">
                                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['payment.paypal_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                        <?php echo e($message); ?></p>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>

                                            <div
                                                class="mt-6 bg-blue-50/50 dark:bg-blue-900/20 border border-blue-200/50 dark:border-blue-400/20 rounded-lg p-4">
                                                <div class="flex items-center justify-center space-x-2">
                                                    <svg class="w-5 h-5 text-blue-600 dark:text-blue-400"
                                                        fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z">
                                                        </path>
                                                    </svg>
                                                    <span
                                                        class="text-sm font-medium text-blue-800 dark:text-blue-200">Secure
                                                        PayPal Processing</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php elseif($payment['payment_method'] === 'bank_transfer'): ?>
                                    <div
                                        class="bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm border border-gray-200/50 dark:border-gray-600/50 rounded-xl p-6 space-y-6">
                                        <div class="grid grid-cols-1 gap-6">
                                            <div class="space-y-2">
                                                <label
                                                    class="block text-sm font-semibold text-gray-700 dark:text-gray-300">Bank
                                                    Name</label>
                                                <input type="text" wire:model="payment.bank_name"
                                                    placeholder="Enter your bank name"
                                                    class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-transparent px-0 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-purple-500 dark:focus:border-purple-400 transition-colors duration-200">
                                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['payment.bank_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                        <?php echo e($message); ?></p>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>

                                            <div class="space-y-2">
                                                <label
                                                    class="block text-sm font-semibold text-gray-700 dark:text-gray-300">Account
                                                    Number</label>
                                                <input type="text" wire:model="payment.account_number"
                                                    placeholder="Enter your account number"
                                                    class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-transparent px-0 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-purple-500 dark:focus:border-purple-400 transition-colors duration-200 font-mono">
                                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['payment.account_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                        <?php echo e($message); ?></p>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>

                                            <div class="space-y-2">
                                                <label
                                                    class="block text-sm font-semibold text-gray-700 dark:text-gray-300">Routing
                                                    Number</label>
                                                <input type="text" wire:model="payment.routing_number"
                                                    placeholder="Enter routing number"
                                                    class="w-full border-0 border-b-2 border-gray-200 dark:border-gray-600 bg-transparent dark:bg-transparent px-0 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-purple-500 dark:focus:border-purple-400 transition-colors duration-200 font-mono">
                                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['payment.routing_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <p class="text-red-500 dark:text-red-400 text-sm mt-1 animate-shake">
                                                        <?php echo e($message); ?></p>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>
                                        </div>

                                        <div
                                            class="bg-amber-50/50 dark:bg-amber-900/20 border border-amber-200/50 dark:border-amber-400/20 rounded-lg p-4">
                                            <div class="flex items-start space-x-3">
                                                <svg class="w-5 h-5 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0"
                                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z">
                                                    </path>
                                                </svg>
                                                <div>
                                                    <p
                                                        class="text-sm font-semibold text-amber-800 dark:text-amber-200 mb-1">
                                                        Processing Time Notice</p>
                                                    <p
                                                        class="text-xs text-amber-700 dark:text-amber-300 leading-relaxed">
                                                        Bank transfer payments require manual verification and may take
                                                        1-3 business days to process. You will receive an email
                                                        confirmation once payment is verified.</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="flex justify-between items-center mt-8 pt-6 border-t border-gray-200/50 dark:border-gray-700/50">
            <button wire:click="previousStep" <?php if($currentStep === 1): ?> disabled <?php endif; ?>
                class="group inline-flex items-center px-6 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:border-gray-400 dark:hover:border-gray-500 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-transparent dark:disabled:hover:bg-transparent transition-all duration-200 font-medium">
                <svg class="w-5 h-5 mr-2 group-hover:-translate-x-0.5 transition-transform" fill="none"
                    stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                Previous
            </button>

            <!--[if BLOCK]><![endif]--><?php if($currentStep < $maxSteps): ?>
                <button wire:click="nextStep"
                    class="group inline-flex items-center px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 dark:from-blue-500 dark:to-blue-600 dark:hover:from-blue-600 dark:hover:to-blue-700 text-white rounded-xl font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl shadow-blue-500/25 dark:shadow-blue-400/25">
                    Continue
                    <svg class="w-5 h-5 ml-2 group-hover:translate-x-0.5 transition-transform" fill="none"
                        stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7">
                        </path>
                    </svg>
                </button>
            <?php else: ?>
                <button wire:click="processOrder" wire:loading.attr="disabled"
                    class="group inline-flex items-center px-8 py-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 dark:from-green-500 dark:to-emerald-500 dark:hover:from-green-600 dark:hover:to-emerald-600 text-white rounded-xl font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl shadow-green-500/25 dark:shadow-green-400/25 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-lg">
                    <span wire:loading.remove class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7">
                            </path>
                        </svg>
                        Complete Order
                    </span>
                    <span wire:loading class="flex items-center">
                        <svg class="w-5 h-5 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                            </path>
                        </svg>
                        Processing...
                    </span>
                </button>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>

                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['payment'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="mt-6 p-4 bg-red-50/80 dark:bg-red-900/20 backdrop-blur-sm border border-red-200/50 dark:border-red-400/20 rounded-xl animate-shake">
                            <div class="flex items-start space-x-3">
                                <svg class="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div>
                                    <p class="font-semibold text-red-800 dark:text-red-200">Payment Error</p>
                                    <p class="text-sm text-red-700 dark:text-red-300 mt-1"><?php echo e($message); ?></p>
                                </div>
                            </div>
                        </div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                </div>

            <!-- Order Summary Sidebar -->
                <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-2xl shadow-2xl border border-gray-200/50 dark:border-gray-700/50 p-6 sticky top-8 transition-all duration-300">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">Order Summary</h3>
                    </div>

        <!-- Cart Items -->
        <div class="space-y-4 mb-6">
            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->cartItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div
                    class="flex items-center space-x-4 p-3 bg-gray-50/50 dark:bg-gray-700/30 rounded-xl border border-gray-100 dark:border-gray-600/30 transition-colors duration-200">
                    <div
                        class="w-14 h-14 bg-white dark:bg-gray-600 rounded-lg flex items-center justify-center shadow-sm">
                        <!--[if BLOCK]><![endif]--><?php if($item->component->image_url): ?>
                            <img src="<?php echo e($item->component->image_url); ?>" alt="<?php echo e($item->component->name); ?>"
                                class="w-12 h-12 object-cover rounded-lg">
                        <?php else: ?>
                            <svg class="w-7 h-7 text-gray-400 dark:text-gray-500" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z">
                                </path>
                            </svg>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-semibold text-gray-900 dark:text-white truncate leading-tight">
                            <?php echo e($item->component->name); ?></p>
                        <div class="flex items-center space-x-2 mt-1">
                            <span
                                class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                Qty: <?php echo e($item->quantity); ?>

                            </span>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-bold text-gray-900 dark:text-white">
                            $<?php echo e(number_format($item->price * $item->quantity, 2)); ?>

                        </p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">
                            $<?php echo e(number_format($item->price, 2)); ?> each
                        </p>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
        </div>

        <!-- Order Totals -->
        <!--[if BLOCK]><![endif]--><?php if(!empty($orderTotals)): ?>
            <div class="border-t border-gray-200/50 dark:border-gray-700/50 pt-4 space-y-3">
                <div class="flex justify-between items-center text-sm">
                    <span class="text-gray-600 dark:text-gray-400 font-medium">Subtotal</span>
                    <span
                        class="text-gray-900 dark:text-white font-semibold">$<?php echo e(number_format($orderTotals['subtotal'], 2)); ?></span>
                </div>

                <div class="flex justify-between items-center text-sm">
                    <span class="text-gray-600 dark:text-gray-400 font-medium">Tax</span>
                    <span
                        class="text-gray-900 dark:text-white font-semibold">$<?php echo e(number_format($orderTotals['tax'], 2)); ?></span>
                </div>

                <div class="flex justify-between items-center text-sm">
                    <span class="text-gray-600 dark:text-gray-400 font-medium">Shipping</span>
                    <span class="text-gray-900 dark:text-white font-semibold">
                        <!--[if BLOCK]><![endif]--><?php if($orderTotals['shipping'] > 0): ?>
                            $<?php echo e(number_format($orderTotals['shipping'], 2)); ?>

                        <?php else: ?>
                            <span class="text-green-600 dark:text-green-400">Free</span>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </span>
                </div>

                <div
                    class="border-t border-gray-200/50 dark:border-gray-700/50 pt-3 flex justify-between items-center">
                    <span class="text-lg font-bold text-gray-900 dark:text-white">Total</span>
                    <span
                        class="text-xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 dark:from-indigo-400 dark:to-purple-400 bg-clip-text text-transparent">
                        $<?php echo e(number_format($orderTotals['total'], 2)); ?>

                    </span>
                </div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <!-- Security Badge -->
                    <div class="mt-6 pt-4 border-t border-gray-200/50 dark:border-gray-700/50">
                        <div class="flex items-center justify-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                            <svg class="w-4 h-4 text-green-500 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            </svg>
                            <span class="font-medium">Secure Checkout</span>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</div>
<?php /**PATH C:\lara\www\pc-builder\resources\views/livewire/shop/checkout.blade.php ENDPATH**/ ?>