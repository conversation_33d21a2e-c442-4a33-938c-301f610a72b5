<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'gateway' => '',
    'name' => '',
    'description' => '',
    'logo' => '',
    'enabled' => true,
    'selected' => false
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'gateway' => '',
    'name' => '',
    'description' => '',
    'logo' => '',
    'enabled' => true,
    'selected' => false
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="payment-gateway-card relative <?php echo e($enabled ? 'cursor-pointer' : 'cursor-not-allowed opacity-50'); ?>">
    <input 
        type="radio" 
        name="gateway" 
        value="<?php echo e($gateway); ?>" 
        id="gateway-<?php echo e($gateway); ?>"
        class="sr-only peer"
        <?php echo e($enabled ? '' : 'disabled'); ?>

        <?php echo e($selected ? 'checked' : ''); ?>

    >
    
    <label 
        for="gateway-<?php echo e($gateway); ?>" 
        class="gateway-label block w-full p-4 border-2 rounded-lg transition-all duration-200 
               <?php echo e($enabled ? 'hover:border-indigo-300 hover:shadow-md' : ''); ?>

               peer-checked:border-indigo-500 peer-checked:bg-indigo-50 peer-checked:shadow-md
               border-gray-200 bg-white"
    >
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <?php if($logo): ?>
                    <img src="<?php echo e($logo); ?>" alt="<?php echo e($name); ?> logo" class="w-8 h-8 object-contain">
                <?php else: ?>
                    <div class="w-8 h-8 bg-gradient-to-br from-indigo-400 to-indigo-600 rounded-md flex items-center justify-center">
                        <span class="text-white font-bold text-sm"><?php echo e(substr($name, 0, 1)); ?></span>
                    </div>
                <?php endif; ?>
                
                <div>
                    <h3 class="font-semibold text-gray-900"><?php echo e($name); ?></h3>
                    <?php if($description): ?>
                        <p class="text-sm text-gray-600"><?php echo e($description); ?></p>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="flex items-center space-x-2">
                <?php if(!$enabled): ?>
                    <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">Disabled</span>
                <?php endif; ?>
                
                <div class="w-5 h-5 border-2 border-gray-300 rounded-full peer-checked:border-indigo-500 peer-checked:bg-indigo-500 flex items-center justify-center">
                    <div class="w-2 h-2 bg-white rounded-full opacity-0 peer-checked:opacity-100 transition-opacity"></div>
                </div>
            </div>
        </div>
        
        <?php if($enabled): ?>
            <div class="mt-3 text-xs text-gray-500">
                Click to select this payment method
            </div>
        <?php endif; ?>
    </label>
</div><?php /**PATH C:\lara\www\pc-builder\resources\views/components/payment-gateway-card.blade.php ENDPATH**/ ?>