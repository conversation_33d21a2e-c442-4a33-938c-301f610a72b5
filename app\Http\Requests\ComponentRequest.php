<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ComponentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization should be handled by middleware/policies
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $componentId = $this->route('component')?->id ?? $this->component?->id;

        return [
            // Basic component information
            'name' => [
                'required',
                'string',
                'max:255',
                'min:3'
            ],
            'slug' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-z0-9]+(?:-[a-z0-9]+)*$/',
                Rule::unique('components', 'slug')->ignore($componentId)
            ],
            'description' => [
                'nullable',
                'string',
                'max:2000'
            ],
            'category_id' => [
                'required',
                'integer',
                'exists:component_categories,id'
            ],
            'brand' => [
                'required',
                'string',
                'max:100',
                'min:2'
            ],
            'model' => [
                'required',
                'string',
                'max:100',
                'min:1'
            ],

            // Pricing and inventory
            'price' => [
                'required',
                'numeric',
                'min:0',
                'max:999999.99'
            ],
            'stock' => [
                'required',
                'integer',
                'min:0',
                'max:999999'
            ],

            // Image and media
            'image' => [
                'nullable',
                'string',
                'max:500'
            ],

            // Technical specifications
            'socket_type' => [
                'nullable',
                'string',
                'max:50',
                'regex:/^[A-Za-z0-9\-\+\s]+$/'
            ],
            'chipset' => [
                'nullable',
                'string',
                'max:100',
                'regex:/^[A-Za-z0-9\-\+\s]+$/'
            ],
            'form_factor' => [
                'nullable',
                'string',
                'max:50',
                'in:ATX,Micro-ATX,Mini-ITX,E-ATX,XL-ATX,Standard,Low Profile,Full Height,Half Height'
            ],
            'power_consumption' => [
                'nullable',
                'integer',
                'min:0',
                'max:2000' // Maximum reasonable power consumption in watts
            ],
            'cooling_type' => [
                'nullable',
                'string',
                'max:50',
                'in:Air,Liquid,Passive,Hybrid'
            ],
            'memory_type' => [
                'nullable',
                'string',
                'max:50',
                'in:DDR3,DDR4,DDR5,GDDR5,GDDR6,GDDR6X,HBM2,HBM3'
            ],
            'interface_type' => [
                'nullable',
                'string',
                'max:50',
                'in:PCIe 3.0,PCIe 4.0,PCIe 5.0,SATA III,M.2,NVMe,USB 3.0,USB 3.1,USB 3.2,USB-C,Thunderbolt 3,Thunderbolt 4'
            ],

            // Product information
            'warranty_months' => [
                'nullable',
                'integer',
                'min:0',
                'max:120' // Maximum 10 years warranty
            ],
            'manufacturer_part_number' => [
                'nullable',
                'string',
                'max:100',
                'regex:/^[A-Za-z0-9\-\+\s]+$/'
            ],
            'ean_code' => [
                'nullable',
                'string',
                'regex:/^[0-9]{8,13}$/' // EAN-8 or EAN-13 format
            ],
            'weight_grams' => [
                'nullable',
                'integer',
                'min:0',
                'max:50000' // Maximum 50kg
            ],
            'dimensions_json' => [
                'nullable',
                'array'
            ],
            'dimensions_json.length' => [
                'nullable',
                'numeric',
                'min:0',
                'max:1000' // Maximum 1 meter
            ],
            'dimensions_json.width' => [
                'nullable',
                'numeric',
                'min:0',
                'max:1000'
            ],
            'dimensions_json.height' => [
                'nullable',
                'numeric',
                'min:0',
                'max:1000'
            ],
            'release_date' => [
                'nullable',
                'date',
                'before_or_equal:today'
            ],
            'discontinued_at' => [
                'nullable',
                'date',
                'after_or_equal:release_date'
            ],

            // Additional specifications
            'specs' => [
                'nullable',
                'array'
            ],
            'specs.*' => [
                'string',
                'max:500'
            ],

            // Status flags
            'is_featured' => [
                'boolean'
            ],
            'is_active' => [
                'boolean'
            ]
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Component name is required.',
            'name.min' => 'Component name must be at least 3 characters.',
            'slug.required' => 'Component slug is required.',
            'slug.regex' => 'Slug must contain only lowercase letters, numbers, and hyphens.',
            'slug.unique' => 'This slug is already taken.',
            'category_id.required' => 'Component category is required.',
            'category_id.exists' => 'Selected category does not exist.',
            'brand.required' => 'Brand is required.',
            'brand.min' => 'Brand must be at least 2 characters.',
            'model.required' => 'Model is required.',
            'price.required' => 'Price is required.',
            'price.min' => 'Price must be greater than or equal to 0.',
            'price.max' => 'Price cannot exceed $999,999.99.',
            'stock.required' => 'Stock quantity is required.',
            'stock.min' => 'Stock cannot be negative.',
            'power_consumption.max' => 'Power consumption cannot exceed 2000W.',
            'form_factor.in' => 'Invalid form factor selected.',
            'cooling_type.in' => 'Invalid cooling type selected.',
            'memory_type.in' => 'Invalid memory type selected.',
            'interface_type.in' => 'Invalid interface type selected.',
            'warranty_months.max' => 'Warranty period cannot exceed 120 months.',
            'ean_code.regex' => 'EAN code must be 8-13 digits.',
            'weight_grams.max' => 'Weight cannot exceed 50kg.',
            'dimensions_json.length.max' => 'Length cannot exceed 1000mm.',
            'dimensions_json.width.max' => 'Width cannot exceed 1000mm.',
            'dimensions_json.height.max' => 'Height cannot exceed 1000mm.',
            'release_date.before_or_equal' => 'Release date cannot be in the future.',
            'discontinued_at.after_or_equal' => 'Discontinuation date must be after release date.'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'category_id' => 'category',
            'manufacturer_part_number' => 'manufacturer part number',
            'ean_code' => 'EAN code',
            'weight_grams' => 'weight',
            'power_consumption' => 'power consumption',
            'warranty_months' => 'warranty period',
            'is_featured' => 'featured status',
            'is_active' => 'active status',
            'dimensions_json.length' => 'length',
            'dimensions_json.width' => 'width',
            'dimensions_json.height' => 'height'
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Custom compatibility validation rules
            $this->validateTechnicalSpecifications($validator);
            $this->validateCompatibilityRequirements($validator);
        });
    }

    /**
     * Validate technical specifications based on component category.
     */
    protected function validateTechnicalSpecifications($validator): void
    {
        $categoryId = $this->input('category_id');
        
        if (!$categoryId) {
            return;
        }

        $category = \App\Models\ComponentCategory::find($categoryId);
        
        if (!$category) {
            return;
        }

        // Category-specific validation rules
        switch ($category->slug) {
            case 'cpu':
                $this->validateCpuSpecs($validator);
                break;
            case 'motherboard':
                $this->validateMotherboardSpecs($validator);
                break;
            case 'ram':
                $this->validateRamSpecs($validator);
                break;
            case 'gpu':
                $this->validateGpuSpecs($validator);
                break;
            case 'storage':
                $this->validateStorageSpecs($validator);
                break;
            case 'psu':
                $this->validatePsuSpecs($validator);
                break;
            case 'case':
                $this->validateCaseSpecs($validator);
                break;
            case 'cooling':
                $this->validateCoolingSpecs($validator);
                break;
        }
    }

    /**
     * Validate CPU-specific specifications.
     */
    protected function validateCpuSpecs($validator): void
    {
        if (!$this->input('socket_type')) {
            $validator->errors()->add('socket_type', 'Socket type is required for CPU components.');
        }

        if (!$this->input('power_consumption')) {
            $validator->errors()->add('power_consumption', 'Power consumption is required for CPU components.');
        }
    }

    /**
     * Validate motherboard-specific specifications.
     */
    protected function validateMotherboardSpecs($validator): void
    {
        if (!$this->input('socket_type')) {
            $validator->errors()->add('socket_type', 'Socket type is required for motherboard components.');
        }

        if (!$this->input('form_factor')) {
            $validator->errors()->add('form_factor', 'Form factor is required for motherboard components.');
        }

        if (!$this->input('memory_type')) {
            $validator->errors()->add('memory_type', 'Memory type is required for motherboard components.');
        }
    }

    /**
     * Validate RAM-specific specifications.
     */
    protected function validateRamSpecs($validator): void
    {
        if (!$this->input('memory_type')) {
            $validator->errors()->add('memory_type', 'Memory type is required for RAM components.');
        }
    }

    /**
     * Validate GPU-specific specifications.
     */
    protected function validateGpuSpecs($validator): void
    {
        if (!$this->input('interface_type')) {
            $validator->errors()->add('interface_type', 'Interface type is required for GPU components.');
        }

        if (!$this->input('power_consumption')) {
            $validator->errors()->add('power_consumption', 'Power consumption is required for GPU components.');
        }
    }

    /**
     * Validate storage-specific specifications.
     */
    protected function validateStorageSpecs($validator): void
    {
        if (!$this->input('interface_type')) {
            $validator->errors()->add('interface_type', 'Interface type is required for storage components.');
        }
    }

    /**
     * Validate PSU-specific specifications.
     */
    protected function validatePsuSpecs($validator): void
    {
        if (!$this->input('power_consumption')) {
            $validator->errors()->add('power_consumption', 'Wattage is required for PSU components.');
        }

        // PSU power consumption should be the output wattage
        $powerConsumption = $this->input('power_consumption');
        if ($powerConsumption && ($powerConsumption < 200 || $powerConsumption > 2000)) {
            $validator->errors()->add('power_consumption', 'PSU wattage should be between 200W and 2000W.');
        }
    }

    /**
     * Validate case-specific specifications.
     */
    protected function validateCaseSpecs($validator): void
    {
        if (!$this->input('form_factor')) {
            $validator->errors()->add('form_factor', 'Form factor is required for case components.');
        }
    }

    /**
     * Validate cooling-specific specifications.
     */
    protected function validateCoolingSpecs($validator): void
    {
        if (!$this->input('cooling_type')) {
            $validator->errors()->add('cooling_type', 'Cooling type is required for cooling components.');
        }

        if (!$this->input('socket_type')) {
            $validator->errors()->add('socket_type', 'Compatible socket type is required for cooling components.');
        }
    }

    /**
     * Validate compatibility requirements between specifications.
     */
    protected function validateCompatibilityRequirements($validator): void
    {
        // Validate that discontinued components have a discontinuation date
        if ($this->input('discontinued_at') && $this->input('is_active')) {
            $validator->errors()->add('is_active', 'Discontinued components cannot be active.');
        }

        // Validate release date vs discontinuation date
        $releaseDate = $this->input('release_date');
        $discontinuedAt = $this->input('discontinued_at');
        
        if ($releaseDate && $discontinuedAt && $discontinuedAt < $releaseDate) {
            $validator->errors()->add('discontinued_at', 'Discontinuation date cannot be before release date.');
        }

        // Validate dimensions consistency
        $dimensions = $this->input('dimensions_json', []);
        if (!empty($dimensions)) {
            $requiredDimensions = ['length', 'width', 'height'];
            $providedDimensions = array_keys($dimensions);
            
            if (count(array_intersect($requiredDimensions, $providedDimensions)) > 0 && 
                count(array_intersect($requiredDimensions, $providedDimensions)) < 3) {
                $validator->errors()->add('dimensions_json', 'If providing dimensions, all three values (length, width, height) are required.');
            }
        }
    }
}