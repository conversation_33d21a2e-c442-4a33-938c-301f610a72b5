<?php $__env->startSection('content'); ?>
<div class="min-h-screen flex items-center justify-center relative overflow-hidden bg-light text-primary-light dark:bg-dark dark:text-primary-dark">
    <!-- Animated particles -->
    <div class="particle" style="left: 15%; animation-delay: 0s;"></div>
    <div class="particle" style="left: 30%; animation-delay: 1s;"></div>
    <div class="particle" style="left: 45%; animation-delay: 2s;"></div>
    <div class="particle" style="left: 60%; animation-delay: 3s;"></div>
    <div class="particle" style="left: 75%; animation-delay: 4s;"></div>
    <div class="particle" style="left: 90%; animation-delay: 5s;"></div>

    <div class="text-center z-10 px-4 max-w-4xl mx-auto">
        <!-- Error icon and title -->
        <div class="fade-in-up" style="animation-delay: 0.2s; opacity: 0;">
            <div class="float mb-8">
                <div class="inline-block p-6 bg-gradient-to-r from-primary-light to-primary-dark dark:from-primary-dark dark:to-primary-light rounded-full glow mb-6 mt-12">
                    <svg class="w-16 h-16 text-white spin-slow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01M21 12c0 4.418-4.03 8-9 8s-9-3.582-9-8 4.03-8 9-8 9 3.582 9 8z" />
                    </svg>
                </div>
                <h1 class="text-4xl md:text-6xl font-black text-gradient mb-4">
                    403 - Access Forbidden
                </h1>
            </div>
        </div>

        <!-- Error message -->
        <div class="fade-in-up" style="animation-delay: 0.4s; opacity: 0;">
            <h2 class="text-2xl md:text-3xl font-bold text-primary-light dark:text-primary-dark mb-4">
                Sorry, you don't have permission to access this page.
            </h2>
            <p class="text-lg md:text-xl text-secondary-light dark:text-secondary-dark mb-8 max-w-2xl mx-auto leading-relaxed">
                If you believe this is a mistake, please contact the administrator or try logging in with a different account.
            </p>
        </div>

        <!-- Action buttons -->
        <div class="fade-in-up mb-8" style="animation-delay: 0.6s; opacity: 0;">
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mt-8">
                <a href="<?php echo e(url('/')); ?>"
                    class="card-hover glow bg-gradient-to-r from-primary-light to-primary-dark dark:from-primary-dark dark:to-primary-light hover:from-primary-dark hover:to-primary-light dark:hover:from-primary-light dark:hover:to-primary-dark text-white font-semibold py-3 px-6 rounded-full shadow-lg transition-all duration-300">
                    <span class="flex items-center gap-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                        </svg>
                        Return to Homepage
                    </span>
                </a>
                <button onclick="window.history.back()"
                    class="card-hover bg-border-light dark:bg-border-dark hover:bg-border-dark dark:hover:bg-border-light text-primary-light dark:text-primary-dark font-semibold py-3 px-6 rounded-full border-2 border-border-light dark:border-border-dark hover:border-primary-light dark:hover:border-primary-dark transition-all duration-300">
                    <span class="flex items-center gap-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        Go Back
                    </span>
                </button>
            </div>
        </div>

        <!-- Contact and support -->
        <div class="fade-in-up" style="animation-delay: 0.8s; opacity: 0;">
            <div class="bg-black bg-opacity-30 dark:bg-white dark:bg-opacity-10 backdrop-blur-sm rounded-2xl border border-border-light dark:border-border-dark p-8 card-hover">
                <h3 class="text-xl font-semibold text-primary-light dark:text-primary-dark mb-6">Need Assistance?</h3>
                <p class="text-secondary-light dark:text-secondary-dark mb-4">If you need help, please contact our support team at <a href="mailto:<EMAIL>" class="underline text-accent-light dark:text-accent-dark"><EMAIL></a>.</p>
            </div>
        </div>

        <!-- Footer message -->
        <div class="fade-in-up mt-8" style="animation-delay: 1.0s; opacity: 0;">
            <p class="text-secondary-light dark:text-secondary-dark text-sm">
                &copy; <?php echo e(date('Y')); ?> Your Company Name. All rights reserved.
            </p>
        </div>
    </div>
    <script>
        // Add interactivity and real-time updates
        document.addEventListener('DOMContentLoaded', function() {
            // Trigger animations on load
            const elements = document.querySelectorAll('.fade-in-up');
            elements.forEach((el, index) => {
                setTimeout(() => {
                    el.style.opacity = '1';
                }, index * 200);
            });

            // Add mouse move parallax effect
            document.addEventListener('mousemove', function(e) {
                const particles = document.querySelectorAll('.particle');
                const x = e.clientX / window.innerWidth;
                const y = e.clientY / window.innerHeight;

                particles.forEach((particle, index) => {
                    const speed = (index + 1) * 0.5;
                    particle.style.transform =
                        `translateX(${x * speed}px) translateY(${y * speed}px)`;
                });
            });
        });
    </script>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
    <style>
        @keyframes float {
            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(3deg);
            }
        }
        @keyframes glow {
            0%,
            100% {
                box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
            }
            50% {
                box-shadow: 0 0 40px rgba(139, 92, 246, 0.6);
            }
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        @keyframes spin {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }
        @keyframes pulse {
            0%,
            100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }
        .float {
            animation: float 4s ease-in-out infinite;
        }
        .glow {
            animation: glow 2s ease-in-out infinite alternate;
        }
        .fade-in-up {
            animation: fadeInUp 0.8s ease-out forwards;
        }
        .spin-slow {
            animation: spin 3s linear infinite;
        }
        .pulse {
            animation: pulse 2s ease-in-out infinite;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
        }
        .text-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
        }
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(139, 92, 246, 0.6);
            border-radius: 50%;
            animation: particle 8s linear infinite;
        }
        @keyframes particle {
            0% {
                transform: translateY(100vh) translateX(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) translateX(100px);
                opacity: 0;
            }
        }
    </style>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\lara\www\pc-builder\resources\views/errors/403.blade.php ENDPATH**/ ?>