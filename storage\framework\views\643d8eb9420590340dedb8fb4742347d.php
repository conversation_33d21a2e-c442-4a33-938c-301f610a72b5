<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'gateways' => [],
    'selectedGateway' => null,
    'error' => null
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'gateways' => [],
    'selectedGateway' => null,
    'error' => null
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="payment-gateway-selector">
    <div class="mb-4">
        <h3 class="text-lg font-semibold text-gray-900 mb-2">Select Payment Method</h3>
        <p class="text-sm text-gray-600">Choose your preferred payment gateway to continue</p>
    </div>
    
    <?php if($error): ?>
        <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-red-800 text-sm"><?php echo e($error); ?></span>
            </div>
        </div>
    <?php endif; ?>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <?php $__empty_1 = true; $__currentLoopData = $gateways; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gateway): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <?php if (isset($component)) { $__componentOriginal9821895a723d2003b0a6a3bc73e74366 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9821895a723d2003b0a6a3bc73e74366 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.payment-gateway-card','data' => ['gateway' => $gateway['key'],'name' => $gateway['name'],'description' => $gateway['description'] ?? '','logo' => $gateway['logo'] ?? '','enabled' => $gateway['enabled'] ?? true,'selected' => $selectedGateway === $gateway['key']]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('payment-gateway-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['gateway' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($gateway['key']),'name' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($gateway['name']),'description' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($gateway['description'] ?? ''),'logo' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($gateway['logo'] ?? ''),'enabled' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($gateway['enabled'] ?? true),'selected' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($selectedGateway === $gateway['key'])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9821895a723d2003b0a6a3bc73e74366)): ?>
<?php $attributes = $__attributesOriginal9821895a723d2003b0a6a3bc73e74366; ?>
<?php unset($__attributesOriginal9821895a723d2003b0a6a3bc73e74366); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9821895a723d2003b0a6a3bc73e74366)): ?>
<?php $component = $__componentOriginal9821895a723d2003b0a6a3bc73e74366; ?>
<?php unset($__componentOriginal9821895a723d2003b0a6a3bc73e74366); ?>
<?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-span-full">
                <div class="text-center py-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Payment Gateways Available</h3>
                    <p class="text-gray-600">Please contact support or try again later.</p>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <?php if(count($gateways) > 0): ?>
        <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div class="flex items-start">
                <svg class="w-5 h-5 text-blue-400 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div class="text-sm text-blue-800">
                    <p class="font-medium mb-1">Secure Payment Processing</p>
                    <p>All payments are processed securely through encrypted connections. Your payment information is never stored on our servers.</p>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const gatewayCards = document.querySelectorAll('.payment-gateway-card input[type="radio"]');
    
    gatewayCards.forEach(function(radio) {
        radio.addEventListener('change', function() {
            // Dispatch custom event when gateway selection changes
            const event = new CustomEvent('gatewayChanged', {
                detail: {
                    gateway: this.value,
                    name: this.closest('.payment-gateway-card').querySelector('.gateway-label h3').textContent
                }
            });
            document.dispatchEvent(event);
        });
    });
    
    // Handle keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
            const focusedElement = document.activeElement;
            if (focusedElement.type === 'radio' && focusedElement.name === 'gateway') {
                e.preventDefault();
                const radios = Array.from(gatewayCards).filter(radio => !radio.disabled);
                const currentIndex = radios.indexOf(focusedElement);
                
                let nextIndex;
                if (e.key === 'ArrowDown') {
                    nextIndex = (currentIndex + 1) % radios.length;
                } else {
                    nextIndex = (currentIndex - 1 + radios.length) % radios.length;
                }
                
                radios[nextIndex].focus();
                radios[nextIndex].checked = true;
                radios[nextIndex].dispatchEvent(new Event('change'));
            }
        }
    });
});
</script>

<style>
.payment-gateway-card input[type="radio"]:focus + .gateway-label {
    @apply ring-2 ring-indigo-500 ring-offset-2;
}

.payment-gateway-card input[type="radio"]:checked + .gateway-label .w-2 {
    @apply opacity-100;
}

@media (max-width: 640px) {
    .payment-gateway-selector .grid {
        @apply grid-cols-1;
    }
}
</style><?php /**PATH C:\lara\www\pc-builder\resources\views/components/payment-gateway-selector.blade.php ENDPATH**/ ?>