<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('components', function (Blueprint $table) {
            // Technical specification fields
            $table->string('socket_type', 50)->nullable()->after('specs');
            $table->string('chipset', 100)->nullable()->after('socket_type');
            $table->string('form_factor', 50)->nullable()->after('chipset');
            $table->integer('power_consumption')->nullable()->after('form_factor');
            $table->string('cooling_type', 50)->nullable()->after('power_consumption');
            $table->string('memory_type', 50)->nullable()->after('cooling_type');
            $table->string('interface_type', 50)->nullable()->after('memory_type');
            
            // Warranty and product information fields
            $table->integer('warranty_months')->default(12)->after('interface_type');
            $table->string('manufacturer_part_number', 100)->nullable()->after('warranty_months');
            $table->string('ean_code', 20)->nullable()->after('manufacturer_part_number');
            $table->integer('weight_grams')->nullable()->after('ean_code');
            $table->json('dimensions_json')->nullable()->after('weight_grams');
            $table->date('release_date')->nullable()->after('dimensions_json');
            $table->timestamp('discontinued_at')->nullable()->after('release_date');
            
            // Performance indexes for common queries
            $table->index(['category_id', 'is_active'], 'idx_components_category_active');
            $table->index(['brand', 'model'], 'idx_components_brand_model');
            $table->index('socket_type', 'idx_components_socket_type');
            $table->index(['price', 'is_active'], 'idx_components_price_range');
            $table->index('form_factor', 'idx_components_form_factor');
            $table->index('power_consumption', 'idx_components_power_consumption');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('components', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex('idx_components_category_active');
            $table->dropIndex('idx_components_brand_model');
            $table->dropIndex('idx_components_socket_type');
            $table->dropIndex('idx_components_price_range');
            $table->dropIndex('idx_components_form_factor');
            $table->dropIndex('idx_components_power_consumption');
            
            // Drop columns
            $table->dropColumn([
                'socket_type',
                'chipset',
                'form_factor',
                'power_consumption',
                'cooling_type',
                'memory_type',
                'interface_type',
                'warranty_months',
                'manufacturer_part_number',
                'ean_code',
                'weight_grams',
                'dimensions_json',
                'release_date',
                'discontinued_at'
            ]);
        });
    }
};
