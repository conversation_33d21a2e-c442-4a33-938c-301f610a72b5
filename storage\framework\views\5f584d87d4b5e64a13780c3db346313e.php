<div class="max-w-7xl mx-auto">
    <!--[if BLOCK]><![endif]--><?php if(auth()->guard()->check()): ?>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">My Dashboard</h1>
                    <p class="text-gray-600">Welcome back, <?php echo e(auth()->user()->name); ?>!</p>
                </div>
                <div class="flex space-x-3">
                    <a 
                        href="<?php echo e(route('builder.index')); ?>"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    >
                        New Build
                    </a>
                    <a 
                        href="<?php echo e(route('shop.index')); ?>"
                        class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    >
                        Shop Components
                    </a>
                </div>
            </div>

            <!-- Tab Navigation -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 pt-4">
                    <nav class="flex space-x-8">
                        <button 
                            wire:click="setActiveTab('overview')"
                            class="py-2 px-1 border-b-2 font-medium text-sm <?php echo e($activeTab === 'overview' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?>"
                        >
                            Overview
                        </button>
                        <button 
                            wire:click="setActiveTab('builds')"
                            class="py-2 px-1 border-b-2 font-medium text-sm <?php echo e($activeTab === 'builds' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?>"
                        >
                            My Builds (<?php echo e($stats['total_builds']); ?>)
                        </button>
                        <button 
                            wire:click="setActiveTab('orders')"
                            class="py-2 px-1 border-b-2 font-medium text-sm <?php echo e($activeTab === 'orders' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?>"
                        >
                            Order History (<?php echo e($stats['total_orders']); ?>)
                        </button>
                        <button 
                            wire:click="setActiveTab('settings')"
                            class="py-2 px-1 border-b-2 font-medium text-sm <?php echo e($activeTab === 'settings' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?>"
                        >
                            Account Settings
                        </button>
                    </nav>
                </div>

                <div class="p-6">
                    <!-- Overview Tab -->
                    <!--[if BLOCK]><![endif]--><?php if($activeTab === 'overview'): ?>
                        <div class="space-y-6">
                            <!-- Stats Cards -->
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                                <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
                                    <div class="flex items-center">
                                        <div class="flex-1">
                                            <p class="text-blue-100 text-sm">Total Builds</p>
                                            <p class="text-2xl font-bold"><?php echo e($stats['total_builds']); ?></p>
                                        </div>
                                        <svg class="w-8 h-8 text-blue-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                        </svg>
                                    </div>
                                </div>

                                <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
                                    <div class="flex items-center">
                                        <div class="flex-1">
                                            <p class="text-green-100 text-sm">Public Builds</p>
                                            <p class="text-2xl font-bold"><?php echo e($stats['public_builds']); ?></p>
                                        </div>
                                        <svg class="w-8 h-8 text-green-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                        </svg>
                                    </div>
                                </div>

                                <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
                                    <div class="flex items-center">
                                        <div class="flex-1">
                                            <p class="text-purple-100 text-sm">Total Orders</p>
                                            <p class="text-2xl font-bold"><?php echo e($stats['total_orders']); ?></p>
                                        </div>
                                        <svg class="w-8 h-8 text-purple-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M8 11v6h8v-6M8 11H6a2 2 0 00-2 2v6a2 2 0 002 2h12a2 2 0 002-2v-6a2 2 0 00-2-2h-2" />
                                        </svg>
                                    </div>
                                </div>

                                <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg p-6 text-white">
                                    <div class="flex items-center">
                                        <div class="flex-1">
                                            <p class="text-yellow-100 text-sm">Total Spent</p>
                                            <p class="text-2xl font-bold">$<?php echo e(number_format($stats['total_spent'], 0)); ?></p>
                                        </div>
                                        <svg class="w-8 h-8 text-yellow-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                        </svg>
                                    </div>
                                </div>
                            </div>

                            <!-- Recent Activity -->
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <!-- Recent Builds -->
                                <div class="bg-gray-50 rounded-lg p-6">
                                    <div class="flex justify-between items-center mb-4">
                                        <h3 class="text-lg font-medium text-gray-900">Recent Builds</h3>
                                        <button 
                                            wire:click="setActiveTab('builds')"
                                            class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                                        >
                                            View All →
                                        </button>
                                    </div>
                                    
                                    <!--[if BLOCK]><![endif]--><?php if($recentBuilds->isEmpty()): ?>
                                        <div class="text-center py-8">
                                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                            </svg>
                                            <p class="mt-2 text-sm text-gray-500">No builds yet</p>
                                            <a 
                                                href="<?php echo e(route('builder.index')); ?>"
                                                class="mt-2 inline-flex items-center text-blue-600 hover:text-blue-800 text-sm"
                                            >
                                                Create your first build →
                                            </a>
                                        </div>
                                    <?php else: ?>
                                        <div class="space-y-3">
                                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $recentBuilds; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $build): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="bg-white rounded-lg p-4 border border-gray-200">
                                                    <div class="flex justify-between items-start">
                                                        <div class="flex-1">
                                                            <h4 class="font-medium text-gray-900"><?php echo e($build->name); ?></h4>
                                                            <p class="text-sm text-gray-500 mt-1">
                                                                <?php echo e($build->components->count()); ?> components • 
                                                                $<?php echo e(number_format($build->total_price, 2)); ?>

                                                            </p>
                                                            <p class="text-xs text-gray-400 mt-1">
                                                                Updated <?php echo e($build->updated_at->diffForHumans()); ?>

                                                            </p>
                                                        </div>
                                                        <div class="flex items-center space-x-2">
                                                            <!--[if BLOCK]><![endif]--><?php if($build->is_public): ?>
                                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                                    Public
                                                                </span>
                                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                            <button 
                                                                wire:click="editBuild(<?php echo e($build->id); ?>)"
                                                                class="text-blue-600 hover:text-blue-800 text-sm"
                                                            >
                                                                Edit
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>

                                <!-- Recent Orders -->
                                <div class="bg-gray-50 rounded-lg p-6">
                                    <div class="flex justify-between items-center mb-4">
                                        <h3 class="text-lg font-medium text-gray-900">Recent Orders</h3>
                                        <button 
                                            wire:click="setActiveTab('orders')"
                                            class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                                        >
                                            View All →
                                        </button>
                                    </div>
                                    
                                    <!--[if BLOCK]><![endif]--><?php if($recentOrders->isEmpty()): ?>
                                        <div class="text-center py-8">
                                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M8 11v6h8v-6M8 11H6a2 2 0 00-2 2v6a2 2 0 002 2h12a2 2 0 002-2v-6a2 2 0 00-2-2h-2" />
                                            </svg>
                                            <p class="mt-2 text-sm text-gray-500">No orders yet</p>
                                            <a 
                                                href="<?php echo e(route('shop.index')); ?>"
                                                class="mt-2 inline-flex items-center text-blue-600 hover:text-blue-800 text-sm"
                                            >
                                                Start shopping →
                                            </a>
                                        </div>
                                    <?php else: ?>
                                        <div class="space-y-3">
                                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $recentOrders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="bg-white rounded-lg p-4 border border-gray-200">
                                                    <div class="flex justify-between items-start">
                                                        <div class="flex-1">
                                                            <h4 class="font-medium text-gray-900">Order #<?php echo e($order->order_number); ?></h4>
                                                            <p class="text-sm text-gray-500 mt-1">
                                                                <?php echo e($order->items->count()); ?> items • 
                                                                $<?php echo e(number_format($order->total, 2)); ?>

                                                            </p>
                                                            <p class="text-xs text-gray-400 mt-1">
                                                                <?php echo e($order->created_at->format('M j, Y')); ?>

                                                            </p>
                                                        </div>
                                                        <div class="flex items-center space-x-2">
                                                            <?php
                                                                $statusColors = [
                                                                    'pending' => 'bg-yellow-100 text-yellow-800',
                                                                    'processing' => 'bg-blue-100 text-blue-800',
                                                                    'completed' => 'bg-green-100 text-green-800',
                                                                    'cancelled' => 'bg-red-100 text-red-800',
                                                                    'refunded' => 'bg-gray-100 text-gray-800',
                                                                ];
                                                            ?>
                                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <?php echo e($statusColors[$order->status] ?? 'bg-gray-100 text-gray-800'); ?>">
                                                                <?php echo e(ucfirst($order->status)); ?>

                                                            </span>
                                                            <button 
                                                                wire:click="selectOrder(<?php echo e($order->id); ?>)"
                                                                class="text-blue-600 hover:text-blue-800 text-sm"
                                                            >
                                                                View
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <!-- Builds Tab -->
                    <!--[if BLOCK]><![endif]--><?php if($activeTab === 'builds'): ?>
                        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('builder.saved-builds');

$__html = app('livewire')->mount($__name, $__params, 'lw-**********-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <!-- Orders Tab -->
                    <!--[if BLOCK]><![endif]--><?php if($activeTab === 'orders'): ?>
                        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('user.order-history');

$__html = app('livewire')->mount($__name, $__params, 'lw-**********-1', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <!-- Settings Tab -->
                    <!--[if BLOCK]><![endif]--><?php if($activeTab === 'settings'): ?>
                        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('user.account-settings');

$__html = app('livewire')->mount($__name, $__params, 'lw-**********-2', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- Not Authenticated -->
        <div class="bg-white rounded-lg shadow p-12 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">Please log in</h3>
            <p class="mt-1 text-sm text-gray-500">You need to be logged in to access your dashboard.</p>
            <div class="mt-6">
                <a 
                    href="<?php echo e(route('login')); ?>"
                    class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                    Log In
                </a>
            </div>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Flash Messages -->
    <!--[if BLOCK]><![endif]--><?php if(session()->has('message')): ?>
        <div class="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
            <div class="flex">
                <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <p class="ml-3 text-sm text-green-700"><?php echo e(session('message')); ?></p>
            </div>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <?php if(session()->has('error')): ?>
        <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <div class="flex">
                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
                <p class="ml-3 text-sm text-red-700"><?php echo e(session('error')); ?></p>
            </div>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>

<script>
    document.addEventListener('livewire:load', function () {
        Livewire.on('copyToClipboard', function (text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text);
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
            }
        });
    });
</script><?php /**PATH C:\lara\www\pc-builder\resources\views/livewire/user/dashboard.blade.php ENDPATH**/ ?>