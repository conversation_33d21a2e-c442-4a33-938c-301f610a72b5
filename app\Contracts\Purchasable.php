<?php

namespace App\Contracts;

interface Purchasable
{
    /**
     * Get the name of the purchasable item
     */
    public function getName(): string;

    /**
     * Get the slug for URL generation
     */
    public function getSlug(): string;

    /**
     * Get the base price of the item
     */
    public function getPrice(): float;

    /**
     * Get the effective price (considering sales, discounts, etc.)
     */
    public function getEffectivePrice(): float;

    /**
     * Get the primary image of the item
     */
    public function getImage(): ?string;

    /**
     * Get all images of the item
     */
    public function getImages(): array;

    /**
     * Get the current stock quantity
     */
    public function getStock(): int;

    /**
     * Check if the item is available for purchase
     */
    public function isAvailable(int $quantity = 1): bool;

    /**
     * Get the description of the item
     */
    public function getDescription(): ?string;

    /**
     * Get the brand of the item
     */
    public function getBrand(): ?string;

    /**
     * Get the model of the item
     */
    public function getModel(): ?string;

    /**
     * Get the category of the item
     */
    public function getCategory(): ?string;

    /**
     * Get the URL for the item
     */
    public function getUrl(): string;
}