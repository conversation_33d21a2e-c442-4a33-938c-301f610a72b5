<?php

namespace App\Livewire\Shop;

use App\Models\Cart;
use App\Models\CartItem;
use App\Services\CartService;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class ShoppingCart extends Component
{
    public $cart;
    public $cartItems = [];
    public $totalItems = 0;
    public $subtotal = 0;
    public $tax = 0;
    public $shipping = 0;
    public $total = 0;
    public $stockIssues = [];
    
    protected $listeners = [
        'cartUpdated' => 'loadCart',
        'itemAdded' => 'loadCart',
        'itemRemoved' => 'loadCart'
    ];
    
    public function mount()
    {
        $this->loadCart();
    }
    
    public function loadCart()
    {
        $cartService = app(CartService::class);
        $this->cart = $cartService->getCart();
        
        if ($this->cart) {
            $this->cartItems = $this->cart->items()
                ->with(['component', 'product'])
                ->where(function($query) {
                    $query->whereHas('component')
                          ->orWhereHas('product');
                })
                ->get();
            $this->calculateTotals();
            $this->checkStockAvailability();
        } else {
            $this->cartItems = collect();
            $this->totalItems = 0;
            $this->subtotal = 0;
            $this->tax = 0;
            $this->shipping = 0;
            $this->total = 0;
            $this->stockIssues = [];
        }
    }
    
    public function calculateTotals()
    {
        $this->totalItems = $this->cartItems->sum('quantity');
        $this->subtotal = $this->cartItems->sum(function($cartItem) {
            return $cartItem->getTotalPrice();
        });
        
        // Get discount if coupon is applied
        $discountAmount = session('discount_amount', 0);
        
        // Calculate tax (example: 10%)
        $this->tax = $this->subtotal * 0.1;
        
        // Calculate shipping (example: flat rate $5 for orders under $100)
        $this->shipping = $this->subtotal < 100 ? 5 : 0;
        
        // Calculate total with discount
        $this->total = $this->subtotal - $discountAmount + $this->tax + $this->shipping;
    }
    
    public function checkStockAvailability()
    {
        $this->stockIssues = [];
        
        foreach ($this->cartItems as $cartItem) {
            $item = $cartItem->item();
            
            if (!$item->isAvailable($cartItem->quantity)) {
                $this->stockIssues[] = [
                    'cart_item_id' => $cartItem->id,
                    'name' => $item->getName(),
                    'requested_quantity' => $cartItem->quantity,
                    'available_stock' => $item->getStock(),
                    'message' => "Only {$item->getStock()} units available for {$item->getName()}"
                ];
            }
        }
    }
    
    public function updateQuantity($cartItemId, $quantity)
    {
        $cartItem = CartItem::find($cartItemId);
        
        if (!$cartItem || $cartItem->cart_id != $this->cart->id) {
            return;
        }
        
        // Check if the requested quantity is valid
        if ($quantity < 1) {
            $this->removeItem($cartItemId);
            return;
        }
        
        // Use Purchasable interface to check availability
        $item = $cartItem->item();
        
        if (!$item->isAvailable($quantity)) {
            session()->flash('error', "Only {$item->getStock()} units available for {$item->getName()}.");
            return;
        }
        
        // Update the quantity
        $cartItem->quantity = $quantity;
        $cartItem->save();
        
        $this->loadCart();
        $this->dispatch('cartUpdated');
        session()->flash('message', 'Cart updated successfully!');
    }
    
    public function incrementQuantity($cartItemId)
    {
        $cartItem = CartItem::find($cartItemId);
        
        if (!$cartItem || $cartItem->cart_id != $this->cart->id) {
            return;
        }
        
        // Use Purchasable interface to check availability
        $item = $cartItem->item();
        $newQuantity = $cartItem->quantity + 1;
        
        if (!$item->isAvailable($newQuantity)) {
            session()->flash('error', "Only {$item->getStock()} units available for {$item->getName()}.");
            return;
        }
        
        // Increment the quantity
        $cartItem->quantity = $newQuantity;
        $cartItem->save();
        
        $this->loadCart();
        $this->dispatch('cartUpdated');
    }
    
    public function decrementQuantity($cartItemId)
    {
        $cartItem = CartItem::find($cartItemId);
        
        if (!$cartItem || $cartItem->cart_id != $this->cart->id) {
            return;
        }
        
        // If quantity is 1, remove the item
        if ($cartItem->quantity <= 1) {
            $this->removeItem($cartItemId);
            return;
        }
        
        // Decrement the quantity
        $cartItem->quantity--;
        $cartItem->save();
        
        $this->loadCart();
        $this->dispatch('cartUpdated');
    }
    
    public function removeItem($cartItemId)
    {
        $cartItem = CartItem::find($cartItemId);
        
        if (!$cartItem || $cartItem->cart_id != $this->cart->id) {
            return;
        }
        
        // Remove the item
        $cartItem->delete();
        
        $this->loadCart();
        session()->flash('message', 'Item removed from cart!');
    }
    
    public function clearCart()
    {
        if ($this->cart) {
            // Remove all items
            $this->cart->items()->delete();
            
            $this->loadCart();
            session()->flash('message', 'Cart cleared successfully!');
        }
    }
    
    public function checkout()
    {
        if (!Auth::check()) {
            return redirect()->route('login', ['redirect' => 'checkout']);
        }
        
        if ($this->totalItems === 0) {
            session()->flash('error', 'Your cart is empty.');
            return;
        }
        
        // Check if all items are in stock using Purchasable interface
        $stockIssues = [];
        foreach ($this->cartItems as $cartItem) {
            $item = $cartItem->item();
            
            if (!$item->isAvailable($cartItem->quantity)) {
                $stockIssues[] = $item->getName();
            }
        }
        
        if (!empty($stockIssues)) {
            $message = 'Some items in your cart are no longer available: ' . implode(', ', $stockIssues);
            session()->flash('error', $message);
            return;
        }
        
        return redirect()->route('shop.checkout');
    }
    
    public function getItemDisplayName($cartItem)
    {
        return $cartItem->getDisplayName();
    }
    
    public function getItemDisplayImage($cartItem)
    {
        return $cartItem->getDisplayImage();
    }
    
    public function getItemUrl($cartItem)
    {
        return $cartItem->item()->getUrl();
    }
    
    public function getItemBrand($cartItem)
    {
        return $cartItem->item()->getBrand();
    }
    
    public function render()
    {
        return view('livewire.shop.shopping-cart');
    }
}