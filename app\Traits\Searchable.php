<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

trait Searchable
{
    /**
     * Scope for searching items by term
     */
    public function scopeSearch(Builder $query, string $term): Builder
    {
        if (empty($term)) {
            return $query;
        }

        $searchTerm = '%' . $term . '%';

        return $query->where(function ($q) use ($searchTerm) {
            $q->where('name', 'LIKE', $searchTerm)
              ->orWhere('description', 'LIKE', $searchTerm)
              ->orWhere('brand', 'LIKE', $searchTerm)
              ->orWhere('model', 'LIKE', $searchTerm);

            // Search in SKU if the model has it
            if ($this->isFillable('sku')) {
                $q->orWhere('sku', 'LIKE', $searchTerm);
            }

            // Search in specs/attributes if the model has them
            if ($this->isFillable('specs')) {
                $q->orWhereJsonContains('specs', $searchTerm);
            }

            if ($this->isFillable('attributes')) {
                $q->orWhereJsonContains('attributes', $searchTerm);
            }
        });
    }

    /**
     * Scope for filtering by category
     */
    public function scopeByCategory(Builder $query, $categoryId): Builder
    {
        if (empty($categoryId)) {
            return $query;
        }

        return $query->where('category_id', $categoryId);
    }

    /**
     * Scope for filtering by brand
     */
    public function scopeByBrand(Builder $query, string $brand): Builder
    {
        if (empty($brand)) {
            return $query;
        }

        return $query->where('brand', $brand);
    }

    /**
     * Scope for filtering by price range
     */
    public function scopeByPriceRange(Builder $query, ?float $minPrice = null, ?float $maxPrice = null): Builder
    {
        if ($minPrice !== null) {
            $query->where('price', '>=', $minPrice);
        }

        if ($maxPrice !== null) {
            $query->where('price', '<=', $maxPrice);
        }

        return $query;
    }

    /**
     * Scope for filtering by availability
     */
    public function scopeAvailable(Builder $query): Builder
    {
        $query->where(function ($q) {
            // For models with is_active field
            if ($this->isFillable('is_active')) {
                $q->where('is_active', true);
            }

            // For models with status field
            if ($this->isFillable('status')) {
                $q->where('status', 'active');
            }

            // For models with stock tracking
            if ($this->isFillable('stock')) {
                $q->where('stock', '>', 0);
            }

            if ($this->isFillable('stock_quantity')) {
                $q->where('stock_quantity', '>', 0);
            }

            // For models with in_stock field
            if ($this->isFillable('in_stock')) {
                $q->where('in_stock', true);
            }
        });

        return $query;
    }

    /**
     * Scope for ordering by relevance
     */
    public function scopeOrderByRelevance(Builder $query, string $term = ''): Builder
    {
        if (empty($term)) {
            return $query->orderBy('name');
        }

        // Order by exact name match first, then partial matches
        return $query->orderByRaw("
            CASE 
                WHEN name = ? THEN 1
                WHEN name LIKE ? THEN 2
                WHEN brand = ? THEN 3
                WHEN model = ? THEN 4
                ELSE 5
            END, name ASC
        ", [$term, $term . '%', $term, $term]);
    }

    /**
     * Get search suggestions based on the term
     */
    public static function getSearchSuggestions(string $term, int $limit = 5): array
    {
        if (empty($term)) {
            return [];
        }

        $searchTerm = '%' . $term . '%';

        $suggestions = static::select('name', 'brand', 'model')
            ->where(function ($q) use ($searchTerm) {
                $q->where('name', 'LIKE', $searchTerm)
                  ->orWhere('brand', 'LIKE', $searchTerm)
                  ->orWhere('model', 'LIKE', $searchTerm);
            })
            ->limit($limit)
            ->get()
            ->map(function ($item) {
                return [
                    'name' => $item->name,
                    'brand' => $item->brand,
                    'model' => $item->model,
                ];
            })
            ->unique('name')
            ->values()
            ->toArray();

        return $suggestions;
    }

    /**
     * Get popular search terms
     */
    public static function getPopularSearchTerms(int $limit = 10): array
    {
        // This would typically come from a search analytics table
        // For now, return common brands and categories
        return static::select('brand')
            ->whereNotNull('brand')
            ->groupBy('brand')
            ->orderByRaw('COUNT(*) DESC')
            ->limit($limit)
            ->pluck('brand')
            ->toArray();
    }

    /**
     * Get searchable fields for this model
     */
    public function getSearchableFields(): array
    {
        $fields = ['name', 'description', 'brand', 'model'];

        if ($this->isFillable('sku')) {
            $fields[] = 'sku';
        }

        if ($this->isFillable('specs')) {
            $fields[] = 'specs';
        }

        if ($this->isFillable('attributes')) {
            $fields[] = 'attributes';
        }

        return $fields;
    }

    /**
     * Build search index for this item
     */
    public function buildSearchIndex(): array
    {
        $searchableFields = $this->getSearchableFields();
        $index = [];

        foreach ($searchableFields as $field) {
            $value = $this->getAttribute($field);
            
            if ($value !== null) {
                if (is_array($value) || is_object($value)) {
                    // Handle JSON fields
                    $index[$field] = $this->extractSearchableText($value);
                } else {
                    $index[$field] = (string) $value;
                }
            }
        }

        // Add computed fields
        $index['full_name'] = $this->getSearchDisplayName();
        $index['category_name'] = $this->getSearchCategoryName();
        $index['search_keywords'] = $this->getSearchKeywords();

        return $index;
    }

    /**
     * Extract searchable text from complex data structures
     */
    protected function extractSearchableText($data): string
    {
        if (is_string($data)) {
            return $data;
        }

        if (is_array($data)) {
            $text = [];
            foreach ($data as $key => $value) {
                if (is_string($value)) {
                    $text[] = $value;
                } elseif (is_string($key)) {
                    $text[] = $key;
                }
            }
            return implode(' ', $text);
        }

        if (is_object($data)) {
            return $this->extractSearchableText((array) $data);
        }

        return (string) $data;
    }

    /**
     * Get display name for search results
     */
    public function getSearchDisplayName(): string
    {
        $brand = $this->getBrand();
        $model = $this->getModel();
        $name = $this->getName();

        if ($brand && $model && $name !== $model) {
            return "{$brand} {$model} - {$name}";
        } elseif ($brand && $name) {
            return "{$brand} {$name}";
        }

        return $name ?? 'Unknown Item';
    }

    /**
     * Get category name for search
     */
    public function getSearchCategoryName(): ?string
    {
        if (method_exists($this, 'category') && $this->category) {
            return $this->category->name ?? null;
        }

        return $this->getCategory();
    }

    /**
     * Get search keywords for this item
     */
    public function getSearchKeywords(): array
    {
        $keywords = [];

        // Add brand and model as keywords
        if ($brand = $this->getBrand()) {
            $keywords[] = $brand;
        }

        if ($model = $this->getModel()) {
            $keywords[] = $model;
        }

        // Add category as keyword
        if ($category = $this->getSearchCategoryName()) {
            $keywords[] = $category;
        }

        // Extract keywords from specs/attributes
        if ($this->isFillable('specs') && $this->specs) {
            $keywords = array_merge($keywords, $this->extractKeywordsFromSpecs($this->specs));
        }

        if ($this->isFillable('attributes') && $this->attributes) {
            $keywords = array_merge($keywords, $this->extractKeywordsFromSpecs($this->attributes));
        }

        return array_unique(array_filter($keywords));
    }

    /**
     * Extract keywords from specs or attributes
     */
    protected function extractKeywordsFromSpecs($specs): array
    {
        $keywords = [];

        if (is_array($specs)) {
            foreach ($specs as $key => $value) {
                if (is_string($key)) {
                    $keywords[] = $key;
                }
                if (is_string($value)) {
                    $keywords[] = $value;
                }
            }
        }

        return $keywords;
    }

    /**
     * Format search result for API/JSON response
     */
    public function formatSearchResult(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->getSearchDisplayName(),
            'slug' => $this->getSlug(),
            'brand' => $this->getBrand(),
            'model' => $this->getModel(),
            'category' => $this->getSearchCategoryName(),
            'price' => $this->getPrice(),
            'effective_price' => $this->getEffectivePrice(),
            'image' => $this->getImage(),
            'thumbnail' => method_exists($this, 'getThumbnail') ? $this->getThumbnail() : $this->getImage(),
            'url' => $this->getUrl(),
            'is_available' => $this->isAvailable(),
            'stock' => $this->getStock(),
            'type' => strtolower(class_basename($this)),
            'search_score' => $this->search_score ?? 1.0,
        ];
    }

    /**
     * Advanced search with filters and sorting
     */
    public static function advancedSearch(array $params): Builder
    {
        $query = static::query();

        // Basic search term
        if (!empty($params['q'])) {
            $query->search($params['q']);
        }

        // Category filter
        if (!empty($params['category'])) {
            $query->byCategory($params['category']);
        }

        // Brand filter
        if (!empty($params['brand'])) {
            $query->byBrand($params['brand']);
        }

        // Price range filter
        if (!empty($params['min_price']) || !empty($params['max_price'])) {
            $query->byPriceRange($params['min_price'] ?? null, $params['max_price'] ?? null);
        }

        // Availability filter
        if (!empty($params['available'])) {
            $query->available();
        }

        // Featured items filter
        if (!empty($params['featured']) && static::make()->isFillable('featured')) {
            $query->where('featured', true);
        }

        // Sorting
        $sortBy = $params['sort_by'] ?? 'relevance';
        $sortOrder = $params['sort_order'] ?? 'asc';

        switch ($sortBy) {
            case 'price':
                $query->orderBy('price', $sortOrder);
                break;
            case 'name':
                $query->orderBy('name', $sortOrder);
                break;
            case 'created_at':
                $query->orderBy('created_at', $sortOrder);
                break;
            case 'relevance':
            default:
                if (!empty($params['q'])) {
                    $query->orderByRelevance($params['q']);
                } else {
                    $query->orderBy('name', 'asc');
                }
                break;
        }

        return $query;
    }

    /**
     * Get cached search suggestions
     */
    public static function getCachedSearchSuggestions(string $term, int $limit = 5): array
    {
        $cacheKey = 'search_suggestions_' . static::class . '_' . md5($term) . '_' . $limit;
        
        return Cache::remember($cacheKey, 300, function () use ($term, $limit) {
            return static::getSearchSuggestions($term, $limit);
        });
    }

    /**
     * Get search analytics data
     */
    public static function getSearchAnalytics(): array
    {
        $cacheKey = 'search_analytics_' . static::class;
        
        return Cache::remember($cacheKey, 3600, function () {
            return [
                'total_items' => static::count(),
                'available_items' => static::available()->count(),
                'brands' => static::whereNotNull('brand')->distinct('brand')->count(),
                'categories' => static::whereNotNull('category_id')->distinct('category_id')->count(),
                'price_range' => [
                    'min' => static::min('price'),
                    'max' => static::max('price'),
                    'avg' => static::avg('price'),
                ],
                'popular_brands' => static::getPopularSearchTerms(10),
            ];
        });
    }

    /**
     * Update search index (for external search engines)
     */
    public function updateSearchIndex(): void
    {
        $searchIndex = $this->buildSearchIndex();
        
        // Here you could integrate with external search services like:
        // - Elasticsearch
        // - Algolia
        // - MeiliSearch
        // - etc.
        
        // For now, we'll just cache the search index
        $cacheKey = 'search_index_' . static::class . '_' . $this->id;
        Cache::put($cacheKey, $searchIndex, 3600);
    }

    /**
     * Delete from search index
     */
    public function deleteFromSearchIndex(): void
    {
        $cacheKey = 'search_index_' . static::class . '_' . $this->id;
        Cache::forget($cacheKey);
    }

    /**
     * Boot the searchable trait
     */
    public static function bootSearchable(): void
    {
        static::saved(function ($model) {
            $model->updateSearchIndex();
        });

        static::deleted(function ($model) {
            $model->deleteFromSearchIndex();
        });
    }
}