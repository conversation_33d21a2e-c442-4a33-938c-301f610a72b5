# Design Document

## Overview

This design document outlines the architectural improvements for the Component and Product models in the Custom PC Building platform. The design focuses on creating a unified interface while maintaining model separation, improving data consistency, and enhancing performance through better relationships and caching strategies.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Interfaces"
        PI[Purchasable Interface]
        CI[Cartable Interface]
    end
    
    subgraph "Models"
        CM[Component Model]
        PM[Product Model]
        CAM[Cart Model]
        CIM[CartItem Model]
    end
    
    subgraph "Traits"
        CT[Cartable Trait]
        IT[ImageManagement Trait]
        ST[Searchable Trait]
    end
    
    subgraph "Services"
        CS[Cart Service]
        CPS[Compatibility Service]
        IS[Inventory Service]
    end
    
    PI --> CM
    PI --> PM
    CI --> CM
    CI --> PM
    
    CM --> CT
    PM --> CT
    CM --> IT
    PM --> IT
    CM --> ST
    PM --> ST
    
    CIM --> CM
    CIM --> PM
    CAM --> CIM
    
    CS --> CAM
    CPS --> CM
    IS --> CM
    IS --> PM
```

### Core Interface Design

#### Purchasable Interface

```php
interface Purchasable
{
    public function getName(): string;
    public function getSlug(): string;
    public function getPrice(): float;
    public function getEffectivePrice(): float;
    public function getImage(): ?string;
    public function getImages(): array;
    public function getStock(): int;
    public function isAvailable(int $quantity = 1): bool;
    public function getDescription(): ?string;
    public function getBrand(): ?string;
    public function getModel(): ?string;
    public function getCategory(): ?string;
    public function getUrl(): string;
}
```

#### Cartable Interface

```php
interface Cartable
{
    public function addToCart(int $quantity = 1): bool;
    public function getCartDisplayName(): string;
    public function getCartImage(): ?string;
    public function getCartPrice(): float;
    public function canAddToCart(int $quantity = 1): bool;
    public function getMaxCartQuantity(): int;
}
```

## Components and Interfaces

### Enhanced Component Model

#### Database Schema Improvements

```sql
-- Components table enhancements
ALTER TABLE components ADD COLUMN IF NOT EXISTS socket_type VARCHAR(50);
ALTER TABLE components ADD COLUMN IF NOT EXISTS chipset VARCHAR(100);
ALTER TABLE components ADD COLUMN IF NOT EXISTS form_factor VARCHAR(50);
ALTER TABLE components ADD COLUMN IF NOT EXISTS power_consumption INTEGER;
ALTER TABLE components ADD COLUMN IF NOT EXISTS cooling_type VARCHAR(50);
ALTER TABLE components ADD COLUMN IF NOT EXISTS memory_type VARCHAR(50);
ALTER TABLE components ADD COLUMN IF NOT EXISTS interface_type VARCHAR(50);
ALTER TABLE components ADD COLUMN IF NOT EXISTS warranty_months INTEGER DEFAULT 12;
ALTER TABLE components ADD COLUMN IF NOT EXISTS manufacturer_part_number VARCHAR(100);
ALTER TABLE components ADD COLUMN IF NOT EXISTS ean_code VARCHAR(20);
ALTER TABLE components ADD COLUMN IF NOT EXISTS weight_grams INTEGER;
ALTER TABLE components ADD COLUMN IF NOT EXISTS dimensions_json JSON;
ALTER TABLE components ADD COLUMN IF NOT EXISTS release_date DATE;
ALTER TABLE components ADD COLUMN IF NOT EXISTS discontinued_at TIMESTAMP NULL;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_components_category_active ON components(category_id, is_active);
CREATE INDEX IF NOT EXISTS idx_components_brand_model ON components(brand, model);
CREATE INDEX IF NOT EXISTS idx_components_socket_type ON components(socket_type);
CREATE INDEX IF NOT EXISTS idx_components_price_range ON components(price, is_active);
```

#### Component Model Implementation

```php
class Component extends Model implements Purchasable, Cartable
{
    use HasFactory, Cartable, ImageManagement, Searchable;

    protected $fillable = [
        'name', 'slug', 'description', 'category_id', 'brand', 'model',
        'price', 'stock', 'image', 'specs', 'is_featured', 'is_active',
        'socket_type', 'chipset', 'form_factor', 'power_consumption',
        'cooling_type', 'memory_type', 'interface_type', 'warranty_months',
        'manufacturer_part_number', 'ean_code', 'weight_grams',
        'dimensions_json', 'release_date', 'discontinued_at'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'stock' => 'integer',
        'specs' => 'array',
        'dimensions_json' => 'array',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'power_consumption' => 'integer',
        'warranty_months' => 'integer',
        'weight_grams' => 'integer',
        'release_date' => 'date',
        'discontinued_at' => 'datetime'
    ];

    // Purchasable Interface Implementation
    public function getName(): string
    {
        return $this->name;
    }

    public function getPrice(): float
    {
        return (float) $this->price;
    }

    public function getEffectivePrice(): float
    {
        // Components don't have sale prices, return regular price
        return $this->getPrice();
    }

    public function getStock(): int
    {
        return $this->stock ?? 0;
    }

    public function isAvailable(int $quantity = 1): bool
    {
        return $this->is_active && 
               !$this->discontinued_at && 
               $this->getStock() >= $quantity;
    }

    public function getUrl(): string
    {
        return route('shop.product', $this->slug);
    }

    // Component-specific methods
    public function isCompatibleWith(Component $other): bool
    {
        return app(CompatibilityService::class)->checkCompatibility($this, $other);
    }

    public function getPowerRequirement(): int
    {
        return $this->power_consumption ?? 0;
    }

    public function getFormFactor(): ?string
    {
        return $this->form_factor;
    }

    public function getSocketType(): ?string
    {
        return $this->socket_type;
    }
}
```

### Enhanced Product Model

#### Database Schema Improvements

```sql
-- Products table enhancements
ALTER TABLE products ADD COLUMN IF NOT EXISTS vendor_id BIGINT UNSIGNED;
ALTER TABLE products ADD COLUMN IF NOT EXISTS warranty_months INTEGER DEFAULT 12;
ALTER TABLE products ADD COLUMN IF NOT EXISTS shipping_weight_grams INTEGER;
ALTER TABLE products ADD COLUMN IF NOT EXISTS shipping_dimensions_json JSON;
ALTER TABLE products ADD COLUMN IF NOT EXISTS min_order_quantity INTEGER DEFAULT 1;
ALTER TABLE products ADD COLUMN IF NOT EXISTS max_order_quantity INTEGER;
ALTER TABLE products ADD COLUMN IF NOT EXISTS bulk_pricing_json JSON;
ALTER TABLE products ADD COLUMN IF NOT EXISTS tags_json JSON;
ALTER TABLE products ADD COLUMN IF NOT EXISTS seo_title VARCHAR(255);
ALTER TABLE products ADD COLUMN IF NOT EXISTS seo_description TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS condition ENUM('new', 'refurbished', 'used') DEFAULT 'new';
ALTER TABLE products ADD COLUMN IF NOT EXISTS origin_country VARCHAR(2);

-- Add foreign key for vendor
ALTER TABLE products ADD CONSTRAINT fk_products_vendor 
    FOREIGN KEY (vendor_id) REFERENCES users(id) ON DELETE SET NULL;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_products_vendor_status ON products(vendor_id, status);
CREATE INDEX IF NOT EXISTS idx_products_category_featured ON products(category_id, featured);
CREATE INDEX IF NOT EXISTS idx_products_price_range ON products(price, sale_price, status);
CREATE INDEX IF NOT EXISTS idx_products_condition ON products(condition, status);
```

#### Product Model Implementation

```php
class Product extends Model implements Purchasable, Cartable
{
    use HasFactory, Cartable, ImageManagement, Searchable;

    protected $fillable = [
        'name', 'slug', 'description', 'short_description', 'sku',
        'price', 'sale_price', 'stock_quantity', 'manage_stock', 'in_stock',
        'status', 'type', 'category', 'category_id', 'images', 'attributes',
        'weight', 'dimensions', 'brand', 'model', 'sort_order', 'featured',
        'meta_data', 'vendor_id', 'warranty_months', 'shipping_weight_grams',
        'shipping_dimensions_json', 'min_order_quantity', 'max_order_quantity',
        'bulk_pricing_json', 'tags_json', 'seo_title', 'seo_description',
        'condition', 'origin_country'
    ];

    protected $casts = [
        'price' => 'float',
        'sale_price' => 'float',
        'weight' => 'float',
        'stock_quantity' => 'integer',
        'sort_order' => 'integer',
        'manage_stock' => 'boolean',
        'in_stock' => 'boolean',
        'featured' => 'boolean',
        'images' => 'array',
        'attributes' => 'array',
        'dimensions' => 'array',
        'meta_data' => 'array',
        'shipping_dimensions_json' => 'array',
        'bulk_pricing_json' => 'array',
        'tags_json' => 'array',
        'warranty_months' => 'integer',
        'shipping_weight_grams' => 'integer',
        'min_order_quantity' => 'integer',
        'max_order_quantity' => 'integer'
    ];

    // Purchasable Interface Implementation
    public function getPrice(): float
    {
        return (float) $this->price;
    }

    public function getEffectivePrice(): float
    {
        return (float) ($this->sale_price ?? $this->price);
    }

    public function getStock(): int
    {
        return $this->stock_quantity ?? 0;
    }

    public function isAvailable(int $quantity = 1): bool
    {
        if (!$this->manage_stock) {
            return $this->status === 'active' && $this->in_stock;
        }

        return $this->status === 'active' && 
               $this->in_stock && 
               $this->getStock() >= $quantity &&
               $quantity >= $this->min_order_quantity &&
               ($this->max_order_quantity === null || $quantity <= $this->max_order_quantity);
    }

    public function getUrl(): string
    {
        return route('products.show', $this->slug);
    }

    // Product-specific methods
    public function vendor()
    {
        return $this->belongsTo(User::class, 'vendor_id');
    }

    public function getBulkPrice(int $quantity): float
    {
        if (!$this->bulk_pricing_json) {
            return $this->getEffectivePrice();
        }

        $bulkPricing = $this->bulk_pricing_json;
        $applicablePrice = $this->getEffectivePrice();

        foreach ($bulkPricing as $tier) {
            if ($quantity >= $tier['min_quantity']) {
                $applicablePrice = $tier['price'];
            }
        }

        return $applicablePrice;
    }

    public function getShippingWeight(): int
    {
        return $this->shipping_weight_grams ?? ($this->weight * 1000);
    }
}
```

## Data Models

### Shared Traits

#### Cartable Trait

```php
trait Cartable
{
    public function addToCart(int $quantity = 1): bool
    {
        $cartService = app(CartService::class);
        return $cartService->addItem($this, $quantity);
    }

    public function getCartDisplayName(): string
    {
        $brand = $this->getBrand();
        $model = $this->getModel();
        
        if ($brand && $model) {
            return "{$brand} {$model} - {$this->getName()}";
        }
        
        return $this->getName();
    }

    public function getCartImage(): ?string
    {
        return $this->getImage();
    }

    public function getCartPrice(): float
    {
        return $this->getEffectivePrice();
    }

    public function canAddToCart(int $quantity = 1): bool
    {
        return $this->isAvailable($quantity);
    }

    public function getMaxCartQuantity(): int
    {
        if (method_exists($this, 'max_order_quantity') && $this->max_order_quantity) {
            return min($this->getStock(), $this->max_order_quantity);
        }
        
        return $this->getStock();
    }
}
```

#### ImageManagement Trait

```php
trait ImageManagement
{
    public function getImage(): ?string
    {
        if (isset($this->images) && is_array($this->images) && !empty($this->images)) {
            return $this->images[0];
        }
        
        return $this->image ?? null;
    }

    public function getImages(): array
    {
        if (isset($this->images) && is_array($this->images)) {
            return $this->images;
        }
        
        if ($this->image) {
            return [$this->image];
        }
        
        return [];
    }

    public function getPrimaryImage(): string
    {
        return $this->getImage() ?? '/images/placeholder.jpg';
    }

    public function hasImages(): bool
    {
        return !empty($this->getImages());
    }
}
```

### Enhanced CartItem Model

```php
class CartItem extends Model
{
    protected $fillable = [
        'cart_id', 'component_id', 'product_id', 'item_type',
        'quantity', 'price', 'metadata'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'quantity' => 'integer',
        'metadata' => 'array'
    ];

    const TYPE_COMPONENT = 'component';
    const TYPE_PRODUCT = 'product';

    // Relationships
    public function cart()
    {
        return $this->belongsTo(Cart::class);
    }

    public function component()
    {
        return $this->belongsTo(Component::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    // Unified item access
    public function item(): Purchasable
    {
        return $this->item_type === self::TYPE_PRODUCT 
            ? $this->product 
            : $this->component;
    }

    // Helper methods
    public function getDisplayName(): string
    {
        return $this->item()->getCartDisplayName();
    }

    public function getDisplayImage(): ?string
    {
        return $this->item()->getCartImage();
    }

    public function getUnitPrice(): float
    {
        return (float) $this->price;
    }

    public function getTotalPrice(): float
    {
        return $this->getUnitPrice() * $this->quantity;
    }

    public function isInStock(): bool
    {
        return $this->item()->isAvailable($this->quantity);
    }

    public function getMaxQuantity(): int
    {
        return $this->item()->getMaxCartQuantity();
    }
}
```

## Error Handling

### Validation Rules

```php
// Component validation
class ComponentRequest extends FormRequest
{
    public function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'slug' => 'required|string|unique:components,slug,' . $this->id,
            'price' => 'required|numeric|min:0',
            'stock' => 'required|integer|min:0',
            'category_id' => 'required|exists:component_categories,id',
            'socket_type' => 'nullable|string|max:50',
            'power_consumption' => 'nullable|integer|min:0',
            'warranty_months' => 'integer|min:0|max:120'
        ];
    }
}

// Product validation
class ProductRequest extends FormRequest
{
    public function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'slug' => 'required|string|unique:products,slug,' . $this->id,
            'price' => 'required|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0|lt:price',
            'stock_quantity' => 'required_if:manage_stock,true|integer|min:0',
            'min_order_quantity' => 'integer|min:1',
            'max_order_quantity' => 'nullable|integer|gte:min_order_quantity',
            'vendor_id' => 'nullable|exists:users,id'
        ];
    }
}
```

## Testing Strategy

### Unit Tests Structure

```php
// Component tests
class ComponentTest extends TestCase
{
    public function test_implements_purchasable_interface()
    public function test_implements_cartable_interface()
    public function test_calculates_power_consumption()
    public function test_checks_compatibility()
    public function test_handles_stock_availability()
}

// Product tests  
class ProductTest extends TestCase
{
    public function test_implements_purchasable_interface()
    public function test_implements_cartable_interface()
    public function test_calculates_bulk_pricing()
    public function test_handles_vendor_relationship()
    public function test_manages_inventory_correctly()
}

// Integration tests
class CartIntegrationTest extends TestCase
{
    public function test_adds_components_to_cart()
    public function test_adds_products_to_cart()
    public function test_handles_mixed_cart_items()
    public function test_calculates_totals_correctly()
}
```

This design provides a solid foundation for improving both models while maintaining separation of concerns and enabling shared functionality through interfaces and traits.