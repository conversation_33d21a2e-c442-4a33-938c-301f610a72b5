<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use App\Contracts\Purchasable;
use App\Contracts\Cartable;
use App\Traits\Cartable as CartableTrait;
use App\Traits\ImageManagement;

class Product extends Model implements Purchasable, Cartable
{
    use HasFactory, CartableTrait, ImageManagement;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'short_description',
        'sku',
        'price',
        'sale_price',
        'stock_quantity',
        'manage_stock',
        'in_stock',
        'status',
        'type',
        'category',
        'category_id',
        'images',
        'attributes',
        'weight',
        'dimensions',
        'brand',
        'model',
        'sort_order',
        'featured',
        'meta_data',
        // New marketplace fields
        'warranty_months',
        'condition',
        'origin_country',
        'min_order_quantity',
        'max_order_quantity',
        'bulk_pricing_json',
        'low_stock_threshold',
        'seo_title',
        'seo_description',
        'tags_json',
        'shipping_weight_grams',
        'shipping_dimensions_json',
        'shipping_cost',
        'free_shipping',
        'manufacturer_part_number',
        'ean_code',
        'release_date',
        'discontinued_at'
    ];

    protected $casts = [
        'price' => 'float',
        'sale_price' => 'float',
        'weight' => 'float',
        'stock_quantity' => 'integer',
        'sort_order' => 'integer',
        'manage_stock' => 'boolean',
        'in_stock' => 'boolean',
        'featured' => 'boolean',
        'images' => 'array',
        'attributes' => 'array',
        'dimensions' => 'array',
        'meta_data' => 'array',
        // New marketplace field casts
        'warranty_months' => 'integer',
        'min_order_quantity' => 'integer',
        'max_order_quantity' => 'integer',
        'bulk_pricing_json' => 'array',
        'low_stock_threshold' => 'integer',
        'tags_json' => 'array',
        'shipping_weight_grams' => 'integer',
        'shipping_dimensions_json' => 'array',
        'shipping_cost' => 'decimal:2',
        'free_shipping' => 'boolean',
        'release_date' => 'date',
        'discontinued_at' => 'datetime'
    ];

    // Automatically generate slug when creating/updating
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            if (empty($product->slug)) {
                $product->slug = Str::slug($product->name);
            }
        });

        static::updating(function ($product) {
            if ($product->isDirty('name') && empty($product->slug)) {
                $product->slug = Str::slug($product->name);
            }
        });
    }

    // Relationships
    public function transactions()
    {
        return $this->hasMany(Transaction::class);
    }

    public function stockMovements()
    {
        return $this->hasMany(StockMovement::class);
    }

    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'category_id');
    }

    public function reviews()
    {
        return $this->hasMany(ProductReview::class);
    }

    public function approvedReviews()
    {
        return $this->hasMany(ProductReview::class)->approved();
    }

    // Scopes
    public function scopeActive(Builder $query)
    {
        return $query->where('status', 'active');
    }

    public function scopeInStock(Builder $query)
    {
        return $query->where('in_stock', true);
    }

    public function scopeFeatured(Builder $query)
    {
        return $query->where('featured', true);
    }

    public function scopeByCategory(Builder $query, string $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByBrand(Builder $query, string $brand)
    {
        return $query->where('brand', $brand);
    }

    public function scopeOnSale(Builder $query)
    {
        return $query->whereNotNull('sale_price')
                    ->whereColumn('sale_price', '<', 'price');
    }

    // Accessors & Mutators
    public function getEffectivePriceAttribute()
    {
        return $this->sale_price ?? $this->price;
    }

    public function getIsOnSaleAttribute()
    {
        return !is_null($this->sale_price) && $this->sale_price < $this->price;
    }

    public function getDiscountPercentageAttribute()
    {
        if (!$this->is_on_sale) {
            return 0;
        }

        return round((($this->price - $this->sale_price) / $this->price) * 100, 2);
    }

    public function getFormattedPriceAttribute()
    {
        return '₹' . number_format($this->effective_price, 2);
    }

    public function getOriginalPriceAttribute()
    {
        return '₹' . number_format($this->price, 2);
    }

    public function getPrimaryImageAttribute()
    {
        return $this->images[0] ?? '/images/placeholder-product.jpg';
    }

    // Methods
    public function updateStock(int $quantity, string $type = 'sale')
    {
        if (!$this->manage_stock) {
            return true;
        }

        $previousStock = $this->stock_quantity;
        $newQuantity = $type === 'sale' 
            ? $this->stock_quantity - $quantity 
            : $this->stock_quantity + $quantity;

        $this->update([
            'stock_quantity' => max(0, $newQuantity),
            'in_stock' => $newQuantity > 0
        ]);

        // Create stock movement record
        StockMovement::create([
            'product_id' => $this->id,
            'type' => $type,
            'quantity_change' => $type === 'sale' ? -$quantity : $quantity,
            'previous_stock' => $previousStock,
            'new_stock' => max(0, $newQuantity),
            'reason' => ucfirst($type) . ' transaction'
        ]);

        return true;
    }



    public function getStockStatus(): string
    {
        if (!$this->manage_stock) {
            return $this->in_stock ? 'In Stock' : 'Out of Stock';
        }

        if ($this->stock_quantity <= 0) {
            return 'Out of Stock';
        }

        if ($this->stock_quantity <= 5) {
            return 'Low Stock';
        }

        return 'In Stock';
    }

    // Review-related methods
    public function getAverageRating(): float
    {
        return ProductReview::getAverageRating($this->id);
    }

    public function getReviewCount(): int
    {
        return ProductReview::getReviewCount($this->id);
    }

    public function getReviewStats(): array
    {
        return ProductReview::getProductReviewStats($this->id);
    }

    public function hasUserReviewed(int $userId): bool
    {
        return $this->reviews()->where('user_id', $userId)->exists();
    }

    // Category-related methods (moved to flexible category system section)

    // Coupon-related methods
    public function isEligibleForCoupon(Coupon $coupon): bool
    {
        return $coupon->isApplicableToProducts([$this->id]);
    }

    // ========================================
    // Purchasable Interface Implementation
    // ========================================

    /**
     * Get the name of the purchasable item
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * Get the slug for URL generation
     */
    public function getSlug(): string
    {
        return $this->slug;
    }

    /**
     * Get the base price of the item
     */
    public function getPrice(): float
    {
        return (float) $this->price;
    }

    /**
     * Get the effective price (considering sales, discounts, etc.)
     * Handles sale pricing logic
     */
    public function getEffectivePrice(): float
    {
        return (float) ($this->sale_price ?? $this->price);
    }

    /**
     * Get the current stock quantity
     * Implements stock management with min/max order quantities
     */
    public function getStock(): int
    {
        return $this->stock_quantity ?? 0;
    }

    /**
     * Check if the item is available for purchase
     * Implements stock management with min/max order quantities
     */
    public function isAvailable(int $quantity = 1): bool
    {
        // Check if product is active and not discontinued
        if ($this->status !== 'active' || $this->discontinued_at) {
            return false;
        }

        // If stock management is disabled, just check in_stock flag
        if (!$this->manage_stock) {
            return $this->in_stock;
        }

        // Check stock availability
        if (!$this->in_stock || $this->getStock() < $quantity) {
            return false;
        }

        // Check minimum order quantity
        if ($quantity < $this->min_order_quantity) {
            return false;
        }

        // Check maximum order quantity if set
        if ($this->max_order_quantity && $quantity > $this->max_order_quantity) {
            return false;
        }

        return true;
    }

    /**
     * Get the description of the item
     */
    public function getDescription(): ?string
    {
        return $this->description;
    }

    /**
     * Get the brand of the item
     */
    public function getBrand(): ?string
    {
        return $this->brand;
    }

    /**
     * Get the model of the item
     */
    public function getModel(): ?string
    {
        return $this->model;
    }

    /**
     * Get the category of the item
     */
    public function getCategory(): ?string
    {
        return $this->category;
    }

    /**
     * Get the URL for the item
     */
    public function getUrl(): string
    {
        return route('products.show', $this->slug);
    }

    // ========================================
    // Product-Specific Methods
    // ========================================

    /**
     * Get bulk pricing for a specific quantity
     */
    public function getBulkPrice(int $quantity): float
    {
        if (!$this->bulk_pricing_json || empty($this->bulk_pricing_json)) {
            return $this->getEffectivePrice();
        }

        $bulkPricing = $this->bulk_pricing_json;
        $applicablePrice = $this->getEffectivePrice();

        // Sort bulk pricing by min_quantity descending to get the best applicable price
        usort($bulkPricing, function ($a, $b) {
            return $b['min_quantity'] <=> $a['min_quantity'];
        });

        foreach ($bulkPricing as $tier) {
            if ($quantity >= $tier['min_quantity']) {
                $applicablePrice = $tier['price'];
                break;
            }
        }

        return (float) $applicablePrice;
    }

    /**
     * Get shipping weight in grams
     */
    public function getShippingWeight(): int
    {
        return $this->shipping_weight_grams ?? (int) ($this->weight * 1000);
    }

    /**
     * Check if product qualifies for free shipping
     */
    public function hasFreeShipping(): bool
    {
        return $this->free_shipping;
    }

    /**
     * Get the warranty period in months
     */
    public function getWarrantyMonths(): int
    {
        return $this->warranty_months ?? 12;
    }

    /**
     * Check if product is on sale
     */
    public function isOnSale(): bool
    {
        return !is_null($this->sale_price) && $this->sale_price < $this->price;
    }

    /**
     * Get discount percentage if on sale
     */
    public function getDiscountPercentage(): float
    {
        if (!$this->isOnSale()) {
            return 0.0;
        }

        return round((($this->price - $this->sale_price) / $this->price) * 100, 2);
    }

    /**
     * Check if stock is low
     */
    public function isLowStock(): bool
    {
        if (!$this->manage_stock) {
            return false;
        }

        return $this->getStock() <= $this->low_stock_threshold;
    }

    /**
     * Get product tags
     */
    public function getTags(): array
    {
        return $this->tags_json ?? [];
    }

    /**
     * Check if product is discontinued
     */
    public function isDiscontinued(): bool
    {
        return !is_null($this->discontinued_at);
    }

    // ========================================
    // Cartable Interface Overrides
    // ========================================

    /**
     * Get the price for cart calculations
     * Implements bulk pricing logic for cart operations
     */
    public function getCartPrice(): float
    {
        // Default to effective price - bulk pricing will be calculated in cart service
        return $this->getEffectivePrice();
    }

    /**
     * Get the price for a specific quantity (bulk pricing)
     */
    public function getCartPriceForQuantity(int $quantity): float
    {
        return $this->getBulkPrice($quantity);
    }

    /**
     * Check if the item can be added to cart
     * Handles vendor-specific cart rules and min/max quantities
     */
    public function canAddToCart(int $quantity = 1): bool
    {
        return $this->isAvailable($quantity);
    }

    /**
     * Get the maximum quantity that can be added to cart
     */
    public function getMaxCartQuantity(): int
    {
        if ($this->max_order_quantity) {
            return min($this->getStock(), $this->max_order_quantity);
        }
        
        return $this->getStock();
    }

    /**
     * Get the minimum quantity that can be added to cart
     */
    public function getMinCartQuantity(): int
    {
        return $this->min_order_quantity ?? 1;
    }

    /**
     * Get cart display name with bulk pricing info if applicable
     */
    public function getCartDisplayName(): string
    {
        $brand = $this->getBrand();
        $model = $this->getModel();
        
        $name = '';
        if ($brand && $model) {
            $name = "{$brand} {$model} - {$this->getName()}";
        } else {
            $name = $this->getName();
        }

        // Add bulk pricing indicator if applicable
        if ($this->bulk_pricing_json && !empty($this->bulk_pricing_json)) {
            $name .= ' (Bulk pricing available)';
        }

        return $name;
    }

    /**
     * Check if bulk pricing is available
     */
    public function hasBulkPricing(): bool
    {
        return !empty($this->bulk_pricing_json);
    }

    /**
     * Get bulk pricing tiers
     */
    public function getBulkPricingTiers(): array
    {
        if (!$this->bulk_pricing_json) {
            return [];
        }

        // Sort by min_quantity ascending
        $tiers = $this->bulk_pricing_json;
        usort($tiers, function ($a, $b) {
            return $a['min_quantity'] <=> $b['min_quantity'];
        });

        return $tiers;
    }

    /**
     * Get the next bulk pricing tier for a given quantity
     */
    public function getNextBulkTier(int $currentQuantity): ?array
    {
        $tiers = $this->getBulkPricingTiers();
        
        foreach ($tiers as $tier) {
            if ($currentQuantity < $tier['min_quantity']) {
                return $tier;
            }
        }

        return null;
    }

    /**
     * Calculate savings from bulk pricing
     */
    public function getBulkSavings(int $quantity): float
    {
        $regularPrice = $this->getEffectivePrice();
        $bulkPrice = $this->getBulkPrice($quantity);
        
        return ($regularPrice - $bulkPrice) * $quantity;
    }

    // ========================================
    // SEO and Metadata Handling
    // ========================================

    /**
     * Get SEO title with fallback to product name
     */
    public function getSeoTitle(): string
    {
        return $this->seo_title ?? $this->name;
    }

    /**
     * Get SEO description with fallback to short description
     */
    public function getSeoDescription(): string
    {
        return $this->seo_description ?? $this->short_description ?? substr(strip_tags($this->description), 0, 160);
    }

    /**
     * Get meta keywords from tags
     */
    public function getMetaKeywords(): string
    {
        $tags = $this->getTags();
        return implode(', ', $tags);
    }

    /**
     * Get structured data for product
     */
    public function getStructuredData(): array
    {
        return [
            '@context' => 'https://schema.org/',
            '@type' => 'Product',
            'name' => $this->getName(),
            'description' => $this->getDescription(),
            'brand' => [
                '@type' => 'Brand',
                'name' => $this->getBrand()
            ],
            'model' => $this->getModel(),
            'sku' => $this->sku,
            'image' => $this->getImages(),
            'offers' => [
                '@type' => 'Offer',
                'price' => $this->getEffectivePrice(),
                'priceCurrency' => 'INR',
                'availability' => $this->isAvailable() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock',
                'condition' => 'https://schema.org/' . ucfirst($this->condition ?? 'new') . 'Condition'
            ],
            'aggregateRating' => [
                '@type' => 'AggregateRating',
                'ratingValue' => $this->getAverageRating(),
                'reviewCount' => $this->getReviewCount()
            ]
        ];
    }

    // ========================================
    // Flexible Category System
    // ========================================

    /**
     * Get category hierarchy as breadcrumb
     */
    public function getCategoryBreadcrumb(): array
    {
        if (!$this->category) {
            return [];
        }

        return $this->category->getBreadcrumb();
    }

    /**
     * Get category path as string
     */
    public function getCategoryPath(): string
    {
        if (!$this->category) {
            return 'Uncategorized';
        }

        $breadcrumb = $this->getCategoryBreadcrumb();
        return collect($breadcrumb)->pluck('name')->implode(' > ');
    }

    /**
     * Get all parent categories
     */
    public function getParentCategories(): array
    {
        if (!$this->category) {
            return [];
        }

        return $this->category->getParents();
    }

    /**
     * Check if product belongs to a specific category or its children
     */
    public function belongsToCategory(int $categoryId): bool
    {
        if ($this->category_id === $categoryId) {
            return true;
        }

        $parentCategories = $this->getParentCategories();
        return collect($parentCategories)->contains('id', $categoryId);
    }

    /**
     * Get related products from same category
     */
    public function getRelatedProducts(int $limit = 4)
    {
        return static::where('category_id', $this->category_id)
            ->where('id', '!=', $this->id)
            ->where('status', 'active')
            ->inStock()
            ->limit($limit)
            ->get();
    }

    /**
     * Get products from same brand
     */
    public function getSameBrandProducts(int $limit = 4)
    {
        return static::where('brand', $this->brand)
            ->where('id', '!=', $this->id)
            ->where('status', 'active')
            ->inStock()
            ->limit($limit)
            ->get();
    }

    // ========================================
    // Advanced Metadata Handling
    // ========================================

    /**
     * Get custom attributes as key-value pairs
     */
    public function getCustomAttributes(): array
    {
        return $this->attributes ?? [];
    }

    /**
     * Get a specific custom attribute
     */
    public function getCustomAttribute(string $key, $default = null)
    {
        $attributes = $this->getCustomAttributes();
        return $attributes[$key] ?? $default;
    }

    /**
     * Set a custom attribute
     */
    public function setCustomAttribute(string $key, $value): void
    {
        $attributes = $this->getCustomAttributes();
        $attributes[$key] = $value;
        $this->attributes = $attributes;
    }

    /**
     * Get shipping dimensions
     */
    public function getShippingDimensions(): array
    {
        return $this->shipping_dimensions_json ?? [];
    }

    /**
     * Get formatted shipping dimensions
     */
    public function getFormattedShippingDimensions(): string
    {
        $dimensions = $this->getShippingDimensions();
        
        if (empty($dimensions)) {
            return 'Not specified';
        }

        return sprintf(
            '%s x %s x %s cm',
            $dimensions['length'] ?? '0',
            $dimensions['width'] ?? '0',
            $dimensions['height'] ?? '0'
        );
    }

    /**
     * Calculate shipping cost based on weight and dimensions
     */
    public function calculateShippingCost(): float
    {
        if ($this->hasFreeShipping()) {
            return 0.0;
        }

        if ($this->shipping_cost) {
            return (float) $this->shipping_cost;
        }

        // Default shipping calculation based on weight
        $weight = $this->getShippingWeight();
        $baseRate = 50.0; // Base rate in INR
        $perKgRate = 25.0; // Per kg rate in INR

        return $baseRate + (($weight / 1000) * $perKgRate);
    }

    /**
     * Get product condition display name
     */
    public function getConditionDisplayName(): string
    {
        return match($this->condition) {
            'new' => 'Brand New',
            'refurbished' => 'Refurbished',
            'used' => 'Used',
            default => 'New'
        };
    }

    /**
     * Get origin country display name
     */
    public function getOriginCountryName(): string
    {
        if (!$this->origin_country) {
            return 'Not specified';
        }

        // You can expand this with a country code to name mapping
        $countries = [
            'IN' => 'India',
            'US' => 'United States',
            'CN' => 'China',
            'JP' => 'Japan',
            'KR' => 'South Korea',
            'TW' => 'Taiwan',
            'DE' => 'Germany',
            // Add more as needed
        ];

        return $countries[$this->origin_country] ?? $this->origin_country;
    }

    /**
     * Get warranty information
     */
    public function getWarrantyInfo(): array
    {
        return [
            'months' => $this->getWarrantyMonths(),
            'years' => round($this->getWarrantyMonths() / 12, 1),
            'display' => $this->getWarrantyMonths() >= 12 
                ? round($this->getWarrantyMonths() / 12, 1) . ' year(s)'
                : $this->getWarrantyMonths() . ' month(s)'
        ];
    }
}