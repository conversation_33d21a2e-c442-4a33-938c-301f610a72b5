<?php

namespace Tests\Feature\Integration;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\User;
use App\Models\Cart;
use App\Models\CartItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CartIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Component $component;
    protected ComponentCategory $cpuCategory;

    protected function setUp(): void
    {
        parent::setUp();

        // Create user
        $this->user = User::factory()->create();

        // Create component category
        $this->cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU', 
            'slug' => 'cpu'
        ]);

        // Create component
        $this->component = Component::factory()->create([
            'name' => 'Test CPU',
            'slug' => 'test-cpu',
            'category_id' => $this->cpuCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 299.99,
            'stock' => 10,
            'socket_type' => 'LGA1700',
            'power_consumption' => 65,
            'is_active' => true
        ]);
    }

    /** @test */
    public function user_can_add_component_to_cart()
    {
        $this->actingAs($this->user);

        $response = $this->postJson('/api/cart', [
            'component_id' => $this->component->id,
            'quantity' => 1
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true
        ]);

        $this->assertDatabaseHas('carts', [
            'user_id' => $this->user->id,
        ]);

        $this->assertDatabaseHas('cart_items', [
            'component_id' => $this->component->id,
            'quantity' => 1
        ]);
    }

    /** @test */
    public function user_cannot_add_invalid_component_to_cart()
    {
        $this->actingAs($this->user);

        $response = $this->postJson('/api/cart', [
            'component_id' => 9999, // Non-existent component
            'quantity' => 1
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['component_id']);
    }

    /** @test */
    public function user_cannot_add_zero_or_negative_quantity_to_cart()
    {
        $this->actingAs($this->user);

        // Test zero quantity
        $response = $this->postJson('/api/cart', [
            'component_id' => $this->component->id,
            'quantity' => 0
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['quantity']);

        // Test negative quantity
        $response = $this->postJson('/api/cart', [
            'component_id' => $this->component->id,
            'quantity' => -1
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['quantity']);
    }

    /** @test */
    public function user_cannot_add_more_than_available_stock()
    {
        $this->actingAs($this->user);

        $response = $this->postJson('/api/cart', [
            'component_id' => $this->component->id,
            'quantity' => 20 // More than available stock (10)
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['quantity']);
    }

    /** @test */
    public function user_can_update_cart_item_quantity()
    {
        $this->actingAs($this->user);

        // First add item to cart
        $this->postJson('/api/cart', [
            'component_id' => $this->component->id,
            'quantity' => 1
        ]);

        // Get the cart item
        $cart = Cart::where('user_id', $this->user->id)->first();
        $cartItem = CartItem::where('cart_id', $cart->id)
            ->where('component_id', $this->component->id)
            ->first();

        // Update quantity
        $response = $this->putJson("/api/cart/{$cartItem->id}", [
            'quantity' => 3
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true
        ]);

        $this->assertDatabaseHas('cart_items', [
            'id' => $cartItem->id,
            'quantity' => 3
        ]);
    }

    /** @test */
    public function user_can_remove_item_from_cart()
    {
        $this->actingAs($this->user);

        // First add item to cart
        $this->postJson('/api/cart', [
            'component_id' => $this->component->id,
            'quantity' => 1
        ]);

        // Get the cart item
        $cart = Cart::where('user_id', $this->user->id)->first();
        $cartItem = CartItem::where('cart_id', $cart->id)
            ->where('component_id', $this->component->id)
            ->first();

        // Remove item
        $response = $this->deleteJson("/api/cart/{$cartItem->id}");

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true
        ]);

        $this->assertDatabaseMissing('cart_items', [
            'id' => $cartItem->id
        ]);
    }

    /** @test */
    public function user_can_view_cart_contents()
    {
        $this->actingAs($this->user);

        // Add item to cart
        $this->postJson('/api/cart', [
            'component_id' => $this->component->id,
            'quantity' => 2
        ]);

        // View cart
        $response = $this->getJson('/api/cart');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'cart' => [
                'id',
                'user_id',
                'items' => [
                    '*' => [
                        'id',
                        'component_id',
                        'quantity',
                        'component' => [
                            'id',
                            'name',
                            'price'
                        ]
                    ]
                ],
                'total'
            ]
        ]);

        $response->assertJsonPath('cart.items.0.component_id', $this->component->id);
        $response->assertJsonPath('cart.items.0.quantity', 2);
    }

    /** @test */
    public function cart_total_is_calculated_correctly()
    {
        $this->actingAs($this->user);

        // Create another component
        $component2 = Component::factory()->create([
            'name' => 'Test Component 2',
            'slug' => 'test-component-2',
            'category_id' => $this->cpuCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model 2',
            'price' => 199.99,
            'stock' => 5,
            'socket_type' => 'LGA1700',
            'power_consumption' => 45,
            'is_active' => true
        ]);

        // Add items to cart
        $this->postJson('/api/cart', [
            'component_id' => $this->component->id,
            'quantity' => 2
        ]);

        $this->postJson('/api/cart', [
            'component_id' => $component2->id,
            'quantity' => 1
        ]);

        // View cart
        $response = $this->getJson('/api/cart');

        $response->assertStatus(200);
        
        // Calculate expected total: (299.99 * 2) + (199.99 * 1) = 799.97
        $expectedTotal = (299.99 * 2) + (199.99 * 1);
        $response->assertJsonPath('cart.total', $expectedTotal);
    }

    /** @test */
    public function guest_user_cannot_access_cart_api()
    {
        // Not logged in
        $response = $this->getJson('/api/cart');
        $response->assertStatus(401);

        $response = $this->postJson('/api/cart', [
            'component_id' => $this->component->id,
            'quantity' => 1
        ]);
        $response->assertStatus(401);
    }
}