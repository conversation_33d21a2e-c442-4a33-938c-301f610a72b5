

<?php $__env->startSection('title', 'Make Payment'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow-md p-6 md:p-8">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">Make Payment</h2>
                <p class="text-gray-600">Choose your payment method and complete your transaction securely</p>
            </div>
            
            <?php if(session('error')): ?>
                <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="text-red-800"><?php echo e(session('error')); ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if(session('success')): ?>
                <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="text-green-800"><?php echo e(session('success')); ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if (isset($component)) { $__componentOriginal521737e9b607bba4b5be5b3795137447 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal521737e9b607bba4b5be5b3795137447 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.payment-form','data' => ['action' => route('payment.store'),'gateways' => $gatewayOptions,'selectedGateway' => old('gateway'),'amount' => old('amount', request('amount', '')),'currency' => old('currency', 'INR'),'description' => old('description', request('description', ''))]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('payment-form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['action' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('payment.store')),'gateways' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($gatewayOptions),'selectedGateway' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(old('gateway')),'amount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(old('amount', request('amount', ''))),'currency' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(old('currency', 'INR')),'description' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(old('description', request('description', '')))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal521737e9b607bba4b5be5b3795137447)): ?>
<?php $attributes = $__attributesOriginal521737e9b607bba4b5be5b3795137447; ?>
<?php unset($__attributesOriginal521737e9b607bba4b5be5b3795137447); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal521737e9b607bba4b5be5b3795137447)): ?>
<?php $component = $__componentOriginal521737e9b607bba4b5be5b3795137447; ?>
<?php unset($__componentOriginal521737e9b607bba4b5be5b3795137447); ?>
<?php endif; ?>
        </div>
        
        <!-- Security badges -->
        <div class="mt-8 text-center">
            <div class="flex items-center justify-center space-x-6 text-sm text-gray-600">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.586-3.414l-2.828-2.828a2 2 0 00-2.828 0l-5.656 5.656a2 2 0 000 2.828l2.828 2.828a2 2 0 002.828 0L19 9a2 2 0 000-2.828z"></path>
                    </svg>
                    <span>SSL Secured</span>
                </div>
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                    <span>PCI Compliant</span>
                </div>
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-purple-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.586-3.414l-2.828-2.828a2 2 0 00-2.828 0l-5.656 5.656a2 2 0 000 2.828l2.828 2.828a2 2 0 002.828 0L19 9a2 2 0 000-2.828z"></path>
                    </svg>
                    <span>Bank Grade Security</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Payment loading overlay -->
    <?php if (isset($component)) { $__componentOriginal8784402a66938cc1e47ad190409edefb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8784402a66938cc1e47ad190409edefb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.payment-loading','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('payment-loading'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8784402a66938cc1e47ad190409edefb)): ?>
<?php $attributes = $__attributesOriginal8784402a66938cc1e47ad190409edefb; ?>
<?php unset($__attributesOriginal8784402a66938cc1e47ad190409edefb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8784402a66938cc1e47ad190409edefb)): ?>
<?php $component = $__componentOriginal8784402a66938cc1e47ad190409edefb; ?>
<?php unset($__componentOriginal8784402a66938cc1e47ad190409edefb); ?>
<?php endif; ?>
</div>

<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
<script src="<?php echo e(asset('js/payment-validation.js')); ?>"></script>
<script>
// Gateway-specific payment handlers
window.handleRazorpayPayment = function(paymentData, transactionId) {
    PaymentLoading.updateMessage('Opening Razorpay', 'Please complete your payment in the popup window');
    
    const options = {
        ...paymentData,
        handler: function(response) {
            PaymentLoading.updateMessage('Verifying Payment', 'Please wait while we verify your payment...');
            verifyRazorpayPayment(response, transactionId);
        },
        modal: {
            ondismiss: function() {
                PaymentLoading.hide();
                showPaymentError('Payment was cancelled by user');
            }
        }
    };
    
    const rzp = new Razorpay(options);
    rzp.open();
};

window.handleCashfreePayment = function(paymentData) {
    PaymentLoading.updateMessage('Redirecting to Cashfree', 'You will be redirected to complete your payment');
    
    // Create form and submit to Cashfree
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = paymentData.payment_session_id ? paymentData.redirect_url : '#';
    
    Object.keys(paymentData).forEach(key => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = paymentData[key];
        form.appendChild(input);
    });
    
    document.body.appendChild(form);
    form.submit();
};

async function verifyRazorpayPayment(response, transactionId) {
    try {
        const verifyResponse = await fetch('<?php echo e(route("payment.verify")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                transaction_id: transactionId,
                razorpay_payment_id: response.razorpay_payment_id,
                razorpay_order_id: response.razorpay_order_id,
                razorpay_signature: response.razorpay_signature
            })
        });
        
        const result = await verifyResponse.json();
        
        if (result.verified) {
            PaymentLoading.updateMessage('Payment Successful!', 'Redirecting to success page...');
            setTimeout(() => {
                window.location.href = '/payment/success/' + transactionId;
            }, 1500);
        } else {
            PaymentLoading.hide();
            window.location.href = '/payment/failed/' + transactionId;
        }
    } catch (error) {
        PaymentLoading.hide();
        showPaymentError('Payment verification failed: ' + error.message);
    }
}

function showPaymentError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'fixed top-4 right-4 bg-red-50 border border-red-200 rounded-lg p-4 shadow-lg z-50 max-w-sm';
    errorDiv.innerHTML = `
        <div class="flex items-start">
            <svg class="w-5 h-5 text-red-400 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div class="flex-1">
                <p class="text-sm font-medium text-red-800">Payment Error</p>
                <p class="text-sm text-red-700 mt-1">${message}</p>
            </div>
            <button type="button" class="ml-2 text-red-400 hover:text-red-600" onclick="this.parentElement.parentElement.remove()">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    `;
    
    document.body.appendChild(errorDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (errorDiv.parentElement) {
            errorDiv.remove();
        }
    }, 5000);
}

// Listen for payment form validation completion
document.addEventListener('paymentFormValidated', function(event) {
    PaymentLoading.show('Initiating Payment', 'Setting up your payment...');
    
    const formData = event.detail.formData;
    const data = Object.fromEntries(formData);
    
    processPayment(data);
});

// Listen for payment initiation events from the form component
document.addEventListener('paymentInitiated', function(event) {
    PaymentLoading.show('Initiating Payment', 'Setting up your payment...');
});

// Listen for gateway selection changes
document.addEventListener('gatewayChanged', function(event) {
    console.log('Gateway changed to:', event.detail.gateway);
    // You can add gateway-specific UI updates here
});

async function processPayment(data) {
    try {
        const response = await fetch('<?php echo e(route("payment.store")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Handle different gateways
            if (data.gateway === 'razorpay') {
                handleRazorpayPayment(result.payment_data, result.transaction_id);
            } else if (data.gateway === 'cashfree') {
                handleCashfreePayment(result.payment_data);
            } else if (data.gateway === 'payumoney') {
                // PayUmoney will redirect to form submission page
                window.location.href = result.redirect_url || '/payment/payumoney-redirect';
            }
        } else {
            PaymentLoading.hide();
            showPaymentError(result.error?.message || 'Payment initiation failed');
        }
    } catch (error) {
        PaymentLoading.hide();
        showPaymentError('Network error: ' + error.message);
    }
}

function handleRazorpayPayment(paymentData, transactionId) {
    const options = {
        ...paymentData,
        handler: function(response) {
            verifyRazorpayPayment(response, transactionId);
        },
        modal: {
            ondismiss: function() {
                alert('Payment cancelled');
            }
        }
    };
    
    const rzp = new Razorpay(options);
    rzp.open();
}

async function verifyRazorpayPayment(response, transactionId) {
    try {
        const verifyResponse = await fetch('<?php echo e(route("payment.verify")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                transaction_id: transactionId,
                razorpay_payment_id: response.razorpay_payment_id,
                razorpay_order_id: response.razorpay_order_id,
                razorpay_signature: response.razorpay_signature
            })
        });
        
        const result = await verifyResponse.json();
        
        if (result.verified) {
            window.location.href = '/payment/success/' + transactionId;
        } else {
            window.location.href = '/payment/failed/' + transactionId;
        }
    } catch (error) {
        alert('Verification failed: ' + error.message);
    }
}

function handleCashfreePayment(paymentData) {
    // Implement Cashfree payment handling
    console.log('Cashfree payment data:', paymentData);
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\lara\www\pc-builder\resources\views/payments/create.blade.php ENDPATH**/ ?>