<?php

namespace Tests\Unit\Requests;

use App\Http\Requests\ComponentRequest;
use App\Http\Requests\ProductRequest;
use App\Models\ComponentCategory;
use App\Models\ProductCategory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use Tests\TestCase;

class RequestValidationTest extends TestCase
{
    use RefreshDatabase;

    protected ComponentCategory $cpuCategory;
    protected ComponentCategory $motherboardCategory;
    protected ProductCategory $productCategory;

    protected function setUp(): void
    {
        parent::setUp();

        // Create component categories
        $this->cpuCategory = ComponentCategory::factory()->create(['name' => 'CPU', 'slug' => 'cpu']);
        $this->motherboardCategory = ComponentCategory::factory()->create(['name' => 'Motherboard', 'slug' => 'motherboard']);
        
        // Create product category
        $this->productCategory = ProductCategory::factory()->create();
    }

    /** @test */
    public function component_request_validates_technical_specifications()
    {
        // Create a new ComponentRequest instance
        $request = new ComponentRequest();
        
        // Get validation rules
        $rules = $request->rules();
        
        // Test CPU validation
        $data = [
            'name' => 'Test CPU',
            'slug' => 'test-cpu',
            'category_id' => $this->cpuCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 299.99,
            'stock' => 10,
            // Missing socket_type and power_consumption
        ];
        
        $validator = Validator::make($data, $rules);
        $this->assertTrue($validator->fails());
        
        // Add the required fields for CPU
        $data['socket_type'] = 'LGA1700';
        $data['power_consumption'] = 65;
        
        // Create a new validator with the updated data
        $validator = Validator::make($data, $rules);
        
        // The validator should still fail because we need to apply the withValidator method
        // which contains the category-specific validation logic
        $this->assertTrue($validator->fails());
        
        // Create a mock request with the data
        $request = $this->getMockBuilder(ComponentRequest::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['all'])
            ->getMock();
        
        $request->expects($this->any())
            ->method('all')
            ->willReturn($data);
        
        // Apply the withValidator method manually
        $validator = Validator::make($data, $rules);
        $request->withValidator($validator);
        
        // Now the validation should pass
        $this->assertFalse($validator->fails());
    }

    /** @test */
    public function component_request_validates_compatibility_requirements()
    {
        // Create a new ComponentRequest instance
        $request = new ComponentRequest();
        
        // Get validation rules
        $rules = $request->rules();
        
        // Test discontinued component validation
        $data = [
            'name' => 'Discontinued Component',
            'slug' => 'discontinued-component',
            'category_id' => $this->cpuCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 299.99,
            'stock' => 10,
            'socket_type' => 'LGA1700',
            'power_consumption' => 65,
            'is_active' => true,
            'discontinued_at' => now()->subDay()->format('Y-m-d')
        ];
        
        // Create a mock request with the data
        $request = $this->getMockBuilder(ComponentRequest::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['all'])
            ->getMock();
        
        $request->expects($this->any())
            ->method('all')
            ->willReturn($data);
        
        // Apply the withValidator method manually
        $validator = Validator::make($data, $rules);
        $request->withValidator($validator);
        
        // Validation should fail because a discontinued component cannot be active
        $this->assertTrue($validator->fails());
        $this->assertTrue($validator->errors()->has('is_active'));
        
        // Fix the data
        $data['is_active'] = false;
        
        // Create a new mock request with the updated data
        $request = $this->getMockBuilder(ComponentRequest::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['all'])
            ->getMock();
        
        $request->expects($this->any())
            ->method('all')
            ->willReturn($data);
        
        // Apply the withValidator method manually
        $validator = Validator::make($data, $rules);
        $request->withValidator($validator);
        
        // Now the validation should pass
        $this->assertFalse($validator->fails());
    }

    /** @test */
    public function product_request_validates_bulk_pricing()
    {
        // Create a new ProductRequest instance
        $request = new ProductRequest();
        
        // Get validation rules
        $rules = $request->rules();
        
        // Test bulk pricing validation
        $data = [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->productCategory->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'bulk_pricing_json' => [
                ['quantity' => 5, 'price' => 90],
                ['quantity' => 5, 'price' => 85], // Duplicate quantity
                ['quantity' => 20, 'price' => 80]
            ]
        ];
        
        // Create a mock request with the data
        $request = $this->getMockBuilder(ProductRequest::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['all'])
            ->getMock();
        
        $request->expects($this->any())
            ->method('all')
            ->willReturn($data);
        
        // Apply the withValidator method manually
        $validator = Validator::make($data, $rules);
        $request->withValidator($validator);
        
        // Validation should fail because of duplicate quantities
        $this->assertTrue($validator->fails());
        $this->assertTrue($validator->errors()->has('bulk_pricing_json'));
        
        // Fix the data with unique quantities but not in ascending order
        $data['bulk_pricing_json'] = [
            ['quantity' => 20, 'price' => 80],
            ['quantity' => 5, 'price' => 90],
            ['quantity' => 10, 'price' => 85]
        ];
        
        // Create a new mock request with the updated data
        $request = $this->getMockBuilder(ProductRequest::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['all'])
            ->getMock();
        
        $request->expects($this->any())
            ->method('all')
            ->willReturn($data);
        
        // Apply the withValidator method manually
        $validator = Validator::make($data, $rules);
        $request->withValidator($validator);
        
        // Validation should still fail because quantities are not in ascending order
        $this->assertTrue($validator->fails());
        $this->assertTrue($validator->errors()->has('bulk_pricing_json'));
        
        // Fix the data with ascending quantities but not decreasing per-unit prices
        $data['bulk_pricing_json'] = [
            ['quantity' => 5, 'price' => 90], // 18 per unit
            ['quantity' => 10, 'price' => 200], // 20 per unit - should be less than 18
            ['quantity' => 20, 'price' => 400] // 20 per unit - should be less than 18
        ];
        
        // Create a new mock request with the updated data
        $request = $this->getMockBuilder(ProductRequest::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['all'])
            ->getMock();
        
        $request->expects($this->any())
            ->method('all')
            ->willReturn($data);
        
        // Apply the withValidator method manually
        $validator = Validator::make($data, $rules);
        $request->withValidator($validator);
        
        // Validation should still fail because per-unit prices are not decreasing
        $this->assertTrue($validator->fails());
        $this->assertTrue($validator->errors()->has('bulk_pricing_json'));
        
        // Fix the data with valid bulk pricing
        $data['bulk_pricing_json'] = [
            ['quantity' => 5, 'price' => 450], // 90 per unit
            ['quantity' => 10, 'price' => 850], // 85 per unit
            ['quantity' => 20, 'price' => 1600] // 80 per unit
        ];
        
        // Create a new mock request with the updated data
        $request = $this->getMockBuilder(ProductRequest::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['all'])
            ->getMock();
        
        $request->expects($this->any())
            ->method('all')
            ->willReturn($data);
        
        // Apply the withValidator method manually
        $validator = Validator::make($data, $rules);
        $request->withValidator($validator);
        
        // Now the validation should pass
        $this->assertFalse($validator->fails());
    }
}