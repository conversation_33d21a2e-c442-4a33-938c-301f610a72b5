<?php

namespace Tests\Feature\Integration;

use App\Http\Requests\ProductRequest;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use Tests\TestCase;

class ProductRequestValidationTest extends TestCase
{
    use RefreshDatabase;

    protected User $adminUser;
    protected ProductCategory $category;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->adminUser = User::factory()->create([
            'role' => 'admin'
        ]);

        // Create product category
        $this->category = ProductCategory::factory()->create();
    }

    /** @test */
    public function it_validates_basic_product_fields()
    {
        $this->actingAs($this->adminUser);

        // Test with missing required fields
        $response = $this->postJson('/api/admin/products', []);
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'name',
            'slug',
            'sku',
            'category_id',
            'brand',
            'price',
            'status',
            'type'
        ]);

        // Test with invalid data
        $response = $this->postJson('/api/admin/products', [
            'name' => 'ab', // too short
            'slug' => 'invalid slug with spaces',
            'sku' => '',
            'category_id' => 999, // non-existent
            'brand' => 'a', // too short
            'price' => -10, // negative
            'status' => 'invalid-status',
            'type' => 'invalid-type'
        ]);
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'name',
            'slug',
            'sku',
            'category_id',
            'brand',
            'price',
            'status',
            'type'
        ]);

        // Test with valid data
        $validData = [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            'stock_quantity' => 10,
            'in_stock' => true
        ];

        $response = $this->postJson('/api/admin/products', $validData);
        $response->assertStatus(201);
    }

    /** @test */
    public function it_validates_pricing_fields()
    {
        $this->actingAs($this->adminUser);

        // Test sale price must be less than regular price
        $response = $this->postJson('/api/admin/products', [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'sale_price' => 109.99, // Higher than regular price
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            'stock_quantity' => 10,
            'in_stock' => true
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'sale_price'
        ]);

        // Test valid sale price
        $validData = [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'sale_price' => 89.99, // Lower than regular price
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            'stock_quantity' => 10,
            'in_stock' => true
        ];

        $response = $this->postJson('/api/admin/products', $validData);
        $response->assertStatus(201);
    }

    /** @test */
    public function it_validates_stock_management_fields()
    {
        $this->actingAs($this->adminUser);

        // Test stock_quantity required when manage_stock is true
        $response = $this->postJson('/api/admin/products', [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            // Missing stock_quantity
            'in_stock' => true
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'stock_quantity'
        ]);

        // Test negative stock_quantity
        $response = $this->postJson('/api/admin/products', [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            'stock_quantity' => -5, // Negative
            'in_stock' => true
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'stock_quantity'
        ]);

        // Test valid stock management
        $validData = [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            'stock_quantity' => 10,
            'in_stock' => true,
            'low_stock_threshold' => 5
        ];

        $response = $this->postJson('/api/admin/products', $validData);
        $response->assertStatus(201);
    }

    /** @test */
    public function it_validates_seo_fields()
    {
        $this->actingAs($this->adminUser);

        // Test SEO title and description length
        $response = $this->postJson('/api/admin/products', [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            'stock_quantity' => 10,
            'in_stock' => true,
            'seo_title' => str_repeat('a', 80), // Too long (max 70)
            'seo_description' => str_repeat('a', 170) // Too long (max 160)
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'seo_title',
            'seo_description'
        ]);

        // Test valid SEO fields
        $validData = [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            'stock_quantity' => 10,
            'in_stock' => true,
            'seo_title' => 'Test Product - Best Quality Widget',
            'seo_description' => 'This is a high-quality test product with amazing features.'
        ];

        $response = $this->postJson('/api/admin/products', $validData);
        $response->assertStatus(201);
    }

    /** @test */
    public function it_validates_physical_properties()
    {
        $this->actingAs($this->adminUser);

        // Test weight and dimensions
        $response = $this->postJson('/api/admin/products', [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            'stock_quantity' => 10,
            'in_stock' => true,
            'weight' => 1500, // Exceeds max 1000
            'dimensions' => [
                'length' => 1200, // Exceeds max 1000
                'width' => 800,
                'height' => 500
            ]
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'weight',
            'dimensions.length'
        ]);

        // Test valid physical properties
        $validData = [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            'stock_quantity' => 10,
            'in_stock' => true,
            'weight' => 500,
            'dimensions' => [
                'length' => 300,
                'width' => 200,
                'height' => 100
            ]
        ];

        $response = $this->postJson('/api/admin/products', $validData);
        $response->assertStatus(201);
    }

    /** @test */
    public function it_validates_shipping_information()
    {
        $this->actingAs($this->adminUser);

        // Test shipping weight and dimensions
        $response = $this->postJson('/api/admin/products', [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            'stock_quantity' => 10,
            'in_stock' => true,
            'shipping_weight_grams' => 60000, // Exceeds max 50000
            'shipping_dimensions_json' => [
                'length' => 1200, // Exceeds max 1000
                'width' => 800,
                'height' => 500
            ],
            'shipping_cost' => 15000 // Exceeds max 10000
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'shipping_weight_grams',
            'shipping_dimensions_json.length',
            'shipping_cost'
        ]);

        // Test valid shipping information
        $validData = [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            'stock_quantity' => 10,
            'in_stock' => true,
            'shipping_weight_grams' => 2500,
            'shipping_dimensions_json' => [
                'length' => 350,
                'width' => 250,
                'height' => 150
            ],
            'shipping_cost' => 9.99,
            'free_shipping' => false
        ];

        $response = $this->postJson('/api/admin/products', $validData);
        $response->assertStatus(201);
    }

    /** @test */
    public function it_validates_marketplace_fields()
    {
        $this->actingAs($this->adminUser);

        // Test marketplace fields validation
        $response = $this->postJson('/api/admin/products', [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            'stock_quantity' => 10,
            'in_stock' => true,
            'warranty_months' => 150, // Exceeds max 120
            'condition' => 'invalid-condition', // Invalid condition
            'origin_country' => 'USA', // Invalid format (should be 2 letters)
            'min_order_quantity' => 0, // Less than min 1
            'max_order_quantity' => 5, // Valid
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'warranty_months',
            'condition',
            'origin_country',
            'min_order_quantity'
        ]);

        // Test max_order_quantity must be >= min_order_quantity
        $response = $this->postJson('/api/admin/products', [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            'stock_quantity' => 10,
            'in_stock' => true,
            'min_order_quantity' => 10,
            'max_order_quantity' => 5 // Less than min_order_quantity
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'max_order_quantity'
        ]);

        // Test valid marketplace fields
        $validData = [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            'stock_quantity' => 10,
            'in_stock' => true,
            'warranty_months' => 24,
            'condition' => 'new',
            'origin_country' => 'US',
            'min_order_quantity' => 2,
            'max_order_quantity' => 20
        ];

        $response = $this->postJson('/api/admin/products', $validData);
        $response->assertStatus(201);
    }

    /** @test */
    public function it_validates_bulk_pricing()
    {
        $this->actingAs($this->adminUser);

        // Test bulk pricing with non-unique quantities
        $response = $this->postJson('/api/admin/products', [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            'stock_quantity' => 100,
            'in_stock' => true,
            'bulk_pricing_json' => [
                ['quantity' => 5, 'price' => 90],
                ['quantity' => 5, 'price' => 85], // Duplicate quantity
                ['quantity' => 20, 'price' => 80]
            ]
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'bulk_pricing_json'
        ]);

        // Test bulk pricing with non-ascending quantities
        $response = $this->postJson('/api/admin/products', [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            'stock_quantity' => 100,
            'in_stock' => true,
            'bulk_pricing_json' => [
                ['quantity' => 20, 'price' => 80], // Not in ascending order
                ['quantity' => 5, 'price' => 90],
                ['quantity' => 10, 'price' => 85]
            ]
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'bulk_pricing_json'
        ]);

        // Test bulk pricing with non-decreasing per-unit prices
        $response = $this->postJson('/api/admin/products', [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            'stock_quantity' => 100,
            'in_stock' => true,
            'bulk_pricing_json' => [
                ['quantity' => 5, 'price' => 450], // 90 per unit
                ['quantity' => 10, 'price' => 950], // 95 per unit - higher than previous
                ['quantity' => 20, 'price' => 1600] // 80 per unit
            ]
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'bulk_pricing_json'
        ]);

        // Test valid bulk pricing
        $validData = [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            'stock_quantity' => 100,
            'in_stock' => true,
            'bulk_pricing_json' => [
                ['quantity' => 5, 'price' => 450], // 90 per unit
                ['quantity' => 10, 'price' => 850], // 85 per unit
                ['quantity' => 20, 'price' => 1600] // 80 per unit
            ]
        ];

        $response = $this->postJson('/api/admin/products', $validData);
        $response->assertStatus(201);
    }

    /** @test */
    public function it_validates_product_identification()
    {
        $this->actingAs($this->adminUser);

        // Test invalid EAN code
        $response = $this->postJson('/api/admin/products', [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            'stock_quantity' => 10,
            'in_stock' => true,
            'ean_code' => '123456', // Too short (should be 8-13 digits)
            'manufacturer_part_number' => 'MPN-12345'
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'ean_code'
        ]);

        // Test valid product identification
        $validData = [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            'stock_quantity' => 10,
            'in_stock' => true,
            'ean_code' => '1234567890123', // Valid 13-digit EAN
            'manufacturer_part_number' => 'MPN-12345'
        ];

        $response = $this->postJson('/api/admin/products', $validData);
        $response->assertStatus(201);
    }

    /** @test */
    public function it_validates_dates()
    {
        $this->actingAs($this->adminUser);

        // Test future release date
        $response = $this->postJson('/api/admin/products', [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            'stock_quantity' => 10,
            'in_stock' => true,
            'release_date' => now()->addDays(10)->format('Y-m-d') // Future date
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'release_date'
        ]);

        // Test discontinuation date before release date
        $response = $this->postJson('/api/admin/products', [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            'stock_quantity' => 10,
            'in_stock' => true,
            'release_date' => now()->subDays(10)->format('Y-m-d'),
            'discontinued_at' => now()->subDays(20)->format('Y-m-d') // Before release date
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'discontinued_at'
        ]);

        // Test valid dates
        $validData = [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->category->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'featured' => false,
            'manage_stock' => true,
            'stock_quantity' => 10,
            'in_stock' => true,
            'release_date' => now()->subDays(30)->format('Y-m-d'),
            'discontinued_at' => now()->subDays(5)->format('Y-m-d') // After release date
        ];

        $response = $this->postJson('/api/admin/products', $validData);
        $response->assertStatus(201);
    }

    /** @test */
    public function it_validates_bulk_pricing_directly()
    {
        // This test directly tests the validation logic without going through HTTP
        $request = new ProductRequest();
        
        // Test bulk pricing with non-unique quantities
        $bulkPricingData = [
            'bulk_pricing_json' => [
                ['quantity' => 5, 'price' => 90],
                ['quantity' => 5, 'price' => 85], // Duplicate quantity
                ['quantity' => 20, 'price' => 80]
            ]
        ];
        
        $validator = Validator::make($bulkPricingData, $request->rules());
        $request->withValidator($validator);
        $validator->validate();
        
        $this->assertTrue($validator->errors()->has('bulk_pricing_json'));
        $this->assertStringContainsString('unique', $validator->errors()->first('bulk_pricing_json'));
        
        // Test bulk pricing with non-ascending quantities
        $bulkPricingData = [
            'bulk_pricing_json' => [
                ['quantity' => 20, 'price' => 80],
                ['quantity' => 5, 'price' => 90],
                ['quantity' => 10, 'price' => 85]
            ]
        ];
        
        $validator = Validator::make($bulkPricingData, $request->rules());
        $request->withValidator($validator);
        $validator->validate();
        
        $this->assertTrue($validator->errors()->has('bulk_pricing_json'));
        $this->assertStringContainsString('ascending', $validator->errors()->first('bulk_pricing_json'));
        
        // Test bulk pricing with non-decreasing per-unit prices
        $bulkPricingData = [
            'bulk_pricing_json' => [
                ['quantity' => 5, 'price' => 450],
                ['quantity' => 10, 'price' => 950],
                ['quantity' => 20, 'price' => 1600]
            ]
        ];
        
        $validator = Validator::make($bulkPricingData, $request->rules());
        $request->withValidator($validator);
        $validator->validate();
        
        $this->assertTrue($validator->errors()->has('bulk_pricing_json'));
        $this->assertStringContainsString('better per-unit prices', $validator->errors()->first('bulk_pricing_json'));
    }
}