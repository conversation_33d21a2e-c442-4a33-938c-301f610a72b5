

<?php $__env->startSection('title', ucfirst($gateway) . ' Gateway Settings'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="<?php echo e(route('admin.gateways.index')); ?>" class="mr-4 text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-4">
                            <?php if($gateway === 'razorpay'): ?>
                                <img src="<?php echo e(asset('images/gateways/razorpay-logo.svg')); ?>" alt="Razorpay" class="w-8 h-8">
                            <?php elseif($gateway === 'payumoney'): ?>
                                <img src="<?php echo e(asset('images/gateways/payumoney-logo.svg')); ?>" alt="PayUmoney" class="w-8 h-8">
                            <?php elseif($gateway === 'cashfree'): ?>
                                <img src="<?php echo e(asset('images/gateways/cashfree-logo.svg')); ?>" alt="Cashfree" class="w-8 h-8">
                            <?php else: ?>
                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                </svg>
                            <?php endif; ?>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900"><?php echo e(ucfirst($gateway)); ?> Gateway</h1>
                            <p class="text-gray-600">Configure your <?php echo e(ucfirst($gateway)); ?> payment gateway settings</p>
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                    <?php if(($setting && !empty($setting->settings)) || (isset($gateway_obj) && !empty($gateway_obj->settings))): ?>
                        <button onclick="testGateway()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition duration-200">
                            Test Configuration
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <?php if(session('success')): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6" role="alert">
                <div class="flex">
                    <div class="py-1">
                        <svg class="fill-current h-6 w-6 text-green-500 mr-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                            <path d="M2.93 17.07A10 10 0 1 1 17.07 2.93 10 10 0 0 1 2.93 17.07zm12.73-1.41A8 8 0 1 0 4.34 4.34a8 8 0 0 0 11.32 11.32zM9 11V9h2v6H9v-4zm0-6h2v2H9V5z"/>
                        </svg>
                    </div>
                    <div><?php echo e(session('success')); ?></div>
                </div>
            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6" role="alert">
                <div class="flex">
                    <div class="py-1">
                        <svg class="fill-current h-6 w-6 text-red-500 mr-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                            <path d="M2.93 17.07A10 10 0 1 1 17.07 2.93 10 10 0 0 1 2.93 17.07zm1.41-1.41A8 8 0 1 0 15.66 4.34 8 8 0 0 0 4.34 15.66zm9.9-8.49L11.41 10l2.83 2.83-1.41 1.41L10 11.41l-2.83 2.83-1.41-1.41L8.59 10 5.76 7.17l1.41-1.41L10 8.59l2.83-2.83 1.41 1.41z"/>
                        </svg>
                    </div>
                    <div><?php echo e(session('error')); ?></div>
                </div>
            </div>
        <?php endif; ?>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Configuration Form -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-md">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-900">Gateway Configuration</h2>
                        <p class="text-sm text-gray-600">Configure your <?php echo e(ucfirst($gateway)); ?> API credentials and settings</p>
                    </div>
                    
                    <form action="<?php echo e(route('admin.gateways.update', $gateway)); ?>" method="POST" class="p-6">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        
                        <!-- Gateway Status -->
                        <div class="mb-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Gateway Status</label>
                                    <p class="text-sm text-gray-500">Enable or disable this payment gateway</p>
                                </div>
                                <div class="flex items-center">
                                    <input type="hidden" name="is_enabled" value="0">
                                    <input type="checkbox" name="is_enabled" value="1" 
                                           <?php echo e(($setting?->is_enabled ?? $gateway_obj?->is_enabled ?? false) ? 'checked' : ''); ?>

                                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                    <label class="ml-2 text-sm text-gray-700">Enabled</label>
                                </div>
                            </div>
                        </div>

                        <!-- Test/Live Mode -->
                        <div class="mb-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Environment Mode</label>
                                    <p class="text-sm text-gray-500">Switch between test and live environment</p>
                                </div>
                                <div class="flex items-center">
                                    <input type="hidden" name="is_test_mode" value="0">
                                    <input type="checkbox" name="is_test_mode" value="1" 
                                           <?php echo e(($setting?->is_test_mode ?? $gateway_obj?->is_test_mode ?? true) ? 'checked' : ''); ?>

                                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                    <label class="ml-2 text-sm text-gray-700">Test Mode</label>
                                </div>
                            </div>
                        </div>

                        <hr class="my-6">

                        <!-- Gateway-specific settings -->
                        <?php if($gateway === 'razorpay'): ?>
                            <?php echo $__env->make('admin.gateways.partials.razorpay-settings', ['settings' => $setting?->settings ?? $gateway_obj?->settings ?? []], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        <?php elseif($gateway === 'payumoney'): ?>
                            <?php echo $__env->make('admin.gateways.partials.payumoney-settings', ['settings' => $setting?->settings ?? $gateway_obj?->settings ?? []], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        <?php elseif($gateway === 'cashfree'): ?>
                            <?php echo $__env->make('admin.gateways.partials.cashfree-settings', ['settings' => $setting?->settings ?? $gateway_obj?->settings ?? []], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        <?php endif; ?>

                        <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                            <a href="<?php echo e(route('admin.gateways.index')); ?>" 
                               class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition duration-200">
                                Cancel
                            </a>
                            <button type="submit" 
                                    class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition duration-200">
                                Save Configuration
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Gateway Status Card -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Gateway Status</h3>
                    
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Status:</span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e(($setting?->is_enabled ?? false) ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                <?php echo e(($setting?->is_enabled ?? false) ? 'Enabled' : 'Disabled'); ?>

                            </span>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Mode:</span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e(($setting?->is_test_mode ?? true) ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'); ?>">
                                <?php echo e(($setting?->is_test_mode ?? true) ? 'Test' : 'Live'); ?>

                            </span>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Configuration:</span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e(($setting && !empty($setting->settings)) ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'); ?>">
                                <?php echo e(($setting && !empty($setting->settings)) ? 'Configured' : 'Not Configured'); ?>

                            </span>
                        </div>
                        
                        <?php if($testResult): ?>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Last Test:</span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($testResult['success'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                    <?php echo e($testResult['success'] ? 'Passed' : 'Failed'); ?>

                                </span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                    
                    <div class="space-y-3">
                        <?php if($setting && !empty($setting->settings)): ?>
                            <button onclick="testGateway()" 
                                    class="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition duration-200 text-sm">
                                Test Configuration
                            </button>
                            
                            <button onclick="toggleGateway('<?php echo e($gateway); ?>', <?php echo e(($setting?->is_enabled ?? false) ? 'false' : 'true'); ?>)" 
                                    class="w-full <?php echo e(($setting?->is_enabled ?? false) ? 'bg-red-600 hover:bg-red-700' : 'bg-indigo-600 hover:bg-indigo-700'); ?> text-white py-2 px-4 rounded-md transition duration-200 text-sm">
                                <?php echo e(($setting?->is_enabled ?? false) ? 'Disable Gateway' : 'Enable Gateway'); ?>

                            </button>
                            
                            <button onclick="switchMode('<?php echo e($gateway); ?>', <?php echo e(($setting?->is_test_mode ?? true) ? 'false' : 'true'); ?>)" 
                                    class="w-full bg-yellow-600 text-white py-2 px-4 rounded-md hover:bg-yellow-700 transition duration-200 text-sm">
                                Switch to <?php echo e(($setting?->is_test_mode ?? true) ? 'Live' : 'Test'); ?> Mode
                            </button>
                        <?php endif; ?>
                        
                        <a href="<?php echo e(route('admin.gateways.index')); ?>" 
                           class="block w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition duration-200 text-sm text-center">
                            Back to Gateways
                        </a>
                    </div>
                </div>

                <!-- Documentation -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Documentation</h3>
                    
                    <div class="space-y-3 text-sm">
                        <?php if($gateway === 'razorpay'): ?>
                            <a href="https://razorpay.com/docs/" target="_blank" class="flex items-center text-indigo-600 hover:text-indigo-800">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                                Razorpay Documentation
                            </a>
                            <a href="https://dashboard.razorpay.com/" target="_blank" class="flex items-center text-indigo-600 hover:text-indigo-800">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                                Razorpay Dashboard
                            </a>
                        <?php elseif($gateway === 'payumoney'): ?>
                            <a href="https://developer.payumoney.com/" target="_blank" class="flex items-center text-indigo-600 hover:text-indigo-800">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                                PayUmoney Documentation
                            </a>
                            <a href="https://www.payumoney.com/merchant/login" target="_blank" class="flex items-center text-indigo-600 hover:text-indigo-800">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                                PayUmoney Dashboard
                            </a>
                        <?php elseif($gateway === 'cashfree'): ?>
                            <a href="https://docs.cashfree.com/" target="_blank" class="flex items-center text-indigo-600 hover:text-indigo-800">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                                Cashfree Documentation
                            </a>
                            <a href="https://merchant.cashfree.com/" target="_blank" class="flex items-center text-indigo-600 hover:text-indigo-800">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                                Cashfree Dashboard
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div id="loadingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 max-w-sm mx-auto">
        <div class="flex items-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span id="loadingText">Processing...</span>
        </div>
    </div>
</div>

<script>
function showLoading(text = 'Processing...') {
    document.getElementById('loadingText').textContent = text;
    document.getElementById('loadingModal').classList.remove('hidden');
    document.getElementById('loadingModal').classList.add('flex');
}

function hideLoading() {
    document.getElementById('loadingModal').classList.add('hidden');
    document.getElementById('loadingModal').classList.remove('flex');
}

async function testGateway() {
    showLoading('Testing gateway configuration...');
    
    try {
        const response = await fetch(`/admin/gateways/<?php echo e($gateway); ?>/test`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            if (data.success) {
                alert('✅ Gateway test successful!\n\nThe gateway configuration is working correctly.');
            } else {
                alert('❌ Gateway test failed:\n\n' + data.message);
            }
        } else {
            alert('❌ Test failed:\n\n' + (data.message || 'Unknown error occurred'));
        }
    } catch (error) {
        alert('❌ Error occurred during test:\n\n' + error.message);
    } finally {
        hideLoading();
    }
}

async function toggleGateway(gateway, enabled) {
    showLoading(enabled ? 'Enabling gateway...' : 'Disabling gateway...');
    
    try {
        const response = await fetch(`/admin/gateways/${gateway}/toggle`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({ enabled: enabled })
        });
        
        const data = await response.json();
        
        if (response.ok && data.success) {
            location.reload();
        } else {
            alert('❌ ' + (data.message || 'Failed to toggle gateway'));
        }
    } catch (error) {
        alert('❌ Error: ' + error.message);
    } finally {
        hideLoading();
    }
}

async function switchMode(gateway, testMode) {
    showLoading(testMode ? 'Switching to test mode...' : 'Switching to live mode...');
    
    try {
        const response = await fetch(`/admin/gateways/${gateway}/switch-mode`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({ test_mode: testMode })
        });
        
        const data = await response.json();
        
        if (response.ok && data.success) {
            location.reload();
        } else {
            alert('❌ ' + (data.message || 'Failed to switch mode'));
        }
    } catch (error) {
        alert('❌ Error: ' + error.message);
    } finally {
        hideLoading();
    }
}
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\lara\www\pc-builder\resources\views/admin/gateways/show.blade.php ENDPATH**/ ?>