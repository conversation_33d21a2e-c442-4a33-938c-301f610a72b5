<?php

namespace App\Services;

use App\Contracts\Purchasable;
use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Component;
use App\Models\Product;
use App\Models\User;
use App\Services\CompatibilityService;
use App\Services\CompatibilityResult;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\DB;

class CartService
{
    /**
     * Get the current cart for the user or session.
     *
     * @param User|null $user
     * @param string|null $sessionId
     * @return \App\Models\Cart
     */
    public function getCart(?User $user = null, ?string $sessionId = null)
    {
        $user = $user ?? Auth::user();
        $sessionId = $sessionId ?? Session::getId();
        
        // If user is logged in, get their cart
        if ($user) {
            $cart = Cart::firstOrCreate(
                ['user_id' => $user->id],
                ['session_id' => $sessionId]
            );
            
            // If there was a session cart, merge it with the user cart
            $this->mergeGuestCart($user, $sessionId);
            
            return $cart;
        }
        
        // Otherwise, get the session cart
        return Cart::firstOrCreate(
            ['session_id' => $sessionId],
            ['user_id' => null]
        );
    }
    
    /**
     * Merge guest cart with user cart when user logs in.
     *
     * @param User $user
     * @param string $sessionId
     * @return void
     */
    public function mergeGuestCart(User $user, string $sessionId): void
    {
        // Get the session cart
        $sessionCart = Cart::where('session_id', $sessionId)
            ->where('user_id', null)
            ->first();
        
        if (!$sessionCart || $sessionCart->items()->count() === 0) {
            return;
        }
        
        // Get or create user cart
        $userCart = Cart::firstOrCreate(['user_id' => $user->id]);
        
        DB::transaction(function () use ($sessionCart, $userCart) {
            // Move all items from session cart to user cart
        foreach ($sessionCart->items()->with(['component', 'product'])->get() as $item) {
            // Determine search criteria based on item type
            $searchCriteria = $item->item_type === CartItem::TYPE_PRODUCT
                ? ['product_id' => $item->product_id, 'item_type' => CartItem::TYPE_PRODUCT]
                : ['component_id' => $item->component_id, 'item_type' => CartItem::TYPE_COMPONENT];
            
            // Check if item already exists in user cart
            $existingItem = $userCart->items()->where($searchCriteria)->first();
            
            if ($existingItem) {
                $existingItem->quantity += $item->quantity;
                $existingItem->save();
            } else {
                $itemData = [
                    'quantity' => $item->quantity,
                    'price' => $item->price,
                    'item_type' => $item->item_type,
                ];
                
                if ($item->item_type === CartItem::TYPE_PRODUCT) {
                    $itemData['product_id'] = $item->product_id;
                } else {
                    $itemData['component_id'] = $item->component_id;
                }
                
                $userCart->items()->create($itemData);
            }
        }
            
            // Update user cart total and delete session cart
            $userCart->updateTotal();
            $sessionCart->delete();
        });
    }
    
    /**
     * Add a purchasable item to the cart.
     *
     * @param Purchasable $item
     * @param int $quantity
     * @param User|null $user
     * @return CartItem
     * @throws \InvalidArgumentException
     */
    public function addToCart(Purchasable $item, int $quantity, ?User $user = null): CartItem
    {
        if ($quantity <= 0) {
            throw new \InvalidArgumentException('Quantity must be greater than 0');
        }

        // Validate availability using interface method
        if (!$item->isAvailable($quantity)) {
            throw new \InvalidArgumentException('Item is not available or insufficient stock');
        }

        $cart = $this->getCart($user);

        return DB::transaction(function () use ($cart, $item, $quantity) {
            // Determine item type and search criteria
            $isProduct = $item instanceof Product;
            $searchCriteria = $isProduct
                ? ['product_id' => $item->id, 'item_type' => CartItem::TYPE_PRODUCT]
                : ['component_id' => $item->id, 'item_type' => CartItem::TYPE_COMPONENT];

            // Check if item already exists in cart
            $existingItem = $cart->items()->where($searchCriteria)->first();

            if ($existingItem) {
                $newQuantity = $existingItem->quantity + $quantity;

                // Check total stock availability using interface method
                if (!$item->isAvailable($newQuantity)) {
                    throw new \InvalidArgumentException('Insufficient stock for requested quantity');
                }

                $existingItem->quantity = $newQuantity;
                $existingItem->save();

                $cart->updateTotal();
                return $existingItem;
            }

            // Create new cart item using interface methods
            $itemData = [
                'quantity' => $quantity,
                'price' => $item->getEffectivePrice(),
                'item_type' => $isProduct ? CartItem::TYPE_PRODUCT : CartItem::TYPE_COMPONENT,
            ];

            if ($isProduct) {
                $itemData['product_id'] = $item->id;
            } else {
                $itemData['component_id'] = $item->id;
            }

            $cartItem = $cart->items()->create($itemData);

            $cart->updateTotal();
            return $cartItem;
        });
    }

    /**
     * Add an item to the cart (unified method for both components and products).
     *
     * @param Purchasable|int $item Purchasable item or component ID for backward compatibility
     * @param int $quantity
     * @return \App\Models\CartItem
     * @throws \InvalidArgumentException
     */
    public function addItem($item, $quantity = 1)
    {
        // Handle backward compatibility with component ID
        if (is_int($item)) {
            $component = Component::find($item);
            
            if (!$component) {
                throw new \InvalidArgumentException('Component not found');
            }
            
            return $this->addToCart($component, $quantity);
        }
        
        // Handle Purchasable items
        if ($item instanceof Purchasable) {
            return $this->addToCart($item, $quantity);
        }
        
        throw new \InvalidArgumentException('Item must be a Purchasable instance or component ID');
    }
    
    /**
     * Get the total price of the cart.
     *
     * @param User|null $user
     * @return float
     */
    public function getCartTotal(?User $user = null): float
    {
        $cart = $this->getCart($user);
        return (float) $cart->total;
    }
    
    /**
     * Calculate shipping cost for the cart.
     *
     * @param Cart $cart
     * @return float
     */
    public function calculateShipping(Cart $cart): float
    {
        // Free shipping for orders over $100
        if ($cart->total >= 100.00) {
            return 0.00;
        }
        
        // Base shipping rate
        $baseShipping = 10.00;
        
        // Calculate shipping based on item count and weight
        $itemCount = $cart->items()->sum('quantity');
        
        // Additional shipping for more items
        if ($itemCount > 5) {
            $baseShipping += 5.00;
        }
        
        return $baseShipping;
    }
    
    /**
     * Clear all items from the cart.
     *
     * @param User|null $user
     * @return bool
     */
    public function clearCart(?User $user = null): bool
    {
        $cart = $this->getCart($user);
        return $cart->clear();
    }
    
    /**
     * Update the quantity of an item in the cart.
     *
     * @param int $itemId
     * @param int $quantity
     * @param User|null $user
     * @return CartItem|bool
     * @throws \InvalidArgumentException
     */
    public function updateItemQuantity($itemId, $quantity, ?User $user = null)
    {
        if ($quantity < 0) {
            throw new \InvalidArgumentException('Quantity cannot be negative');
        }

        $cart = $this->getCart($user);
        $item = $cart->items()->with(['component', 'product'])->find($itemId);

        if (!$item) {
            return false;
        }

        if ($quantity === 0) {
            return $this->removeFromCart($item, $user);
        }

        // Get the purchasable item and check availability using interface method
        $purchasableItem = $item->item();
        if ($purchasableItem && !$purchasableItem->isAvailable($quantity)) {
            throw new \InvalidArgumentException('Insufficient stock for requested quantity');
        }

        return DB::transaction(function () use ($item, $quantity, $cart) {
            $item->quantity = $quantity;
            $item->save();
            
            $cart->updateTotal();
            return $item;
        });
    }
    
    /**
     * Remove an item from the cart.
     *
     * @param CartItem|int $item
     * @param User|null $user
     * @return bool
     */
    public function removeFromCart($item, ?User $user = null): bool
    {
        $cart = $this->getCart($user);
        
        if (is_int($item)) {
            $item = $cart->items()->find($item);
        }
        
        if (!$item) {
            return false;
        }
        
        return DB::transaction(function () use ($item, $cart) {
            $result = $item->delete();
            $cart->updateTotal();
            return $result;
        });
    }

    /**
     * Remove an item from the cart (legacy method).
     *
     * @param int $itemId
     * @return bool
     */
    public function removeItem($itemId)
    {
        return $this->removeFromCart($itemId);
    }
    
    // Removed duplicate clearCart method
    
    /**
     * Add a build to the cart.
     *
     * @param int $buildId
     * @return bool
     */
    public function addBuild($buildId)
    {
        $build = \App\Models\Build::find($buildId);
        
        if (!$build) {
            return false;
        }
        
        $cart = $this->getCart();
        return $cart->addBuild($build);
    }
    
    /**
     * Get the total number of items in the cart.
     *
     * @param User|null $user
     * @return int
     */
    public function getItemCount(?User $user = null)
    {
        $cart = $this->getCart($user);
        
        if (!$cart) {
            return 0;
        }
        
        return $cart->items()->sum('quantity');
    }
    
    /**
     * Get the total price of the cart.
     *
     * @param User|null $user
     * @return float
     */
    public function getTotal(?User $user = null)
    {
        $cart = $this->getCart($user);

        if (!$cart) {
            return 0;
        }

        return $cart->total;
    }

    /**
     * Clean up old session carts (older than 30 days)
     *
     * @return int Number of carts cleaned up
     */
    public function cleanupOldSessionCarts(): int
    {
        $cutoffDate = now()->subDays(30);

        $oldCarts = Cart::where('user_id', null)
            ->where('updated_at', '<', $cutoffDate)
            ->get();

        $count = $oldCarts->count();

        foreach ($oldCarts as $cart) {
            $cart->items()->delete();
            $cart->delete();
        }

        return $count;
    }

    /**
     * Ensure cart data integrity and fix common issues
     *
     * @param User|null $user
     * @return array Issues found and fixed
     */
    public function ensureCartIntegrity(?User $user = null): array
    {
        $cart = $this->getCart($user);
        $issues = [];

        if (!$cart) {
            return $issues;
        }

        // Check for items with invalid components/products
        $invalidItems = $cart->items()->whereDoesntHave('component')->whereDoesntHave('product')->get();

        foreach ($invalidItems as $item) {
            $item->delete();
            $issues[] = "Removed invalid cart item (ID: {$item->id})";
        }

        // Update cart total if items were removed
        if (!empty($issues)) {
            $cart->updateTotal();
        }

        return $issues;
    }
    
    /**
     * Transfer a guest cart to a user after login.
     *
     * @param string $sessionId
     * @param int $userId
     * @return \App\Models\Cart|null
     */
    public function transferCartAfterLogin($sessionId, $userId)
    {
        // Find the session cart
        $sessionCart = Cart::where('session_id', $sessionId)
            ->where('user_id', null)
            ->first();
        
        if (!$sessionCart) {
            return null;
        }
        
        // Find or create the user cart
        $userCart = Cart::firstOrCreate(
            ['user_id' => $userId],
            ['session_id' => $sessionId]
        );
        
        // Move all items from session cart to user cart
        foreach ($sessionCart->items()->with('component')->get() as $item) {
            $userCart->addItem($item->component, $item->quantity, $item->price);
        }
        
        // Delete the session cart
        $sessionCart->delete();
        
        return $userCart;
    }

    /**
     * Calculate cart subtotal (before tax and shipping).
     *
     * @param User|null $user
     * @return float
     */
    public function getSubtotal(?User $user = null): float
    {
        $cart = $this->getCart($user);
        return $cart->total ?? 0;
    }

    /**
     * Calculate tax amount for the cart.
     *
     * @param User|null $user
     * @param float $taxRate
     * @return float
     */
    public function getTaxAmount(?User $user = null, float $taxRate = 0.08): float
    {
        $subtotal = $this->getSubtotal($user);
        return round($subtotal * $taxRate, 2);
    }

    /**
     * Calculate shipping cost for the cart.
     *
     * @param User|null $user
     * @param array $shippingAddress
     * @return float
     */
    public function getShippingCost(?User $user = null, array $shippingAddress = []): float
    {
        $cart = $this->getCart($user);
        $subtotal = $cart->total ?? 0;
        
        // Free shipping over $100
        if ($subtotal >= 100) {
            return 0;
        }
        
        // Standard shipping rate
        return 9.99;
    }

    /**
     * Calculate total cart amount including tax and shipping.
     *
     * @param User|null $user
     * @param float $taxRate
     * @param array $shippingAddress
     * @return array
     */
    public function getCartTotals(?User $user = null, float $taxRate = 0.08, array $shippingAddress = []): array
    {
        $subtotal = $this->getSubtotal($user);
        $tax = $this->getTaxAmount($user, $taxRate);
        $shipping = $this->getShippingCost($user, $shippingAddress);
        $total = $subtotal + $tax + $shipping;

        return [
            'subtotal' => $subtotal,
            'tax' => $tax,
            'shipping' => $shipping,
            'total' => round($total, 2),
        ];
    }

    /**
     * Validate cart item quantities against current stock levels.
     *
     * @param User|null $user
     * @return array
     */
    public function validateCartStock(?User $user = null): array
    {
        $cart = $this->getCart($user);
        $issues = [];

        if (!$cart) {
            return $issues;
        }

        foreach ($cart->items()->with(['component', 'product'])->get() as $item) {
            // Get the purchasable item using the unified interface
            $purchasableItem = $item->item();
            
            if (!$purchasableItem) {
                $issues[] = [
                    'item_id' => $item->id,
                    'component_name' => 'Unknown Item',
                    'issue' => 'Item no longer exists',
                    'current_quantity' => $item->quantity,
                    'available_quantity' => 0,
                ];
                continue;
            }
            
            // Check availability using interface method
            if (!$purchasableItem->isAvailable($item->quantity)) {
                $availableStock = $purchasableItem->getStock();
                
                $issues[] = [
                    'item_id' => $item->id,
                    'component_name' => $purchasableItem->getName(),
                    'issue' => $availableStock > 0 ? 'Insufficient stock' : 'Item is no longer available',
                    'current_quantity' => $item->quantity,
                    'available_quantity' => $availableStock,
                ];
            }
        }

        return $issues;
    }

    /**
     * Fix cart stock issues by adjusting quantities or removing items.
     *
     * @param User|null $user
     * @return array
     */
    public function fixCartStockIssues(?User $user = null): array
    {
        $issues = $this->validateCartStock($user);
        $fixed = [];

        foreach ($issues as $issue) {
            $itemId = $issue['item_id'];
            $availableQuantity = $issue['available_quantity'];

            if ($availableQuantity > 0) {
                // Adjust quantity to available stock
                $this->updateItemQuantity($itemId, $availableQuantity, $user);
                $fixed[] = [
                    'item_id' => $itemId,
                    'action' => 'quantity_adjusted',
                    'new_quantity' => $availableQuantity,
                ];
            } else {
                // Remove item completely
                $this->removeFromCart($itemId, $user);
                $fixed[] = [
                    'item_id' => $itemId,
                    'action' => 'item_removed',
                ];
            }
        }

        return $fixed;
    }

    /**
     * Clean up expired guest carts.
     *
     * @param int $daysOld
     * @return int
     */
    public function cleanupExpiredCarts(int $daysOld = 7): int
    {
        $cutoffDate = now()->subDays($daysOld);
        
        return Cart::where('user_id', null)
            ->where('updated_at', '<', $cutoffDate)
            ->delete();
    }

    /**
     * Clean up empty carts.
     *
     * @return int
     */
    public function cleanupEmptyCarts(): int
    {
        $emptyCarts = Cart::whereDoesntHave('items')->get();
        $count = $emptyCarts->count();
        
        foreach ($emptyCarts as $cart) {
            $cart->delete();
        }
        
        return $count;
    }

    /**
     * Update cart item prices to current item prices.
     *
     * @param User|null $user
     * @return array
     */
    public function updateCartPrices(?User $user = null): array
    {
        $cart = $this->getCart($user);
        $updated = [];

        if (!$cart) {
            return $updated;
        }

        foreach ($cart->items()->with(['component', 'product'])->get() as $item) {
            // Get the purchasable item using the unified interface
            $purchasableItem = $item->item();
            
            if (!$purchasableItem) {
                continue;
            }
            
            // Get current effective price using interface method
            $currentPrice = $purchasableItem->getEffectivePrice();
            
            if ($item->price != $currentPrice) {
                $oldPrice = $item->price;
                $item->price = $currentPrice;
                $item->save();
                
                $updated[] = [
                    'item_id' => $item->id,
                    'component_name' => $purchasableItem->getName(),
                    'old_price' => $oldPrice,
                    'new_price' => $currentPrice,
                ];
            }
        }

        if (!empty($updated)) {
            $cart->updateTotal();
        }

        return $updated;
    }

    /**
     * Check if cart has any items.
     *
     * @param User|null $user
     * @return bool
     */
    public function hasItems(?User $user = null): bool
    {
        return $this->getItemCount($user) > 0;
    }

    /**
     * Get cart weight for shipping calculations.
     *
     * @param User|null $user
     * @return float
     */
    public function getCartWeight(?User $user = null): float
    {
        $cart = $this->getCart($user);
        $weight = 0;

        if (!$cart) {
            return $weight;
        }

        foreach ($cart->items()->with(['component', 'product'])->get() as $item) {
            if ($item->component) {
                $componentWeight = $item->component->getSpec('weight', 1.0); // Default 1 lb
                $weight += $componentWeight * $item->quantity;
            } elseif ($item->product && method_exists($item->product, 'getSpec')) {
                $productWeight = $item->product->getSpec('weight', 1.0); // Default 1 lb
                $weight += $productWeight * $item->quantity;
            } else {
                // Default weight for items without weight specification
                $weight += 1.0 * $item->quantity;
            }
        }

        return $weight;
    }

    /**
     * Remove an item from the cart by component ID.
     *
     * @param int $componentId
     * @param User|null $user
     * @return bool
     */
    public function removeItemByComponentId($componentId, ?User $user = null): bool
    {
        $cart = $this->getCart($user);
        $item = $cart->items()->where('component_id', $componentId)->first();
        
        if (!$item) {
            throw new \InvalidArgumentException('Item not found in cart');
        }
        
        return $this->removeFromCart($item, $user);
    }

    /**
     * Apply a coupon to the cart.
     *
     * @param string $couponCode
     * @param User|null $user
     * @return array
     */
    public function applyCoupon($couponCode, ?User $user = null): array
    {
        $coupon = \App\Models\Coupon::findByCode($couponCode);

        if (!$coupon) {
            throw new \InvalidArgumentException('Invalid coupon code');
        }

        $userId = $user ? $user->id : auth()->id();
        if (!$coupon->canBeUsedBy($userId)) {
            throw new \InvalidArgumentException('This coupon cannot be used by this user');
        }

        $cart = $this->getCart($user);
        $subtotal = $cart->total;

        // Check minimum amount requirement
        if ($coupon->minimum_amount && $subtotal < $coupon->minimum_amount) {
            throw new \InvalidArgumentException(
                "Minimum order amount of ₹" . number_format($coupon->minimum_amount, 2) . " required."
            );
        }

        // For now, skip product applicability check for components
        // TODO: Implement proper component-coupon compatibility
        // Get cart items for product applicability check
        // $cartItems = $cart->items()->with('component')->get();
        // $productIds = $cartItems->pluck('component_id')->toArray();
        //
        // if (!$coupon->isApplicableToProducts($productIds)) {
        //     throw new \InvalidArgumentException('This coupon is not applicable to selected products');
        // }

        $discountAmount = $coupon->calculateDiscount((float) $subtotal);

        // Store coupon in session
        session(['applied_coupon' => $couponCode, 'discount_amount' => $discountAmount]);

        return [
            'discount_amount' => $discountAmount,
            'coupon_code' => $couponCode
        ];
    }

    /**
     * Calculate bulk pricing for products in the cart.
     *
     * @param User|null $user
     * @return array
     */
    public function calculateBulkPricing(?User $user = null): array
    {
        $cart = $this->getCart($user);
        $bulkPricingApplied = [];

        if (!$cart) {
            return $bulkPricingApplied;
        }

        foreach ($cart->items()->with(['product'])->get() as $item) {
            // Only apply bulk pricing to products
            if (!$item->product) {
                continue;
            }

            $product = $item->product;
            
            // Check if product supports bulk pricing
            if (method_exists($product, 'getBulkPrice')) {
                $bulkPrice = $product->getBulkPrice($item->quantity);
                $originalPrice = $product->getEffectivePrice();
                
                if ($bulkPrice < $originalPrice) {
                    $savings = ($originalPrice - $bulkPrice) * $item->quantity;
                    
                    // Update the cart item price
                    $item->price = $bulkPrice;
                    $item->save();
                    
                    $bulkPricingApplied[] = [
                        'item_id' => $item->id,
                        'product_name' => $product->getName(),
                        'quantity' => $item->quantity,
                        'original_price' => $originalPrice,
                        'bulk_price' => $bulkPrice,
                        'total_savings' => $savings,
                    ];
                }
            }
        }

        if (!empty($bulkPricingApplied)) {
            $cart->updateTotal();
        }

        return $bulkPricingApplied;
    }

    /**
     * Check compatibility of components in the cart.
     *
     * @param User|null $user
     * @return array
     */
    public function checkCartCompatibility(?User $user = null): array
    {
        $cart = $this->getCart($user);
        
        if (!$cart) {
            return [
                'compatible' => true,
                'issues' => [],
                'warnings' => [],
                'components' => [],
            ];
        }

        // Get all components from the cart
        $components = $cart->items()
            ->with(['component'])
            ->whereNotNull('component_id')
            ->get()
            ->pluck('component')
            ->filter();

        if ($components->isEmpty()) {
            return [
                'compatible' => true,
                'issues' => [],
                'warnings' => [],
                'components' => [],
            ];
        }

        // Use CompatibilityService to check compatibility
        $compatibilityService = app(CompatibilityService::class);
        $result = $compatibilityService->checkCompatibility($components->toArray());

        return [
            'compatible' => $result->isCompatible(),
            'issues' => $result->getIssues(),
            'warnings' => $result->getWarnings(),
            'components' => $components->toArray(),
        ];
    }

    /**
     * Get cart optimization suggestions.
     *
     * @param User|null $user
     * @return array
     */
    public function getCartOptimizationSuggestions(?User $user = null): array
    {
        $cart = $this->getCart($user);
        $suggestions = [];

        if (!$cart) {
            return $suggestions;
        }

        // Check for bulk pricing opportunities
        $bulkOpportunities = $this->getBulkPricingOpportunities($cart);
        if (!empty($bulkOpportunities)) {
            $suggestions[] = [
                'type' => 'bulk_pricing',
                'title' => 'Bulk Pricing Available',
                'description' => 'Increase quantities to unlock bulk pricing discounts',
                'opportunities' => $bulkOpportunities,
            ];
        }

        // Check for compatibility issues
        $compatibilityResult = $this->checkCartCompatibility($user);
        if (!$compatibilityResult['compatible']) {
            $suggestions[] = [
                'type' => 'compatibility',
                'title' => 'Compatibility Issues Found',
                'description' => 'Some components in your cart may not be compatible',
                'issues' => $compatibilityResult['issues'],
            ];
        }

        // Check for missing components for a complete build
        $missingComponents = $this->getMissingBuildComponents($cart);
        if (!empty($missingComponents)) {
            $suggestions[] = [
                'type' => 'missing_components',
                'title' => 'Complete Your Build',
                'description' => 'Add these components to complete your PC build',
                'missing' => $missingComponents,
            ];
        }

        // Check for shipping optimization
        $shippingOptimization = $this->getShippingOptimization($cart);
        if ($shippingOptimization) {
            $suggestions[] = $shippingOptimization;
        }

        return $suggestions;
    }

    /**
     * Get bulk pricing opportunities for cart items.
     *
     * @param Cart $cart
     * @return array
     */
    protected function getBulkPricingOpportunities(Cart $cart): array
    {
        $opportunities = [];

        foreach ($cart->items()->with(['product'])->get() as $item) {
            if (!$item->product || !method_exists($item->product, 'getBulkPrice')) {
                continue;
            }

            $product = $item->product;
            $currentQuantity = $item->quantity;
            $currentPrice = $product->getEffectivePrice();

            // Check if increasing quantity would unlock bulk pricing
            $nextTierQuantity = $this->getNextBulkPricingTier($product, $currentQuantity);
            
            if ($nextTierQuantity) {
                $bulkPrice = $product->getBulkPrice($nextTierQuantity);
                
                if ($bulkPrice < $currentPrice) {
                    $additionalQuantity = $nextTierQuantity - $currentQuantity;
                    $savings = ($currentPrice - $bulkPrice) * $nextTierQuantity;
                    
                    $opportunities[] = [
                        'product_id' => $product->id,
                        'product_name' => $product->getName(),
                        'current_quantity' => $currentQuantity,
                        'suggested_quantity' => $nextTierQuantity,
                        'additional_needed' => $additionalQuantity,
                        'current_price' => $currentPrice,
                        'bulk_price' => $bulkPrice,
                        'total_savings' => $savings,
                    ];
                }
            }
        }

        return $opportunities;
    }

    /**
     * Get the next bulk pricing tier for a product.
     *
     * @param Product $product
     * @param int $currentQuantity
     * @return int|null
     */
    protected function getNextBulkPricingTier(Product $product, int $currentQuantity): ?int
    {
        if (!isset($product->bulk_pricing_json) || !is_array($product->bulk_pricing_json)) {
            return null;
        }

        $bulkTiers = collect($product->bulk_pricing_json)
            ->sortBy('min_quantity')
            ->where('min_quantity', '>', $currentQuantity);

        return $bulkTiers->first()['min_quantity'] ?? null;
    }

    /**
     * Get missing components for a complete PC build.
     *
     * @param Cart $cart
     * @return array
     */
    protected function getMissingBuildComponents(Cart $cart): array
    {
        $components = $cart->items()
            ->with(['component.category'])
            ->whereNotNull('component_id')
            ->get()
            ->pluck('component')
            ->filter();

        if ($components->isEmpty()) {
            return [];
        }

        // Define essential component categories for a complete build
        $essentialCategories = [
            'cpu' => 'Processor (CPU)',
            'motherboard' => 'Motherboard',
            'memory-ram' => 'Memory (RAM)',
            'storage' => 'Storage',
            'graphics-card' => 'Graphics Card',
            'power-supply' => 'Power Supply',
            'case' => 'PC Case',
        ];

        $presentCategories = $components->pluck('category.slug')->filter()->unique();
        $missingCategories = [];

        foreach ($essentialCategories as $slug => $name) {
            if (!$presentCategories->contains($slug)) {
                $missingCategories[] = [
                    'category_slug' => $slug,
                    'category_name' => $name,
                    'priority' => $this->getCategoryPriority($slug),
                ];
            }
        }

        // Sort by priority (higher priority first)
        usort($missingCategories, function ($a, $b) {
            return $b['priority'] - $a['priority'];
        });

        return $missingCategories;
    }

    /**
     * Get priority level for component categories.
     *
     * @param string $categorySlug
     * @return int
     */
    protected function getCategoryPriority(string $categorySlug): int
    {
        $priorities = [
            'cpu' => 10,
            'motherboard' => 9,
            'memory-ram' => 8,
            'power-supply' => 7,
            'storage' => 6,
            'graphics-card' => 5,
            'case' => 4,
        ];

        return $priorities[$categorySlug] ?? 1;
    }

    /**
     * Get shipping optimization suggestions.
     *
     * @param Cart $cart
     * @return array|null
     */
    protected function getShippingOptimization(Cart $cart): ?array
    {
        $subtotal = $cart->total ?? 0;
        $freeShippingThreshold = 100.00;

        if ($subtotal < $freeShippingThreshold) {
            $amountNeeded = $freeShippingThreshold - $subtotal;
            
            return [
                'type' => 'free_shipping',
                'title' => 'Free Shipping Available',
                'description' => "Add $" . number_format($amountNeeded, 2) . " more to qualify for free shipping",
                'current_total' => $subtotal,
                'threshold' => $freeShippingThreshold,
                'amount_needed' => $amountNeeded,
            ];
        }

        return null;
    }

    /**
     * Apply bulk pricing to eligible cart items.
     *
     * @param User|null $user
     * @return array
     */
    public function applyBulkPricing(?User $user = null): array
    {
        return $this->calculateBulkPricing($user);
    }

    /**
     * Get compatibility warnings for the cart.
     *
     * @param User|null $user
     * @return array
     */
    public function getCompatibilityWarnings(?User $user = null): array
    {
        $compatibilityResult = $this->checkCartCompatibility($user);
        
        return [
            'has_issues' => !$compatibilityResult['compatible'],
            'issues' => $compatibilityResult['issues'],
            'warnings' => $compatibilityResult['warnings'],
        ];
    }
}