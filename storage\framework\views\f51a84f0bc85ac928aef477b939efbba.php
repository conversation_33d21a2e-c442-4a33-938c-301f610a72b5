<section class="bg-bg-light dark:bg-bg-dark py-8 md:py-12 mt-6 transition-colors duration-300">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col items-center w-full space-y-4 md:space-y-6">
            <?php if(is_array($segments) && !empty($segments)): ?>
                <h1
                    class="text-3xl md:text-4xl lg:text-5xl font-bold text-center text-text-primary-light dark:text-text-primary-dark break-words tracking-tight transition-colors duration-300">
                    <?php echo e(__($pageTitle)); ?>

                </h1>

                <nav aria-label="Breadcrumb" class="w-full">
                    <div class="overflow-x-auto scrollbar-hide">
                        <ul
                            class="flex items-center justify-center text-sm text-text-secondary-light dark:text-text-secondary-dark space-x-2">
                            <li>
                                <a href="<?php echo e(route('home')); ?>"
                                    class="flex items-center gap-1 hover:text-primary-light dark:hover:text-primary-dark transition-all duration-200 p-2 rounded-md hover:bg-white dark:hover:bg-slate-800"
                                    title="Home">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="w-4 h-4 flex-shrink-0 transition-transform duration-200 hover:scale-110"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round">
                                        <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
                                        <polyline points="9 22 9 12 15 12 15 22" />
                                    </svg>
                                    
                                </a>
                            </li>

                            <?php $__currentLoopData = $segments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $segment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li
                                    class="flex items-center before:content-['/'] before:mx-2 before:text-border-light dark:before:text-border-dark before:transition-colors before:duration-300">
                                    <?php if($loop->last): ?>
                                        <span
                                            class="font-medium truncate max-w-[150px] sm:max-w-xs text-text-primary-light dark:text-text-primary-dark px-2 py-1 bg-primary-light/10 dark:bg-primary-dark/10 rounded-md border border-primary-light/20 dark:border-primary-dark/20 transition-all duration-300"
                                            aria-current="page">
                                            <?php echo e(__($segment['name'])); ?>

                                        </span>
                                    <?php else: ?>
                                        <a href="<?php echo e($segment['url']); ?>"
                                            class="truncate max-w-[150px] sm:max-w-xs hover:text-primary-light dark:hover:text-primary-dark transition-all duration-200 px-2 py-1 rounded-md hover:bg-white dark:hover:bg-slate-800">
                                            <?php echo e(__($segment['name'])); ?>

                                        </a>
                                    <?php endif; ?>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                </nav>
            <?php else: ?>
                <div
                    class="bg-accent-light/20 dark:bg-accent-dark/20 border-l-4 border-accent-light dark:border-accent-dark p-4 rounded-md transition-all duration-300">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-accent-light dark:text-accent-dark transition-colors duration-300"
                                viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                    clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h4
                                class="text-sm font-medium text-text-primary-light dark:text-text-primary-dark transition-colors duration-300">
                                No Segment Available
                            </h4>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<style>
    /* Custom scrollbar styles */
    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }

    /* Enhanced hover effects */
    nav a:hover {
        transform: translateY(-1px);
    }

    /* Breadcrumb separator animation */
    nav li:hover::before {
        transform: scale(1.2);
    }

    /* Custom focus states */
    nav a:focus {
        outline: 2px solid theme('colors.primary-light');
        outline-offset: 2px;
    }

    .dark nav a:focus {
        outline-color: theme('colors.primary-dark');
    }

    /* Smooth transitions for all elements */
    * {
        transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, transform 0.2s ease;
    }
</style><?php /**PATH C:\lara\www\pc-builder\resources\views/components/breadcrumb.blade.php ENDPATH**/ ?>