<?php $__env->startSection('title', 'Blog Categories'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-text-primary-light dark:text-text-primary-dark mb-2">Blog Categories</h1>
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="<?php echo e(route('admin.dashboard')); ?>" class="inline-flex items-center text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark hover:text-text-primary-light dark:hover:text-text-primary-dark transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                            </svg>
                            Dashboard
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-text-secondary-light dark:text-text-secondary-dark" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="ml-1 text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark md:ml-2">Blog Categories</span>
                        </div>
                    </li>
    </ol>
            </nav>
        </div>
        <a href="<?php echo e(route('admin.blog.categories.create')); ?>" class="inline-flex items-center px-4 py-2 bg-primary-light dark:bg-primary-dark text-white text-sm font-medium rounded-md hover:bg-primary-dark dark:hover:bg-primary-light focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-bg-dark focus:ring-primary-light dark:focus:ring-primary-dark transition-colors">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add New Category
        </a>
    </div>

    <div class="bg-white dark:bg-bg-dark shadow rounded-lg border border-border-light dark:border-border-dark">
        <div class="px-6 py-4 border-b border-border-light dark:border-border-dark bg-bg-light dark:bg-gray-700 rounded-t-lg">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-accent-light dark:text-accent-dark mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                <h2 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">Blog Categories</h2>
            </div>
        </div>
        
        <div class="p-6">
            <?php if(session('success')): ?>
                <div class="mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 text-green-700 dark:text-green-300 px-4 py-3 rounded-md">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-green-400 dark:text-green-500" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                    <?php echo e(session('success')); ?>

                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 px-4 py-3 rounded-md">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400 dark:text-red-500" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                    <?php echo e(session('error')); ?>

                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 dark:ring-white dark:ring-opacity-10 rounded-lg">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-border-light dark:divide-border-dark hidden md:table">
                        <thead class="bg-bg-light dark:bg-bg-dark">
                        <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Name</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Slug</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider hidden md:table-cell">Description</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider hidden md:table-cell">Posts</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider hidden md:table-cell">Order</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                        <tbody class="bg-white dark:bg-bg-dark divide-y divide-border-light dark:divide-border-dark">
                        <?php $__empty_1 = true; $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr class="hover:bg-bg-light dark:hover:bg-gray-800/50 transition-colors">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-text-primary-light dark:text-text-primary-dark">
                                        <?php echo e($category->name); ?>

                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                        <?php echo e($category->slug); ?>

                                    </td>
                                    <td class="px-6 py-4 text-sm text-text-secondary-light dark:text-text-secondary-dark max-w-xs hidden md:table-cell">
                                        <div class="truncate">
                                            <?php echo e(Str::limit($category->description, 50)); ?>

                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark hidden md:table-cell">
                                        <?php echo e($category->posts_count); ?>

                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark hidden md:table-cell">
                                        <?php echo e($category->display_order); ?>

                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <a href="<?php echo e(route('admin.blog.categories.edit', $category)); ?>" class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-primary-light dark:text-primary-dark bg-primary-light/10 dark:bg-primary-dark/20 hover:bg-primary-light/20 dark:hover:bg-primary-dark/30 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-bg-dark focus:ring-primary-light dark:focus:ring-primary-dark transition-colors">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                                Edit
                                        </a>
                                            <form action="<?php echo e(route('admin.blog.categories.destroy', $category)); ?>" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this category?')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-red-700 dark:text-red-400 bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-900/50 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-bg-dark focus:ring-red-500 transition-colors" <?php echo e($category->posts_count > 0 ? 'disabled' : ''); ?>>
                                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                    </svg>
                                                    Delete
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                    <td colspan="6" class="px-6 py-12 text-center text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                        <div class="flex flex-col items-center">
                                            <svg class="mx-auto h-12 w-12 text-text-secondary-light dark:text-text-secondary-dark" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                                <path d="M34 40h10v-4a6 6 0 00-10.712-3.714M34 40H14m20 0v-4a9.971 9.971 0 00-.712-3.714M14 40H4v-4a6 6 0 0110.713-3.714M14 40v-4c0-1.313.253-2.566.713-3.714m0 0A10.003 10.003 0 0124 26c4.21 0 7.813 2.602 9.288 6.286M30 14a6 6 0 11-12 0 6 6 0 0112 0zm12 6a4 4 0 11-8 0 4 4 0 018 0zm-28 0a4 4 0 11-8 0 4 4 0 018 0z" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" />
                                            </svg>
                                            <h3 class="mt-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">No categories found</h3>
                                            <p class="mt-1 text-sm text-text-secondary-light dark:text-text-secondary-dark">Get started by creating a new category.</p>
                                        </div>
                                    </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
                <!-- Mobile Card View -->
                <div class="md:hidden">
                    <?php $__empty_1 = true; $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="mb-4 p-4 bg-white dark:bg-bg-dark rounded-lg shadow border border-border-light dark:border-border-dark">
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-semibold text-text-primary-light dark:text-text-primary-dark"><?php echo e($category->name); ?></span>
                                <span class="text-xs text-text-secondary-light dark:text-text-secondary-dark"><?php echo e($category->slug); ?></span>
                            </div>
                            <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark mb-1"><span class="font-medium">Description:</span> <?php echo e(Str::limit($category->description, 50)); ?></div>
                            <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark mb-1"><span class="font-medium">Posts:</span> <?php echo e($category->posts_count); ?></div>
                            <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark mb-1"><span class="font-medium">Order:</span> <?php echo e($category->display_order); ?></div>
                            <div class="flex space-x-2 mt-2">
                                <a href="<?php echo e(route('admin.blog.categories.edit', $category)); ?>" class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-primary-light dark:text-primary-dark bg-primary-light/10 dark:bg-primary-dark/20 hover:bg-primary-light/20 dark:hover:bg-primary-dark/30 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-bg-dark focus:ring-primary-light dark:focus:ring-primary-dark transition-colors">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                    Edit
                                </a>
                                <form action="<?php echo e(route('admin.blog.categories.destroy', $category)); ?>" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this category?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-red-700 dark:text-red-400 bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-900/50 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-bg-dark focus:ring-red-500 transition-colors" <?php echo e($category->posts_count > 0 ? 'disabled' : ''); ?>>
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                        Delete
                                    </button>
                                </form>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="p-4 text-center text-text-secondary-light dark:text-text-secondary-dark">No categories found.</div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="mt-6">
                <?php echo e($categories->links()); ?>

            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\lara\www\pc-builder\resources\views/admin/blog/categories/index.blade.php ENDPATH**/ ?>