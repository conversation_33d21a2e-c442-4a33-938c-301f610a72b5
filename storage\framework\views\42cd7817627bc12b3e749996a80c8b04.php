<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['comment']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['comment']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="mb-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl"
    id="comment-<?php echo e($comment->id); ?>">
    <div class="p-6">
        <div class="flex justify-between items-start">
            <div class="flex items-center gap-3">
                <div class="relative">
                    <div
                        class="bg-gradient-to-br from-primary-500 to-primary-600 text-white rounded-full w-10 h-10 flex items-center justify-center shadow-md">
                        <span class="text-sm font-medium"><?php echo e(substr($comment->author->name, 0, 2)); ?></span>
                    </div>
                </div>
                <div>
                    <h4 class="font-semibold text-gray-900 dark:text-white"><?php echo e($comment->author->name); ?></h4>
                    <span class="text-sm text-gray-500 dark:text-gray-400">
                        <?php echo e($comment->created_at->diffForHumans()); ?>

                    </span>
                </div>
            </div>

            <?php if(auth()->guard()->check()): ?>
                <?php if(Auth::id() === $comment->user_id): ?>
                    <div class="relative group">
                        <button
                            class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                class="w-5 h-5 stroke-current text-gray-500 dark:text-gray-400">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z">
                                </path>
                            </svg>
                        </button>
                        <ul
                            class="hidden group-hover:block absolute right-0 mt-1 py-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-xl z-10 border border-gray-200 dark:border-gray-700">
                            <li>
                                <button onclick="toggleEditForm(<?php echo e($comment->id); ?>)"
                                    class="w-full text-left px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                                    Edit
                                </button>
                            </li>
                            <li>
                                <form action="<?php echo e(route('comments.delete', $comment)); ?>" method="POST" class="w-full">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit"
                                        class="w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200">
                                        Delete
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>

        <div class="mt-4">
            <p class="text-gray-700 dark:text-gray-300 leading-relaxed"><?php echo e(strip_tags($comment->content)); ?></p>
        </div>

        
        <div id="edit-form-<?php echo e($comment->id); ?>" class="hidden mt-4">
            <form action="<?php echo e(route('comments.update', $comment)); ?>" method="POST" class="space-y-4">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PATCH'); ?>
                <textarea name="content"
                    class="w-full px-4 py-3 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white transition-colors duration-200"
                    required maxlength="1000" rows="3"><?php echo e(strip_tags($comment->content)); ?></textarea>
                <div class="flex justify-between items-center">
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        <span id="char-count-<?php echo e($comment->id); ?>">0</span>/1000 characters
                    </div>
                    <div class="space-x-2">
                        <button type="button" onclick="toggleEditForm(<?php echo e($comment->id); ?>)"
                            class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200">
                            Cancel
                        </button>
                        <button type="submit"
                            class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors duration-200">
                            Update
                        </button>
                    </div>
                </div>
            </form>
        </div>

        
        <?php if(auth()->guard()->check()): ?>
            <div class="mt-4">
                <button onclick="toggleReplyForm(<?php echo e($comment->id); ?>)"
                    class="px-4 py-2 text-primary-600 dark:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-lg transition-colors duration-200">
                    Reply
                </button>
                <div id="reply-form-<?php echo e($comment->id); ?>" class="hidden mt-4">
                    <form action="<?php echo e(route('comments.store', $comment->post)); ?>" method="POST" class="space-y-4">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="parent_id" value="<?php echo e($comment->id); ?>">
                        <textarea name="content"
                            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white transition-colors duration-200"
                            placeholder="Write your reply..." required rows="3"></textarea>
                        <div class="flex justify-end space-x-2">
                            <button type="button" onclick="toggleReplyForm(<?php echo e($comment->id); ?>)"
                                class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200">
                                Cancel
                            </button>
                            <button type="submit"
                                class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors duration-200">
                                Submit Reply
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        <?php endif; ?>

        
        <?php if($comment->replies->count() > 0): ?>
            <div class="mt-6 border-t border-gray-200 dark:border-gray-700"></div>
            <div class="ml-8 space-y-4">
                <?php $__currentLoopData = $comment->replies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reply): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if (isset($component)) { $__componentOriginalfe4855bb643954c83a0cbd6710da1102 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalfe4855bb643954c83a0cbd6710da1102 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.comment','data' => ['comment' => $reply]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('comment'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['comment' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($reply)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalfe4855bb643954c83a0cbd6710da1102)): ?>
<?php $attributes = $__attributesOriginalfe4855bb643954c83a0cbd6710da1102; ?>
<?php unset($__attributesOriginalfe4855bb643954c83a0cbd6710da1102); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalfe4855bb643954c83a0cbd6710da1102)): ?>
<?php $component = $__componentOriginalfe4855bb643954c83a0cbd6710da1102; ?>
<?php unset($__componentOriginalfe4855bb643954c83a0cbd6710da1102); ?>
<?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php endif; ?>
    </div>
</div><?php /**PATH C:\lara\www\pc-builder\resources\views/components/comment.blade.php ENDPATH**/ ?>