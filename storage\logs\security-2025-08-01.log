[2025-08-01 14:28:11] testing.INFO: Payment admin action {"user_id":659,"action":"admin.gateways.update","ip":"127.0.0.1","timestamp":"2025-08-01T14:28:11.686541Z"} 
[2025-08-01 14:28:11] testing.INFO: Payment admin action {"user_id":661,"action":"admin.gateways.update","ip":"127.0.0.1","timestamp":"2025-08-01T14:28:11.739530Z"} 
[2025-08-01 14:28:11] testing.INFO: Payment admin action {"user_id":663,"action":"admin.gateways.update","ip":"127.0.0.1","timestamp":"2025-08-01T14:28:11.787647Z"} 
[2025-08-01 14:28:12] testing.INFO: Payment admin action {"user_id":689,"action":"admin.gateways.update","ip":"127.0.0.1","timestamp":"2025-08-01T14:28:12.766662Z"} 
[2025-08-01 14:28:12] testing.INFO: Payment admin action {"user_id":693,"action":"admin.gateways.update","ip":"127.0.0.1","timestamp":"2025-08-01T14:28:12.876529Z"} 
[2025-08-01 14:28:21] testing.INFO: Payment admin action {"user_id":842,"action":"admin.gateways.update","ip":"127.0.0.1","timestamp":"2025-08-01T14:28:21.115197Z"} 
[2025-08-01 14:29:08] testing.INFO: Webhook received {"gateway":"razorpay","ip":"127.0.0.1","user_agent":"Symfony","payload_size":144,"headers":{"host":["127.0.0.1:8000"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["144"],"content-type":["application/json"],"x-razorpay-signature":["[REDACTED]"]},"timestamp":"2025-08-01T14:29:08.458145Z"} 
[2025-08-01 14:29:08] testing.INFO: Webhook received {"gateway":"cashfree","ip":"127.0.0.1","user_agent":"Symfony","payload_size":193,"headers":{"host":["127.0.0.1:8000"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["193"],"content-type":["application/json"],"x-webhook-signature":["valid_signature"],"x-webhook-timestamp":["1754058548"]},"timestamp":"2025-08-01T14:29:08.564988Z"} 
[2025-08-01 14:29:08] testing.WARNING: Webhook security violation {"type":"invalid_signature","gateway":"razorpay","ip":"127.0.0.1","timestamp":"2025-08-01T14:29:08.612699Z","details":null} 
[2025-08-01 14:48:25] testing.INFO: Payment admin action {"user_id":692,"action":"admin.gateways.update","ip":"127.0.0.1","timestamp":"2025-08-01T14:48:25.643623Z"} 
[2025-08-01 14:48:25] testing.INFO: Payment admin action {"user_id":694,"action":"admin.gateways.update","ip":"127.0.0.1","timestamp":"2025-08-01T14:48:25.706706Z"} 
[2025-08-01 14:48:25] testing.INFO: Payment admin action {"user_id":696,"action":"admin.gateways.update","ip":"127.0.0.1","timestamp":"2025-08-01T14:48:25.752537Z"} 
[2025-08-01 14:48:26] testing.INFO: Payment admin action {"user_id":722,"action":"admin.gateways.update","ip":"127.0.0.1","timestamp":"2025-08-01T14:48:26.204904Z"} 
[2025-08-01 14:48:26] testing.INFO: Payment admin action {"user_id":726,"action":"admin.gateways.update","ip":"127.0.0.1","timestamp":"2025-08-01T14:48:26.297129Z"} 
[2025-08-01 14:48:32] testing.INFO: Payment admin action {"user_id":875,"action":"admin.gateways.update","ip":"127.0.0.1","timestamp":"2025-08-01T14:48:32.232657Z"} 
[2025-08-01 14:49:07] testing.INFO: Webhook received {"gateway":"razorpay","ip":"127.0.0.1","user_agent":"Symfony","payload_size":144,"headers":{"host":["127.0.0.1:8000"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["144"],"content-type":["application/json"],"x-razorpay-signature":["[REDACTED]"]},"timestamp":"2025-08-01T14:49:07.520083Z"} 
[2025-08-01 14:49:07] testing.INFO: Webhook received {"gateway":"cashfree","ip":"127.0.0.1","user_agent":"Symfony","payload_size":193,"headers":{"host":["127.0.0.1:8000"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["193"],"content-type":["application/json"],"x-webhook-signature":["valid_signature"],"x-webhook-timestamp":["1754059747"]},"timestamp":"2025-08-01T14:49:07.622114Z"} 
[2025-08-01 14:49:07] testing.WARNING: Webhook security violation {"type":"invalid_signature","gateway":"razorpay","ip":"127.0.0.1","timestamp":"2025-08-01T14:49:07.689485Z","details":null} 
[2025-08-01 14:50:40] testing.INFO: Payment admin action {"user_id":692,"action":"admin.gateways.update","ip":"127.0.0.1","timestamp":"2025-08-01T14:50:40.701102Z"} 
[2025-08-01 14:50:40] testing.INFO: Payment admin action {"user_id":694,"action":"admin.gateways.update","ip":"127.0.0.1","timestamp":"2025-08-01T14:50:40.747659Z"} 
[2025-08-01 14:50:40] testing.INFO: Payment admin action {"user_id":696,"action":"admin.gateways.update","ip":"127.0.0.1","timestamp":"2025-08-01T14:50:40.797227Z"} 
[2025-08-01 14:50:41] testing.INFO: Payment admin action {"user_id":722,"action":"admin.gateways.update","ip":"127.0.0.1","timestamp":"2025-08-01T14:50:41.250008Z"} 
[2025-08-01 14:50:41] testing.INFO: Payment admin action {"user_id":726,"action":"admin.gateways.update","ip":"127.0.0.1","timestamp":"2025-08-01T14:50:41.346075Z"} 
[2025-08-01 14:50:47] testing.INFO: Payment admin action {"user_id":875,"action":"admin.gateways.update","ip":"127.0.0.1","timestamp":"2025-08-01T14:50:47.480761Z"} 
[2025-08-01 14:51:23] testing.INFO: Webhook received {"gateway":"razorpay","ip":"127.0.0.1","user_agent":"Symfony","payload_size":144,"headers":{"host":["127.0.0.1:8000"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["144"],"content-type":["application/json"],"x-razorpay-signature":["[REDACTED]"]},"timestamp":"2025-08-01T14:51:23.003839Z"} 
[2025-08-01 14:51:23] testing.INFO: Webhook received {"gateway":"cashfree","ip":"127.0.0.1","user_agent":"Symfony","payload_size":193,"headers":{"host":["127.0.0.1:8000"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["193"],"content-type":["application/json"],"x-webhook-signature":["valid_signature"],"x-webhook-timestamp":["1754059883"]},"timestamp":"2025-08-01T14:51:23.110317Z"} 
[2025-08-01 14:51:23] testing.WARNING: Webhook security violation {"type":"invalid_signature","gateway":"razorpay","ip":"127.0.0.1","timestamp":"2025-08-01T14:51:23.170539Z","details":null} 
