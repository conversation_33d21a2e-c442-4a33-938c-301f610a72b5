<?php

namespace App\Models;

use App\Contracts\Purchasable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CartItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'cart_id',
        'component_id',
        'product_id',
        'item_type',
        'quantity',
        'price',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'decimal:2',
        'quantity' => 'integer',
        'metadata' => 'array',
    ];

    /**
     * Item type constants.
     */
    public const TYPE_COMPONENT = 'component';
    public const TYPE_PRODUCT = 'product';

    /**
     * Get the cart that owns the item.
     */
    public function cart(): BelongsTo
    {
        return $this->belongsTo(Cart::class);
    }

    /**
     * Get the component for the cart item.
     */
    public function component(): BelongsTo
    {
        return $this->belongsTo(Component::class);
    }

    /**
     * Get the product for the cart item.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the unified item (component or product) that implements Purchasable interface.
     */
    public function item(): Purchasable
    {
        return $this->item_type === self::TYPE_PRODUCT ? $this->product : $this->component;
    }

    /**
     * Get display methods using interface methods.
     */
    public function getDisplayName(): string
    {
        $item = $this->item();
        if (!$item) {
            return 'Unknown Item';
        }

        $brand = $item->getBrand();
        $model = $item->getModel();
        
        if ($brand && $model) {
            return "{$brand} {$model} - {$item->getName()}";
        }
        
        return $item->getName();
    }

    public function getDisplayImage(): ?string
    {
        $item = $this->item();
        return $item ? $item->getImage() : null;
    }

    /**
     * Stock validation using interface methods.
     */
    public function isInStock(): bool
    {
        $item = $this->item();
        return $item ? $item->isAvailable($this->quantity) : false;
    }

    public function getMaxQuantity(): int
    {
        $item = $this->item();
        if (!$item) {
            return 0;
        }

        $stock = $item->getStock();
        
        // For products, check if they have max order quantity
        if ($this->item_type === self::TYPE_PRODUCT && method_exists($item, 'max_order_quantity') && $item->max_order_quantity) {
            return min($stock, $item->max_order_quantity);
        }
        
        return $stock;
    }

    /**
     * Check if the item can be added to cart with the specified quantity.
     */
    public function canAddQuantity(int $additionalQuantity = 1): bool
    {
        $item = $this->item();
        if (!$item) {
            return false;
        }

        $totalQuantity = $this->quantity + $additionalQuantity;
        return $item->isAvailable($totalQuantity) && $totalQuantity <= $this->getMaxQuantity();
    }

    /**
     * Legacy methods for backward compatibility.
     */
    public function getItemAttribute()
    {
        return $this->item();
    }

    public function getItemNameAttribute(): string
    {
        return $this->getDisplayName();
    }

    public function getItemPriceAttribute(): float
    {
        $item = $this->item();
        if (!$item) {
            return (float) $this->price;
        }

        return $item->getEffectivePrice();
    }

    public function getTotalAttribute()
    {
        return $this->price * $this->quantity;
    }

    /**
     * Update the price based on the current item price.
     */
    public function updatePrice()
    {
        $item = $this->item();
        if ($item) {
            $this->price = $item->getEffectivePrice();
            $this->save();
        }
        
        return $this;
    }

    /**
     * Price calculation methods.
     */
    public function getUnitPrice(): float
    {
        return (float) $this->price;
    }

    public function getTotalPrice(): float
    {
        return $this->getUnitPrice() * $this->quantity;
    }

    public function getEffectiveUnitPrice(): float
    {
        $item = $this->item();
        if (!$item) {
            return $this->getUnitPrice();
        }

        // For products with bulk pricing, calculate based on quantity
        if ($this->item_type === self::TYPE_PRODUCT && method_exists($item, 'getBulkPrice')) {
            return $item->getBulkPrice($this->quantity);
        }

        return $item->getEffectivePrice();
    }

    public function getEffectiveTotalPrice(): float
    {
        return $this->getEffectiveUnitPrice() * $this->quantity;
    }

    public function getSavingsAmount(): float
    {
        $regularTotal = $this->getTotalPrice();
        $effectiveTotal = $this->getEffectiveTotalPrice();
        return max(0, $regularTotal - $effectiveTotal);
    }

    /**
     * Stock checking methods.
     */
    public function hasEnoughStock(): bool
    {
        return $this->isInStock();
    }

    public function getStockStatus(): string
    {
        $item = $this->item();
        if (!$item) {
            return 'unavailable';
        }

        $stock = $item->getStock();
        $quantity = $this->quantity;

        if (!$item->isAvailable($quantity)) {
            return 'out_of_stock';
        }

        if ($stock < $quantity) {
            return 'insufficient_stock';
        }

        if ($stock <= 5) {
            return 'low_stock';
        }

        return 'in_stock';
    }

    public function getStockMessage(): string
    {
        $status = $this->getStockStatus();
        $item = $this->item();
        
        switch ($status) {
            case 'out_of_stock':
                return 'Out of stock';
            case 'insufficient_stock':
                return "Only {$item->getStock()} available";
            case 'low_stock':
                return "Low stock ({$item->getStock()} remaining)";
            case 'in_stock':
                return 'In stock';
            default:
                return 'Unavailable';
        }
    }

    /**
     * Metadata handling for cart customization.
     */
    public function setMetadata(string $key, $value): self
    {
        $metadata = $this->metadata ?? [];
        $metadata[$key] = $value;
        $this->metadata = $metadata;
        return $this;
    }

    public function getMetadata(string $key = null, $default = null)
    {
        if ($key === null) {
            return $this->metadata ?? [];
        }

        return $this->metadata[$key] ?? $default;
    }

    public function hasMetadata(string $key): bool
    {
        return isset($this->metadata[$key]);
    }

    public function removeMetadata(string $key): self
    {
        if ($this->metadata && isset($this->metadata[$key])) {
            $metadata = $this->metadata;
            unset($metadata[$key]);
            $this->metadata = $metadata;
        }
        return $this;
    }

    public function clearMetadata(): self
    {
        $this->metadata = [];
        return $this;
    }

    /**
     * Additional helper methods for cart customization.
     */
    public function setCustomization(array $customization): self
    {
        return $this->setMetadata('customization', $customization);
    }

    public function getCustomization(): array
    {
        return $this->getMetadata('customization', []);
    }

    public function hasCustomization(): bool
    {
        return $this->hasMetadata('customization') && !empty($this->getCustomization());
    }

    public function setNote(string $note): self
    {
        return $this->setMetadata('note', $note);
    }

    public function getNote(): ?string
    {
        return $this->getMetadata('note');
    }

    public function hasNote(): bool
    {
        return $this->hasMetadata('note') && !empty($this->getNote());
    }

    /**
     * Quantity management methods.
     */
    public function canIncreaseQuantity(int $amount = 1): bool
    {
        return $this->canAddQuantity($amount);
    }

    public function canDecreaseQuantity(int $amount = 1): bool
    {
        return $this->quantity > $amount;
    }

    public function increaseQuantity(int $amount = 1): bool
    {
        if (!$this->canIncreaseQuantity($amount)) {
            return false;
        }

        $this->quantity += $amount;
        $this->save();
        return true;
    }

    public function decreaseQuantity(int $amount = 1): bool
    {
        if (!$this->canDecreaseQuantity($amount)) {
            return false;
        }

        $this->quantity -= $amount;
        $this->save();
        return true;
    }

    public function setQuantity(int $quantity): bool
    {
        if ($quantity <= 0) {
            return false;
        }

        $item = $this->item();
        if (!$item || !$item->isAvailable($quantity)) {
            return false;
        }

        if ($quantity > $this->getMaxQuantity()) {
            return false;
        }

        $this->quantity = $quantity;
        $this->save();
        return true;
    }
}