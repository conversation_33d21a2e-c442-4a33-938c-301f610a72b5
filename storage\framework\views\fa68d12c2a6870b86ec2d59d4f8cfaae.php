

<?php $__env->startSection('title', 'Payment Gateways'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Payment Gateways</h1>
            <p class="text-gray-600">Manage your payment gateway configurations</p>
        </div>

        <?php if(session('success')): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6" role="alert">
                <div class="flex">
                    <div class="py-1">
                        <svg class="fill-current h-6 w-6 text-green-500 mr-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                            <path d="M2.93 17.07A10 10 0 1 1 17.07 2.93 10 10 0 0 1 2.93 17.07zm12.73-1.41A8 8 0 1 0 4.34 4.34a8 8 0 0 0 11.32 11.32zM9 11V9h2v6H9v-4zm0-6h2v2H9V5z"/>
                        </svg>
                    </div>
                    <div>
                        <?php echo e(session('success')); ?>

                    </div>
                </div>
            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6" role="alert">
                <div class="flex">
                    <div class="py-1">
                        <svg class="fill-current h-6 w-6 text-red-500 mr-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                            <path d="M2.93 17.07A10 10 0 1 1 17.07 2.93 10 10 0 0 1 2.93 17.07zm1.41-1.41A8 8 0 1 0 15.66 4.34 8 8 0 0 0 4.34 15.66zm9.9-8.49L11.41 10l2.83 2.83-1.41 1.41L10 11.41l-2.83 2.83-1.41-1.41L8.59 10 5.76 7.17l1.41-1.41L10 8.59l2.83-2.83 1.41 1.41z"/>
                        </svg>
                    </div>
                    <div>
                        <?php echo e(session('error')); ?>

                    </div>
                </div>
            </div>
        <?php endif; ?>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php $__currentLoopData = $supportedGateways; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gateway): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php
                    $setting = $settings->firstWhere('gateway_name', $gateway);
                    $isEnabled = $setting?->is_enabled ?? false;
                    $isTestMode = $setting?->is_test_mode ?? true;
                    $hasConfiguration = $setting && !empty($setting->settings);
                ?>
                
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                                    <?php if($gateway === 'razorpay'): ?>
                                        <img src="<?php echo e(asset('images/gateways/razorpay-logo.svg')); ?>" alt="Razorpay" class="w-8 h-8">
                                    <?php elseif($gateway === 'payumoney'): ?>
                                        <img src="<?php echo e(asset('images/gateways/payumoney-logo.svg')); ?>" alt="PayUmoney" class="w-8 h-8">
                                    <?php elseif($gateway === 'cashfree'): ?>
                                        <img src="<?php echo e(asset('images/gateways/cashfree-logo.svg')); ?>" alt="Cashfree" class="w-8 h-8">
                                    <?php else: ?>
                                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                        </svg>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900"><?php echo e(ucfirst($gateway)); ?></h3>
                                    <p class="text-sm text-gray-500">Payment Gateway</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center">
                                <span class="text-sm text-gray-500 mr-2"><?php echo e($isEnabled ? 'Enabled' : 'Disabled'); ?></span>
                                <button onclick="toggleGateway('<?php echo e($gateway); ?>', <?php echo e($isEnabled ? 'false' : 'true'); ?>)" 
                                        class="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 <?php echo e($isEnabled ? 'bg-indigo-600' : 'bg-gray-200'); ?>"
                                        <?php echo e(!$hasConfiguration ? 'disabled title="Configure gateway first"' : ''); ?>>
                                    <span class="sr-only">Toggle gateway</span>
                                    <span class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform <?php echo e($isEnabled ? 'translate-x-6' : 'translate-x-1'); ?>"></span>
                                </button>
                            </div>
                        </div>
                        
                        <div class="space-y-3 mb-6">
                            <div class="flex justify-between items-center text-sm">
                                <span class="text-gray-600">Mode:</span>
                                <div class="flex items-center">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($isTestMode ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'); ?>">
                                        <?php echo e($isTestMode ? 'Test' : 'Live'); ?>

                                    </span>
                                    <?php if($hasConfiguration): ?>
                                        <button onclick="switchMode('<?php echo e($gateway); ?>', <?php echo e($isTestMode ? 'false' : 'true'); ?>)" 
                                                class="ml-2 text-indigo-600 hover:text-indigo-800 text-xs font-medium">
                                            Switch to <?php echo e($isTestMode ? 'Live' : 'Test'); ?>

                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="flex justify-between items-center text-sm">
                                <span class="text-gray-600">Status:</span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($isEnabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                    <?php echo e($isEnabled ? 'Active' : 'Inactive'); ?>

                                </span>
                            </div>
                            
                            <div class="flex justify-between items-center text-sm">
                                <span class="text-gray-600">Configuration:</span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($hasConfiguration ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'); ?>">
                                    <?php echo e($hasConfiguration ? 'Configured' : 'Not Configured'); ?>

                                </span>
                            </div>
                        </div>
                        
                        <div class="flex space-x-2">
                            <a href="<?php echo e(route('admin.gateways.show', $gateway)); ?>" 
                               class="flex-1 bg-indigo-600 text-white text-center py-2 px-4 rounded-md hover:bg-indigo-700 transition duration-200 text-sm font-medium">
                                Configure
                            </a>
                            
                            <?php if($hasConfiguration): ?>
                                <button onclick="testGateway('<?php echo e($gateway); ?>')" 
                                        class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition duration-200 text-sm font-medium">
                                    Test
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <?php if($supportedGateways->isEmpty()): ?>
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No payment gateways</h3>
                <p class="mt-1 text-sm text-gray-500">No payment gateways are currently supported.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Loading Modal -->
<div id="loadingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 max-w-sm mx-auto">
        <div class="flex items-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span id="loadingText">Processing...</span>
        </div>
    </div>
</div>

<script>
function showLoading(text = 'Processing...') {
    document.getElementById('loadingText').textContent = text;
    document.getElementById('loadingModal').classList.remove('hidden');
    document.getElementById('loadingModal').classList.add('flex');
}

function hideLoading() {
    document.getElementById('loadingModal').classList.add('hidden');
    document.getElementById('loadingModal').classList.remove('flex');
}

async function toggleGateway(gateway, enabled) {
    showLoading(enabled ? 'Enabling gateway...' : 'Disabling gateway...');
    
    try {
        const response = await fetch(`/admin/gateways/${gateway}/toggle`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({ enabled: enabled })
        });
        
        const data = await response.json();
        
        if (response.ok && data.success) {
            location.reload();
        } else {
            alert(data.message || 'Failed to toggle gateway');
        }
    } catch (error) {
        alert('Error: ' + error.message);
    } finally {
        hideLoading();
    }
}

async function switchMode(gateway, testMode) {
    showLoading(testMode ? 'Switching to test mode...' : 'Switching to live mode...');
    
    try {
        const response = await fetch(`/admin/gateways/${gateway}/switch-mode`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({ test_mode: testMode })
        });
        
        const data = await response.json();
        
        if (response.ok && data.success) {
            location.reload();
        } else {
            alert(data.message || 'Failed to switch mode');
        }
    } catch (error) {
        alert('Error: ' + error.message);
    } finally {
        hideLoading();
    }
}

async function testGateway(gateway) {
    showLoading('Testing gateway configuration...');
    
    try {
        const response = await fetch(`/admin/gateways/${gateway}/test`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            if (data.success) {
                alert('✅ Gateway test successful!');
            } else {
                alert('❌ Gateway test failed: ' + data.message);
            }
        } else {
            alert('❌ Test failed: ' + (data.message || 'Unknown error'));
        }
    } catch (error) {
        alert('❌ Error: ' + error.message);
    } finally {
        hideLoading();
    }
}
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\lara\www\pc-builder\resources\views/admin/gateways/index.blade.php ENDPATH**/ ?>