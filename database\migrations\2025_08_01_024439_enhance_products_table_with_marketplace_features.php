<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Marketplace fields (removed vendor_id as this is single vendor store)
            $table->integer('warranty_months')->default(12)->after('meta_data');
            $table->enum('condition', ['new', 'refurbished', 'used'])->default('new')->after('warranty_months');
            $table->string('origin_country', 2)->nullable()->after('condition');

            // Bulk pricing and inventory management fields
            $table->integer('min_order_quantity')->default(1)->after('origin_country');
            $table->integer('max_order_quantity')->nullable()->after('min_order_quantity');
            $table->json('bulk_pricing_json')->nullable()->after('max_order_quantity');
            $table->integer('low_stock_threshold')->default(5)->after('bulk_pricing_json');

            // SEO and shipping-related fields
            $table->string('seo_title')->nullable()->after('low_stock_threshold');
            $table->text('seo_description')->nullable()->after('seo_title');
            $table->json('tags_json')->nullable()->after('seo_description');
            $table->integer('shipping_weight_grams')->nullable()->after('tags_json');
            $table->json('shipping_dimensions_json')->nullable()->after('shipping_weight_grams');
            $table->decimal('shipping_cost', 8, 2)->nullable()->after('shipping_dimensions_json');
            $table->boolean('free_shipping')->default(false)->after('shipping_cost');

            // Additional marketplace fields
            $table->string('manufacturer_part_number', 100)->nullable()->after('free_shipping');
            $table->string('ean_code', 20)->nullable()->after('manufacturer_part_number');
            $table->date('release_date')->nullable()->after('ean_code');
            $table->timestamp('discontinued_at')->nullable()->after('release_date');

            // Performance indexes for product queries (removed vendor_id index)
            $table->index('status', 'idx_products_status');
            $table->index(['category_id', 'featured'], 'idx_products_category_featured');
            $table->index(['price', 'sale_price', 'status'], 'idx_products_price_range');
            $table->index(['condition', 'status'], 'idx_products_condition');
            $table->index(['brand', 'model'], 'idx_products_brand_model');
            $table->index('warranty_months', 'idx_products_warranty');
            $table->index('free_shipping', 'idx_products_free_shipping');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Drop indexes
            $table->dropIndex('idx_products_status');
            $table->dropIndex('idx_products_category_featured');
            $table->dropIndex('idx_products_price_range');
            $table->dropIndex('idx_products_condition');
            $table->dropIndex('idx_products_brand_model');
            $table->dropIndex('idx_products_warranty');
            $table->dropIndex('idx_products_free_shipping');

            // Drop columns (removed vendor_id)
            $table->dropColumn([
                'warranty_months',
                'condition',
                'origin_country',
                'min_order_quantity',
                'max_order_quantity',
                'bulk_pricing_json',
                'low_stock_threshold',
                'seo_title',
                'seo_description',
                'tags_json',
                'shipping_weight_grams',
                'shipping_dimensions_json',
                'shipping_cost',
                'free_shipping',
                'manufacturer_part_number',
                'ean_code',
                'release_date',
                'discontinued_at'
            ]);
        });
    }
};
