<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cart_items', function (Blueprint $table) {
            // Check if product_id column doesn't exist before adding it
            if (!Schema::hasColumn('cart_items', 'product_id')) {
                $table->foreignId('product_id')->nullable()->constrained()->onDelete('cascade');
            }

            // Check if item_type column doesn't exist before adding it
            if (!Schema::hasColumn('cart_items', 'item_type')) {
                $table->enum('item_type', ['component', 'product'])->default('component');
            }

            // Make component_id nullable since we now support products too
            $table->unsignedBigInteger('component_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cart_items', function (Blueprint $table) {
            // Make component_id not nullable again
            $table->unsignedBigInteger('component_id')->nullable(false)->change();

            // Drop the new columns
            $table->dropColumn(['product_id', 'item_type']);
        });
    }
};
