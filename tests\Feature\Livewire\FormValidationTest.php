<?php

namespace Tests\Feature\Livewire;

use App\Http\Livewire\Admin\ComponentForm;
use App\Http\Livewire\Admin\ProductForm;
use App\Models\ComponentCategory;
use App\Models\ProductCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class FormValidationTest extends TestCase
{
    use RefreshDatabase;

    protected User $adminUser;
    protected ComponentCategory $cpuCategory;
    protected ComponentCategory $motherboardCategory;
    protected ProductCategory $productCategory;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->adminUser = User::factory()->create([
            'role' => 'admin'
        ]);

        // Create component categories
        $this->cpuCategory = ComponentCategory::factory()->create(['name' => 'CPU', 'slug' => 'cpu']);
        $this->motherboardCategory = ComponentCategory::factory()->create(['name' => 'Motherboard', 'slug' => 'motherboard']);
        
        // Create product category
        $this->productCategory = ProductCategory::factory()->create();
    }

    /** @test */
    public function component_form_validates_basic_fields()
    {
        $this->actingAs($this->adminUser);

        Livewire::test(ComponentForm::class)
            ->set('name', '')
            ->set('slug', '')
            ->set('category_id', '')
            ->set('brand', '')
            ->set('model', '')
            ->set('price', '')
            ->set('stock', '')
            ->call('save')
            ->assertHasErrors([
                'name',
                'slug',
                'category_id',
                'brand',
                'model',
                'price',
                'stock'
            ]);

        // Test with invalid data
        Livewire::test(ComponentForm::class)
            ->set('name', 'a') // too short
            ->set('slug', 'invalid slug!')
            ->set('category_id', 9999) // non-existent
            ->set('brand', 'a') // too short
            ->set('model', '')
            ->set('price', -10) // negative
            ->set('stock', -5) // negative
            ->call('save')
            ->assertHasErrors([
                'name',
                'slug',
                'category_id',
                'brand',
                'model',
                'price',
                'stock'
            ]);
    }

    /** @test */
    public function component_form_validates_cpu_specific_fields()
    {
        $this->actingAs($this->adminUser);

        // Test CPU-specific validation
        Livewire::test(ComponentForm::class)
            ->set('name', 'Test CPU')
            ->set('slug', 'test-cpu')
            ->set('category_id', $this->cpuCategory->id)
            ->set('brand', 'Test Brand')
            ->set('model', 'Test Model')
            ->set('price', 299.99)
            ->set('stock', 10)
            // Missing socket_type and power_consumption
            ->call('save')
            ->assertHasErrors([
                'socket_type',
                'power_consumption'
            ]);

        // Test with valid CPU data
        Livewire::test(ComponentForm::class)
            ->set('name', 'Test CPU')
            ->set('slug', 'test-cpu')
            ->set('category_id', $this->cpuCategory->id)
            ->set('brand', 'Test Brand')
            ->set('model', 'Test Model')
            ->set('price', 299.99)
            ->set('stock', 10)
            ->set('socket_type', 'LGA1700')
            ->set('power_consumption', 65)
            ->set('is_active', true)
            ->call('save')
            ->assertHasNoErrors();
    }

    /** @test */
    public function component_form_validates_motherboard_specific_fields()
    {
        $this->actingAs($this->adminUser);

        // Test motherboard-specific validation
        Livewire::test(ComponentForm::class)
            ->set('name', 'Test Motherboard')
            ->set('slug', 'test-motherboard')
            ->set('category_id', $this->motherboardCategory->id)
            ->set('brand', 'Test Brand')
            ->set('model', 'Test Model')
            ->set('price', 199.99)
            ->set('stock', 10)
            // Missing socket_type, form_factor, and memory_type
            ->call('save')
            ->assertHasErrors([
                'socket_type',
                'form_factor',
                'memory_type'
            ]);

        // Test with valid motherboard data
        Livewire::test(ComponentForm::class)
            ->set('name', 'Test Motherboard')
            ->set('slug', 'test-motherboard')
            ->set('category_id', $this->motherboardCategory->id)
            ->set('brand', 'Test Brand')
            ->set('model', 'Test Model')
            ->set('price', 199.99)
            ->set('stock', 10)
            ->set('socket_type', 'LGA1700')
            ->set('form_factor', 'ATX')
            ->set('memory_type', 'DDR4')
            ->set('is_active', true)
            ->call('save')
            ->assertHasNoErrors();
    }

    /** @test */
    public function component_form_validates_compatibility_requirements()
    {
        $this->actingAs($this->adminUser);

        // Test discontinued component cannot be active
        Livewire::test(ComponentForm::class)
            ->set('name', 'Discontinued Component')
            ->set('slug', 'discontinued-component')
            ->set('category_id', $this->cpuCategory->id)
            ->set('brand', 'Test Brand')
            ->set('model', 'Test Model')
            ->set('price', 99.99)
            ->set('stock', 10)
            ->set('socket_type', 'LGA1700')
            ->set('power_consumption', 65)
            ->set('is_active', true)
            ->set('discontinued_at', now()->subDay()->format('Y-m-d'))
            ->call('save')
            ->assertHasErrors([
                'is_active'
            ]);

        // Test discontinuation date cannot be before release date
        Livewire::test(ComponentForm::class)
            ->set('name', 'Invalid Dates Component')
            ->set('slug', 'invalid-dates-component')
            ->set('category_id', $this->cpuCategory->id)
            ->set('brand', 'Test Brand')
            ->set('model', 'Test Model')
            ->set('price', 99.99)
            ->set('stock', 10)
            ->set('socket_type', 'LGA1700')
            ->set('power_consumption', 65)
            ->set('is_active', false)
            ->set('release_date', now()->format('Y-m-d'))
            ->set('discontinued_at', now()->subDays(2)->format('Y-m-d'))
            ->call('save')
            ->assertHasErrors([
                'discontinued_at'
            ]);

        // Test dimensions consistency
        Livewire::test(ComponentForm::class)
            ->set('name', 'Incomplete Dimensions')
            ->set('slug', 'incomplete-dimensions')
            ->set('category_id', $this->cpuCategory->id)
            ->set('brand', 'Test Brand')
            ->set('model', 'Test Model')
            ->set('price', 99.99)
            ->set('stock', 10)
            ->set('socket_type', 'LGA1700')
            ->set('power_consumption', 65)
            ->set('is_active', true)
            ->set('dimensions_json', [
                'length' => 100,
                'width' => 50
                // Missing height
            ])
            ->call('save')
            ->assertHasErrors([
                'dimensions_json'
            ]);
    }

    /** @test */
    public function product_form_validates_basic_fields()
    {
        $this->actingAs($this->adminUser);

        Livewire::test(ProductForm::class)
            ->set('name', '')
            ->set('slug', '')
            ->set('sku', '')
            ->set('category_id', '')
            ->set('brand', '')
            ->set('price', '')
            ->set('status', '')
            ->set('type', '')
            ->call('save')
            ->assertHasErrors([
                'name',
                'slug',
                'sku',
                'category_id',
                'brand',
                'price',
                'status',
                'type'
            ]);

        // Test with invalid data
        Livewire::test(ProductForm::class)
            ->set('name', 'a') // too short
            ->set('slug', 'invalid slug!')
            ->set('sku', '')
            ->set('category_id', 9999) // non-existent
            ->set('brand', 'a') // too short
            ->set('price', -10) // negative
            ->set('status', 'invalid-status')
            ->set('type', 'invalid-type')
            ->call('save')
            ->assertHasErrors([
                'name',
                'slug',
                'sku',
                'category_id',
                'brand',
                'price',
                'status',
                'type'
            ]);
    }

    /** @test */
    public function product_form_validates_pricing_fields()
    {
        $this->actingAs($this->adminUser);

        // Test sale price must be less than regular price
        Livewire::test(ProductForm::class)
            ->set('name', 'Test Product')
            ->set('slug', 'test-product')
            ->set('sku', 'TEST-SKU-001')
            ->set('category_id', $this->productCategory->id)
            ->set('brand', 'Test Brand')
            ->set('price', 99.99)
            ->set('sale_price', 109.99) // Higher than regular price
            ->set('status', 'published')
            ->set('type', 'simple')
            ->call('save')
            ->assertHasErrors([
                'sale_price'
            ]);

        // Test with valid sale price
        Livewire::test(ProductForm::class)
            ->set('name', 'Test Product')
            ->set('slug', 'test-product')
            ->set('sku', 'TEST-SKU-001')
            ->set('category_id', $this->productCategory->id)
            ->set('brand', 'Test Brand')
            ->set('price', 99.99)
            ->set('sale_price', 89.99) // Lower than regular price
            ->set('status', 'published')
            ->set('type', 'simple')
            ->call('save')
            ->assertHasNoErrors(['sale_price']);
    }

    /** @test */
    public function product_form_validates_stock_management_fields()
    {
        $this->actingAs($this->adminUser);

        // Test stock_quantity required when manage_stock is true
        Livewire::test(ProductForm::class)
            ->set('name', 'Test Product')
            ->set('slug', 'test-product')
            ->set('sku', 'TEST-SKU-001')
            ->set('category_id', $this->productCategory->id)
            ->set('brand', 'Test Brand')
            ->set('price', 99.99)
            ->set('status', 'published')
            ->set('type', 'simple')
            ->set('manage_stock', true)
            // Missing stock_quantity
            ->call('save')
            ->assertHasErrors([
                'stock_quantity'
            ]);

        // Test negative stock_quantity
        Livewire::test(ProductForm::class)
            ->set('name', 'Test Product')
            ->set('slug', 'test-product')
            ->set('sku', 'TEST-SKU-001')
            ->set('category_id', $this->productCategory->id)
            ->set('brand', 'Test Brand')
            ->set('price', 99.99)
            ->set('status', 'published')
            ->set('type', 'simple')
            ->set('manage_stock', true)
            ->set('stock_quantity', -5) // Negative
            ->call('save')
            ->assertHasErrors([
                'stock_quantity'
            ]);

        // Test valid stock management
        Livewire::test(ProductForm::class)
            ->set('name', 'Test Product')
            ->set('slug', 'test-product')
            ->set('sku', 'TEST-SKU-001')
            ->set('category_id', $this->productCategory->id)
            ->set('brand', 'Test Brand')
            ->set('price', 99.99)
            ->set('status', 'published')
            ->set('type', 'simple')
            ->set('manage_stock', true)
            ->set('stock_quantity', 10)
            ->set('low_stock_threshold', 5)
            ->call('save')
            ->assertHasNoErrors(['stock_quantity', 'low_stock_threshold']);
    }

    /** @test */
    public function product_form_validates_bulk_pricing()
    {
        $this->actingAs($this->adminUser);

        // Test bulk pricing with non-unique quantities
        Livewire::test(ProductForm::class)
            ->set('name', 'Test Product')
            ->set('slug', 'test-product')
            ->set('sku', 'TEST-SKU-001')
            ->set('category_id', $this->productCategory->id)
            ->set('brand', 'Test Brand')
            ->set('price', 99.99)
            ->set('status', 'published')
            ->set('type', 'simple')
            ->set('manage_stock', true)
            ->set('stock_quantity', 100)
            ->set('bulk_pricing_json', [
                ['quantity' => 5, 'price' => 90],
                ['quantity' => 5, 'price' => 85], // Duplicate quantity
                ['quantity' => 20, 'price' => 80]
            ])
            ->call('save')
            ->assertHasErrors([
                'bulk_pricing_json'
            ]);

        // Test bulk pricing with non-ascending quantities
        Livewire::test(ProductForm::class)
            ->set('name', 'Test Product')
            ->set('slug', 'test-product')
            ->set('sku', 'TEST-SKU-001')
            ->set('category_id', $this->productCategory->id)
            ->set('brand', 'Test Brand')
            ->set('price', 99.99)
            ->set('status', 'published')
            ->set('type', 'simple')
            ->set('manage_stock', true)
            ->set('stock_quantity', 100)
            ->set('bulk_pricing_json', [
                ['quantity' => 20, 'price' => 80], // Not in ascending order
                ['quantity' => 5, 'price' => 90],
                ['quantity' => 10, 'price' => 85]
            ])
            ->call('save')
            ->assertHasErrors([
                'bulk_pricing_json'
            ]);

        // Test valid bulk pricing
        Livewire::test(ProductForm::class)
            ->set('name', 'Test Product')
            ->set('slug', 'test-product')
            ->set('sku', 'TEST-SKU-001')
            ->set('category_id', $this->productCategory->id)
            ->set('brand', 'Test Brand')
            ->set('price', 99.99)
            ->set('status', 'published')
            ->set('type', 'simple')
            ->set('manage_stock', true)
            ->set('stock_quantity', 100)
            ->set('bulk_pricing_json', [
                ['quantity' => 5, 'price' => 450], // 90 per unit
                ['quantity' => 10, 'price' => 850], // 85 per unit
                ['quantity' => 20, 'price' => 1600] // 80 per unit
            ])
            ->call('save')
            ->assertHasNoErrors(['bulk_pricing_json']);
    }
}