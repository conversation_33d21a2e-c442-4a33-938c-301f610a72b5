<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Status Update</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #6366f1;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f9fafb;
            padding: 20px;
            border: 1px solid #e5e7eb;
        }
        .status-update {
            background-color: white;
            padding: 20px;
            border-radius: 6px;
            margin: 15px 0;
            text-align: center;
        }
        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 0.9em;
        }
        .status-pending { background-color: #fef3c7; color: #92400e; }
        .status-processing { background-color: #dbeafe; color: #1e40af; }
        .status-completed { background-color: #d1fae5; color: #065f46; }
        .status-cancelled { background-color: #fee2e2; color: #991b1b; }
        .status-refunded { background-color: #f3e8ff; color: #6b21a8; }
        .order-details {
            background-color: white;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .tracking-info {
            background-color: #ecfdf5;
            border-left: 4px solid #10b981;
            padding: 15px;
            margin: 15px 0;
        }
        .message-section {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 15px;
            margin: 15px 0;
        }
        .footer {
            background-color: #374151;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 0 0 8px 8px;
            font-size: 0.9em;
        }
        .button {
            display: inline-block;
            background-color: #6366f1;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 0;
        }
        .progress-bar {
            background-color: #e5e7eb;
            height: 8px;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background-color: #10b981;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Order Status Update</h1>
        <p>Your order <?php echo e($order->order_number); ?> has been updated</p>
    </div>

    <div class="content">
        <div class="status-update">
            <h2>Status Changed</h2>
            <p>
                <span class="status-badge status-<?php echo e($previousStatus); ?>"><?php echo e(ucfirst($previousStatus)); ?></span>
                →
                <span class="status-badge status-<?php echo e($currentStatus); ?>"><?php echo e(ucfirst($currentStatus)); ?></span>
            </p>
            
            <div class="progress-bar">
                <?php
                    $progressPercentage = match($currentStatus) {
                        'pending' => 25,
                        'processing' => 50,
                        'completed' => 100,
                        'cancelled' => 0,
                        'refunded' => 0,
                        default => 0,
                    };
                ?>
                <div class="progress-fill" style="width: <?php echo e($progressPercentage); ?>%"></div>
            </div>
            
            <p><strong>Updated:</strong> <?php echo e(now()->format('F j, Y \a\t g:i A')); ?></p>
        </div>

        <?php if($updateMessage): ?>
        <div class="message-section">
            <h3>Update Details</h3>
            <p><?php echo e($updateMessage); ?></p>
        </div>
        <?php endif; ?>

        <div class="order-details">
            <h3>Order Information</h3>
            <p><strong>Order Number:</strong> <?php echo e($order->order_number); ?></p>
            <p><strong>Order Date:</strong> <?php echo e($order->created_at->format('F j, Y')); ?></p>
            <p><strong>Total Amount:</strong> $<?php echo e(number_format($order->total, 2)); ?></p>
            <p><strong>Current Status:</strong> <?php echo e(ucfirst($currentStatus)); ?></p>
        </div>

        <?php if($trackingInfo): ?>
        <div class="tracking-info">
            <h3>📦 Tracking Information</h3>
            <p><strong>Tracking Number:</strong> <?php echo e($trackingInfo['number']); ?></p>
            <p><strong>Carrier:</strong> <?php echo e($trackingInfo['carrier']); ?></p>
            <?php if($trackingInfo['url']): ?>
                <p><a href="<?php echo e($trackingInfo['url']); ?>" style="color: #10b981;">Track Your Package</a></p>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <div style="text-align: center; margin: 20px 0;">
            <a href="<?php echo e(route('orders.show', $order)); ?>" class="button">View Order Details</a>
        </div>

        <div class="order-details">
            <h3>What happens next?</h3>
            <?php switch($currentStatus):
                case ('pending'): ?>
                    <p>We've received your order and are preparing it for processing. You'll receive another update once we begin processing your items.</p>
                    <?php break; ?>
                <?php case ('processing'): ?>
                    <p>Your order is being prepared for shipment. We're gathering your items and preparing them for dispatch.</p>
                    <?php break; ?>
                <?php case ('completed'): ?>
                    <p>Your order has been shipped! You should receive it within the estimated delivery timeframe. Check the tracking information above for updates.</p>
                    <?php break; ?>
                <?php case ('cancelled'): ?>
                    <p>Your order has been cancelled. If you have any questions, please contact our support team.</p>
                    <?php break; ?>
                <?php case ('refunded'): ?>
                    <p>Your refund has been processed. It may take 3-5 business days to appear in your account depending on your payment method.</p>
                    <?php break; ?>
            <?php endswitch; ?>
        </div>
    </div>

    <div class="footer">
        <p>Questions about your order? Contact <NAME_EMAIL></p>
        <p>&copy; <?php echo e(date('Y')); ?> PC Builder. All rights reserved.</p>
    </div>
</body>
</html>

<?php /**PATH C:\lara\www\pc-builder\resources\views/emails/order-status-update.blade.php ENDPATH**/ ?>