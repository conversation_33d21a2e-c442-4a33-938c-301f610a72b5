<?php if (isset($component)) { $__componentOriginal91fdd17964e43374ae18c674f95cdaa3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal91fdd17964e43374ae18c674f95cdaa3 = $attributes; } ?>
<?php $component = App\View\Components\AdminLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AdminLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container px-6 mx-auto grid">
        <div class="flex justify-between items-center">
            <h2 class="my-6 text-2xl font-semibold text-text-primary-light dark:text-text-primary-dark">
                Manage Comments
            </h2>
        </div>

        <!-- Filters -->
        <div class="mb-6 p-4 bg-bg-light dark:bg-bg-dark rounded-lg shadow-md border border-border-light dark:border-border-dark">
            <form action="<?php echo e(route('admin.comments.index')); ?>" method="GET" class="flex flex-wrap gap-4 items-end">
                <div class="w-full md:w-auto">
                    <label for="status" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">Status</label>
                    <select id="status" name="status" class="mt-1 block w-full pl-3 pr-10 py-2 text-base bg-bg-light dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark border border-border-light dark:border-border-dark focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark sm:text-sm rounded-md">
                        <option value="" <?php echo e(request('status') == '' ? 'selected' : ''); ?>>All Comments</option>
                        <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Pending</option>
                        <option value="approved" <?php echo e(request('status') == 'approved' ? 'selected' : ''); ?>>Approved</option>
                        <option value="rejected" <?php echo e(request('status') == 'rejected' ? 'selected' : ''); ?>>Rejected</option>
                    </select>
                </div>

                <div class="w-full md:w-auto flex-grow">
                    <label for="search" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">Search</label>
                    <input type="text" id="search" name="search" value="<?php echo e(request('search')); ?>" placeholder="Search comments..." class="mt-1 bg-bg-light dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark placeholder-text-secondary-light dark:placeholder-text-secondary-dark focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark block w-full shadow-sm sm:text-sm border border-border-light dark:border-border-dark rounded-md">
                </div>

                <div class="w-full md:w-auto">
                    <button type="submit" class="w-full inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-light dark:bg-primary-dark hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark transition-colors duration-200">
                        Filter
                    </button>
                </div>

                <?php if(request()->anyFilled(['status', 'search'])): ?>
                <div class="w-full md:w-auto">
                    <a href="<?php echo e(route('admin.comments.index')); ?>" class="w-full inline-flex justify-center py-2 px-4 border border-border-light dark:border-border-dark shadow-sm text-sm font-medium rounded-md text-text-primary-light dark:text-text-primary-dark bg-bg-light dark:bg-bg-dark hover:bg-gray-50 dark:hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark transition-colors duration-200">
                        Clear Filters
                    </a>
                </div>
                <?php endif; ?>
            </form>
        </div>

        <!-- Comments List -->
        <div class="w-full overflow-hidden rounded-lg shadow-lg border border-border-light dark:border-border-dark">
            <div class="w-full overflow-x-auto">
                <table class="w-full whitespace-no-wrap">
                    <thead>
                        <tr class="text-xs font-semibold tracking-wide text-left text-text-secondary-light dark:text-text-secondary-dark uppercase border-b border-border-light dark:border-border-dark bg-gray-50 dark:bg-slate-800">
                            <th class="px-4 py-3">Content</th>
                            <th class="px-4 py-3">Author</th>
                            <th class="px-4 py-3">Post</th>
                            <th class="px-4 py-3">Status</th>
                            <th class="px-4 py-3">Date</th>
                            <th class="px-4 py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-bg-light dark:bg-bg-dark divide-y divide-border-light dark:divide-border-dark">
                        <?php $__empty_1 = true; $__currentLoopData = $comments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="text-text-primary-light dark:text-text-primary-dark hover:bg-gray-50 dark:hover:bg-slate-800 transition-colors duration-200">
                            <td class="px-4 py-3">
                                <div class="flex items-center text-sm">
                                    <div>
                                        <p class="font-semibold"><?php echo e(Str::limit($comment->content, 50)); ?></p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-3 text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                <?php echo e($comment->author->name ?? 'Unknown'); ?>

                            </td>
                            <td class="px-4 py-3 text-sm">
                                <a href="<?php echo e(route('admin.blog.posts.edit', $comment->blogPost->id)); ?>" class="text-primary-light dark:text-primary-dark hover:text-opacity-80 transition-colors duration-200">
                                    <?php echo e(Str::limit($comment->blogPost->title, 30)); ?>

                                </a>
                            </td>
                            <td class="px-4 py-3 text-sm">
                                <?php if($comment->is_approved): ?>
                                    <span class="px-2 py-1 font-semibold leading-tight text-green-800 dark:text-green-200 bg-green-100 dark:bg-green-800 rounded-full">
                                        Approved
                                    </span>
                                <?php elseif($comment->rejected_reason): ?>
                                    <span class="px-2 py-1 font-semibold leading-tight text-red-800 dark:text-red-200 bg-red-100 dark:bg-red-800 rounded-full">
                                        Rejected
                                    </span>
                                <?php else: ?>
                                    <span class="px-2 py-1 font-semibold leading-tight text-orange-800 dark:text-orange-200 bg-orange-100 dark:bg-orange-800 rounded-full">
                                        Pending
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-4 py-3 text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                <?php echo e($comment->created_at->format('M d, Y')); ?>

                            </td>
                            <td class="px-4 py-3 text-sm">
                                <div class="flex items-center space-x-2">
                                    <a href="<?php echo e(route('admin.comments.show', $comment->id)); ?>" class="text-primary-light dark:text-primary-dark hover:text-opacity-80 transition-colors duration-200" title="View Comment">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                    </a>
                                    
                                    <?php if(!$comment->is_approved && !$comment->rejected_reason): ?>
                                    <form action="<?php echo e(route('admin.comments.approve', $comment->id)); ?>" method="POST" class="inline">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('PATCH'); ?>
                                        <button type="submit" class="text-green-600 dark:text-green-400 hover:text-opacity-80 transition-colors duration-200" title="Approve Comment">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                        </button>
                                    </form>
                                    <?php endif; ?>
                                    
                                    <form action="<?php echo e(route('admin.comments.destroy', $comment->id)); ?>" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this comment?');">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="text-red-600 dark:text-red-400 hover:text-opacity-80 transition-colors duration-200" title="Delete Comment">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="px-4 py-8 text-center text-text-secondary-light dark:text-text-secondary-dark">
                                <div class="flex flex-col items-center justify-center space-y-2">
                                    <svg class="w-12 h-12 text-text-secondary-light dark:text-text-secondary-dark opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                    </svg>
                                    <p>No comments found.</p>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="px-4 py-3 border-t border-border-light dark:border-border-dark bg-bg-light dark:bg-bg-dark">
                <?php echo e($comments->links()); ?>

            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal91fdd17964e43374ae18c674f95cdaa3)): ?>
<?php $attributes = $__attributesOriginal91fdd17964e43374ae18c674f95cdaa3; ?>
<?php unset($__attributesOriginal91fdd17964e43374ae18c674f95cdaa3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal91fdd17964e43374ae18c674f95cdaa3)): ?>
<?php $component = $__componentOriginal91fdd17964e43374ae18c674f95cdaa3; ?>
<?php unset($__componentOriginal91fdd17964e43374ae18c674f95cdaa3); ?>
<?php endif; ?><?php /**PATH C:\lara\www\pc-builder\resources\views/admin/comments/index.blade.php ENDPATH**/ ?>