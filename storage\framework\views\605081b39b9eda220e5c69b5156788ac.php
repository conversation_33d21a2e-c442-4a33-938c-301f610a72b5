<!-- Transaction Overview -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="text-sm font-medium text-gray-900 mb-3">Transaction Information</h4>
        <dl class="space-y-2">
            <div class="flex justify-between">
                <dt class="text-sm text-gray-600">Transaction ID:</dt>
                <dd class="text-sm font-medium text-gray-900"><?php echo e($transaction->transaction_id); ?></dd>
            </div>
            <?php if($transaction->gateway_transaction_id): ?>
                <div class="flex justify-between">
                    <dt class="text-sm text-gray-600">Gateway Transaction ID:</dt>
                    <dd class="text-sm font-medium text-gray-900"><?php echo e($transaction->gateway_transaction_id); ?></dd>
                </div>
            <?php endif; ?>
            <div class="flex justify-between">
                <dt class="text-sm text-gray-600">Amount:</dt>
                <dd class="text-sm font-medium text-gray-900"><?php echo e($transaction->currency); ?> <?php echo e(number_format($transaction->amount, 2)); ?></dd>
            </div>
            <div class="flex justify-between">
                <dt class="text-sm text-gray-600">Gateway:</dt>
                <dd class="text-sm font-medium text-gray-900 flex items-center">
                    <div class="w-6 h-6 bg-gray-100 rounded flex items-center justify-center mr-2">
                        <?php if($transaction->gateway_name === 'razorpay'): ?>
                            <img src="<?php echo e(asset('images/gateways/razorpay-logo.svg')); ?>" alt="Razorpay" class="w-4 h-4">
                        <?php elseif($transaction->gateway_name === 'payumoney'): ?>
                            <img src="<?php echo e(asset('images/gateways/payumoney-logo.svg')); ?>" alt="PayUmoney" class="w-4 h-4">
                        <?php elseif($transaction->gateway_name === 'cashfree'): ?>
                            <img src="<?php echo e(asset('images/gateways/cashfree-logo.svg')); ?>" alt="Cashfree" class="w-4 h-4">
                        <?php else: ?>
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                            </svg>
                        <?php endif; ?>
                    </div>
                    <?php echo e(ucfirst($transaction->gateway_name)); ?>

                </dd>
            </div>
            <div class="flex justify-between">
                <dt class="text-sm text-gray-600">Status:</dt>
                <dd class="text-sm font-medium">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        <?php switch($transaction->status):
                            case ('completed'): ?>
                                bg-green-100 text-green-800
                                <?php break; ?>
                            <?php case ('failed'): ?>
                                bg-red-100 text-red-800
                                <?php break; ?>
                            <?php case ('pending'): ?>
                                bg-yellow-100 text-yellow-800
                                <?php break; ?>
                            <?php case ('processing'): ?>
                                bg-blue-100 text-blue-800
                                <?php break; ?>
                            <?php case ('cancelled'): ?>
                                bg-gray-100 text-gray-800
                                <?php break; ?>
                            <?php default: ?>
                                bg-gray-100 text-gray-800
                        <?php endswitch; ?>
                        ">
                        <?php echo e(ucfirst($transaction->status)); ?>

                    </span>
                </dd>
            </div>
            <div class="flex justify-between">
                <dt class="text-sm text-gray-600">Webhook Verified:</dt>
                <dd class="text-sm font-medium">
                    <?php if($transaction->webhook_verified): ?>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Yes
                        </span>
                    <?php else: ?>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            No
                        </span>
                    <?php endif; ?>
                </dd>
            </div>
            <div class="flex justify-between">
                <dt class="text-sm text-gray-600">Created At:</dt>
                <dd class="text-sm font-medium text-gray-900"><?php echo e($transaction->created_at->format('Y-m-d H:i:s')); ?></dd>
            </div>
            <?php if($transaction->completed_at): ?>
            <div class="flex justify-between">
                <dt class="text-sm text-gray-600">Completed At:</dt>
                <dd class="text-sm font-medium text-gray-900"><?php echo e($transaction->completed_at->format('Y-m-d H:i:s')); ?></dd>
            </div>
            <?php endif; ?>
        </dl>
    </div>

    <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="text-sm font-medium text-gray-900 mb-3">User Information</h4>
        <dl class="space-y-2">
            <div class="flex justify-between">
                <dt class="text-sm text-gray-600">Name:</dt>
                <dd class="text-sm font-medium text-gray-900"><?php echo e($transaction->user->name); ?></dd>
            </div>
            <div class="flex justify-between">
                <dt class="text-sm text-gray-600">Email:</dt>
                <dd class="text-sm font-medium text-gray-900"><?php echo e($transaction->user->email); ?></dd>
            </div>
            <div class="flex justify-between">
                <dt class="text-sm text-gray-600">User ID:</dt>
                <dd class="text-sm font-medium text-gray-900"><?php echo e($transaction->user->id); ?></dd>
            </div>
        </dl>
    </div>
</div>

<!-- Transaction Timeline -->
<div class="mb-6">
    <h4 class="text-sm font-medium text-gray-900 mb-3">Transaction Timeline</h4>
    <div class="bg-gray-50 rounded-lg p-4">
        <div class="flow-root">
            <ul class="-mb-8">
                <?php if($transaction->completed_at): ?>
                <li class="relative pb-8">
                    <div class="relative flex space-x-3">
                        <div>
                            <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                <svg class="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </span>
                        </div>
                        <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                            <div>
                                <p class="text-sm text-gray-900">Transaction completed</p>
                            </div>
                            <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                <time><?php echo e($transaction->completed_at->format('M d, Y H:i:s')); ?></time>
                            </div>
                        </div>
                    </div>
                </li>
                <?php endif; ?>

                <?php if($transaction->status === 'failed' && $transaction->failure_reason): ?>
                <li class="relative pb-8">
                    <div class="relative flex space-x-3">
                        <div>
                            <span class="h-8 w-8 rounded-full bg-red-500 flex items-center justify-center ring-8 ring-white">
                                <svg class="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </span>
                        </div>
                        <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                            <div>
                                <p class="text-sm text-gray-900">Transaction failed: <?php echo e($transaction->failure_reason); ?></p>
                            </div>
                            <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                <time><?php echo e($transaction->updated_at->format('M d, Y H:i:s')); ?></time>
                            </div>
                        </div>
                    </div>
                </li>
                <?php endif; ?>

                <li class="relative pb-8">
                    <div class="relative flex space-x-3">
                        <div>
                            <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                <svg class="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                                </svg>
                            </span>
                        </div>
                        <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                            <div>
                                <p class="text-sm text-gray-900">Transaction created</p>
                            </div>
                            <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                <time><?php echo e($transaction->created_at->format('M d, Y H:i:s')); ?></time>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</div>

<!-- Payment Details -->
<?php if($transaction->payment_details): ?>
<div class="mb-6">
    <h4 class="text-sm font-medium text-gray-900 mb-3">Payment Details</h4>
    <div class="bg-gray-50 rounded-lg p-4 overflow-x-auto">
        <pre class="text-xs text-gray-800"><?php echo e(json_encode($transaction->payment_details, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES)); ?></pre>
    </div>
</div>
<?php endif; ?>

<!-- Actions -->
<div class="flex justify-end space-x-3">    
    <?php if(in_array($transaction->status, ['pending', 'processing'])): ?>
        <button type="button" onclick="refreshStatus()" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
            </svg>
            Refresh Status
        </button>
    <?php endif; ?>
    
    <button type="button" onclick="window.location.href='<?php echo e(route('admin.transactions.index')); ?>'" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
        Close
    </button>
</div><?php /**PATH C:\lara\www\pc-builder\resources\views/admin/transactions/_details.blade.php ENDPATH**/ ?>