<?php

namespace Tests\Feature\Api;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class ValidationApiTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected User $adminUser;
    protected ComponentCategory $cpuCategory;
    protected ProductCategory $productCategory;
    protected Component $component;
    protected Product $product;

    protected function setUp(): void
    {
        parent::setUp();

        // Create users
        $this->user = User::factory()->create();
        $this->adminUser = User::factory()->create([
            'role' => 'admin'
        ]);

        // Create categories
        $this->cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu'
        ]);
        $this->productCategory = ProductCategory::factory()->create();

        // Create a component
        $this->component = Component::factory()->create([
            'category_id' => $this->cpuCategory->id,
            'socket_type' => 'LGA1700',
            'power_consumption' => 65
        ]);

        // Create a product
        $this->product = Product::factory()->create([
            'category_id' => $this->productCategory->id
        ]);
    }

    /** @test */
    public function api_validates_component_creation_request()
    {
        Sanctum::actingAs($this->adminUser, ['*']);

        // Test with missing required fields
        $response = $this->postJson('/api/v1/admin/components', []);
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'name',
            'slug',
            'category_id',
            'brand',
            'model',
            'price',
            'stock'
        ]);

        // Test with invalid data
        $response = $this->postJson('/api/v1/admin/components', [
            'name' => 'a', // too short
            'slug' => 'invalid slug!',
            'category_id' => 9999, // non-existent
            'brand' => '',
            'model' => '',
            'price' => -50,
            'stock' => -10
        ]);
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'name',
            'slug',
            'category_id',
            'brand',
            'model',
            'price',
            'stock'
        ]);

        // Test category-specific validation (CPU requires socket_type and power_consumption)
        $response = $this->postJson('/api/v1/admin/components', [
            'name' => 'Test CPU',
            'slug' => 'test-cpu',
            'category_id' => $this->cpuCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 299.99,
            'stock' => 10,
            // Missing socket_type and power_consumption
        ]);
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'socket_type',
            'power_consumption'
        ]);
        
        // Test dimensions validation
        $response = $this->postJson('/api/v1/admin/components', [
            'name' => 'Test Component With Dimensions',
            'slug' => 'test-component-dimensions',
            'category_id' => $this->cpuCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 299.99,
            'stock' => 10,
            'socket_type' => 'LGA1700',
            'power_consumption' => 65,
            'dimensions_json' => [
                'length' => 100,
                'width' => 50
                // Missing height
            ]
        ]);
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'dimensions_json'
        ]);
        
        // Test discontinued component validation
        $response = $this->postJson('/api/v1/admin/components', [
            'name' => 'Discontinued Component',
            'slug' => 'discontinued-component',
            'category_id' => $this->cpuCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 299.99,
            'stock' => 10,
            'socket_type' => 'LGA1700',
            'power_consumption' => 65,
            'is_active' => true,
            'discontinued_at' => now()->subDay()->format('Y-m-d')
        ]);
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'is_active'
        ]);
    }

    /** @test */
    public function api_validates_component_update_request()
    {
        Sanctum::actingAs($this->adminUser, ['*']);

        // Test with invalid data
        $response = $this->putJson("/api/v1/admin/components/{$this->component->id}", [
            'name' => '', // empty
            'price' => -10, // negative
            'stock' => -5, // negative
        ]);
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'name',
            'price',
            'stock'
        ]);

        // Test with valid data
        $response = $this->putJson("/api/v1/admin/components/{$this->component->id}", [
            'name' => 'Updated Component',
            'price' => 349.99,
            'stock' => 15,
            'socket_type' => 'LGA1700', // Required for CPU
            'power_consumption' => 95 // Required for CPU
        ]);
        $response->assertStatus(200);
    }

    /** @test */
    public function api_validates_product_creation_request()
    {
        Sanctum::actingAs($this->adminUser, ['*']);

        // Test with missing required fields
        $response = $this->postJson('/api/v1/admin/products', []);
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'name',
            'slug',
            'sku',
            'category_id',
            'brand',
            'price',
            'status',
            'type'
        ]);

        // Test with invalid data
        $response = $this->postJson('/api/v1/admin/products', [
            'name' => 'a', // too short
            'slug' => 'invalid slug!',
            'sku' => '',
            'category_id' => 9999, // non-existent
            'brand' => '',
            'price' => -50,
            'status' => 'invalid-status',
            'type' => 'invalid-type'
        ]);
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'name',
            'slug',
            'sku',
            'category_id',
            'brand',
            'price',
            'status',
            'type'
        ]);

        // Test specific validation rules
        $response = $this->postJson('/api/v1/admin/products', [
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-SKU-001',
            'category_id' => $this->productCategory->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'sale_price' => 109.99, // Higher than regular price
            'status' => 'published',
            'type' => 'simple',
            'manage_stock' => true,
            // Missing stock_quantity which is required when manage_stock is true
        ]);
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'sale_price',
            'stock_quantity'
        ]);
        
        // Test bulk pricing validation
        $response = $this->postJson('/api/v1/admin/products', [
            'name' => 'Bulk Test Product',
            'slug' => 'bulk-test-product',
            'sku' => 'BULK-TEST-001',
            'category_id' => $this->productCategory->id,
            'brand' => 'Test Brand',
            'price' => 99.99,
            'status' => 'published',
            'type' => 'simple',
            'bulk_pricing' => [
                ['quantity' => 0, 'price' => 90], // Invalid quantity
                ['quantity' => 10, 'price' => -5], // Invalid price
                ['quantity' => 'invalid', 'price' => 85] // Non-numeric quantity
            ]
        ]);
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'bulk_pricing.0.quantity',
            'bulk_pricing.1.price',
            'bulk_pricing.2.quantity'
        ]);
        
        // Test compatibility requirements validation
        $response = $this->postJson('/api/v1/admin/products', [
            'name' => 'Compatible Test Product',
            'slug' => 'compatible-test-product',
            'sku' => 'COMPAT-TEST-001',
            'category_id' => $this->productCategory->id,
            'brand' => 'Test Brand',
            'price' => 199.99,
            'status' => 'published',
            'type' => 'simple',
            'compatibility_requirements' => [
                'socket_type' => '', // Empty value
                'power_required' => -50 // Negative value
            ]
        ]);
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'compatibility_requirements.socket_type',
            'compatibility_requirements.power_required'
        ]);
    }

    /** @test */
    public function api_validates_product_update_request()
    {
        Sanctum::actingAs($this->adminUser, ['*']);

        // Test with invalid data
        $response = $this->putJson("/api/v1/admin/products/{$this->product->id}", [
            'name' => '', // empty
            'price' => -10, // negative
            'sale_price' => 200, // higher than original price
            'status' => 'invalid-status'
        ]);
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'name',
            'price',
            'sale_price',
            'status'
        ]);

        // Test with valid data
        $response = $this->putJson("/api/v1/admin/products/{$this->product->id}", [
            'name' => 'Updated Product',
            'price' => 129.99,
            'sale_price' => 119.99,
            'status' => 'published'
        ]);
        $response->assertStatus(200);
    }

    /** @test */
    public function api_validates_cart_operations()
    {
        Sanctum::actingAs($this->user, ['*']);

        // Test adding to cart with missing component_id
        $response = $this->postJson('/api/v1/cart', [
            'quantity' => 1
        ]);
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'component_id'
        ]);

        // Test adding to cart with invalid component_id
        $response = $this->postJson('/api/v1/cart', [
            'component_id' => 9999, // non-existent
            'quantity' => 1
        ]);
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'component_id'
        ]);

        // Test adding to cart with invalid quantity
        $response = $this->postJson('/api/v1/cart', [
            'component_id' => $this->component->id,
            'quantity' => 0 // must be at least 1
        ]);
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'quantity'
        ]);

        // Test adding to cart with valid data
        $response = $this->postJson('/api/v1/cart', [
            'component_id' => $this->component->id,
            'quantity' => 1
        ]);
        $response->assertStatus(201);
    }

    /** @test */
    public function api_validates_component_filtering_parameters()
    {
        // Test with invalid category
        $response = $this->getJson('/api/v1/components?category=invalid-category');
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'category'
        ]);

        // Test with invalid min_price
        $response = $this->getJson('/api/v1/components?min_price=-100');
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'min_price'
        ]);

        // Test with invalid max_price
        $response = $this->getJson('/api/v1/components?max_price=-50');
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'max_price'
        ]);

        // Test with invalid per_page
        $response = $this->getJson('/api/v1/components?per_page=0');
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'per_page'
        ]);

        // Test with valid parameters
        $response = $this->getJson('/api/v1/components?category=cpu&min_price=100&max_price=500&per_page=10');
        $response->assertStatus(200);
    }

    /** @test */
    public function api_validates_product_filtering_parameters()
    {
        // Test with invalid category
        $response = $this->getJson('/api/v1/products?category=invalid-category');
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'category'
        ]);

        // Test with invalid price range
        $response = $this->getJson('/api/v1/products?min_price=500&max_price=100');
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'max_price'
        ]);

        // Test with invalid sort parameter
        $response = $this->getJson('/api/v1/products?sort=invalid-sort');
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'sort'
        ]);

        // Test with valid parameters
        $response = $this->getJson('/api/v1/products?category=electronics&min_price=10&max_price=1000&sort=price_asc');
        $response->assertStatus(200);
    }
}