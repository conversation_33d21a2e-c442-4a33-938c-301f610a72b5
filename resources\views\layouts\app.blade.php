<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <script>
        (function() {
            try {
                var theme = localStorage.getItem('theme');
                if (theme === 'dark' || (!theme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                    document.documentElement.classList.add('dark');
                    document.documentElement.setAttribute('data-theme', 'dark');
                } else {
                    document.documentElement.classList.remove('dark');
                    document.documentElement.setAttribute('data-theme', 'light');
                }
            } catch (e) {}
        })();
    </script>

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Styles -->
    @livewireStyles
    @stack('styles')

    <!-- GSAP Animation Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
</head>

<body class="font-sans antialiased">
    <x-banner />

    <div class="min-h-screen bg-gray-100 dark:bg-gray-900">

    {{--  <livewire:navigation-menu /> --}}

         @include('navigation-menu')    

        <!-- Page Heading -->
        @if (isset($header))
        <header class="bg-white dark:bg-gray-800 shadow">
            <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                {{ $header }}
            </div>
        </header>
        @endif

        <!-- Page Content -->
        <main>
            @hasSection('content')
            @yield('content')
            @else
            {{ $slot }}
            @endif
        </main>

        <!-- Footer -->
        <x-footer />
    </div>

    @stack('modals')
    @stack('scripts')
    @livewireScripts

    <script>
        // Prevent multiple Alpine instances
        if (window.Alpine && window.Alpine.version) {
            console.warn('Alpine.js already initialized, skipping duplicate initialization');
        }

        // Theme toggle functionality
        function setThemeIcon(isDark) {
            const icon = isDark ? '☀️' : '🌙';
            const navIcon = document.getElementById('theme-switcher-icon');
            const mobileIcon = document.getElementById('theme-switcher-icon-mobile');
            if (navIcon) navIcon.textContent = icon;
            if (mobileIcon) mobileIcon.textContent = icon;
        }

        function toggleTheme() {
            const html = document.documentElement;
            const isDark = html.classList.contains('dark');
            if (isDark) {
                html.classList.remove('dark');
                html.setAttribute('data-theme', 'light');
                localStorage.setItem('theme', 'light');
            } else {
                html.classList.add('dark');
                html.setAttribute('data-theme', 'dark');
                localStorage.setItem('theme', 'dark');
            }
            setThemeIcon(!isDark);
        }

        document.addEventListener('DOMContentLoaded', function() {
            setThemeIcon(document.documentElement.classList.contains('dark'));
        });
    </script>
</body>

</html>