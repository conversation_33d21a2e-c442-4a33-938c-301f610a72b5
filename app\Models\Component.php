<?php

namespace App\Models;

use App\Contracts\Purchasable;
use App\Contracts\Cartable;
use App\Traits\ImageManagement;
use App\Traits\Cartable as CartableTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Component extends Model implements Purchasable, Cartable
{
    use HasFactory, ImageManagement, CartableTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
        'category_id',
        'brand',
        'model',
        'price',
        'stock',
        'image',
        'specs',
        'is_featured',
        'is_active',
        'socket_type',
        'chipset',
        'form_factor',
        'power_consumption',
        'cooling_type',
        'memory_type',
        'interface_type',
        'warranty_months',
        'manufacturer_part_number',
        'ean_code',
        'weight_grams',
        'dimensions_json',
        'release_date',
        'discontinued_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'decimal:2',
        'stock' => 'integer',
        'specs' => 'array',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'power_consumption' => 'integer',
        'warranty_months' => 'integer',
        'weight_grams' => 'integer',
        'dimensions_json' => 'array',
        'release_date' => 'date',
        'discontinued_at' => 'datetime',
    ];

    /**
     * Get the category that owns the component.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ComponentCategory::class, 'category_id');
    }

    /**
     * Get the build components for the component.
     */
    public function buildComponents(): HasMany
    {
        return $this->hasMany(BuildComponent::class);
    }

    /**
     * Get the compatibility rules for the component.
     */
    public function compatibilityRules(): HasMany
    {
        return $this->hasMany(ComponentCompatibility::class);
    }

    /**
     * Get the cart items for the component.
     */
    public function cartItems(): HasMany
    {
        return $this->hasMany(CartItem::class);
    }

    /**
     * Get the order items for the component.
     */
    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get the reviews for the component.
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Get the price history for the component.
     */
    public function priceHistory(): HasMany
    {
        return $this->hasMany(PriceHistory::class);
    }

    /**
     * Scope a query to only include active components.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include featured components.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to only include components in stock.
     */
    public function scopeInStock($query)
    {
        return $query->where('stock', '>', 0);
    }

    /**
     * Check if this component is compatible with another component.
     */
    public function isCompatibleWith(Component $otherComponent): bool
    {
        $compatibilityService = app(\App\Services\CompatibilityService::class);
        
        // Use reflection to access the protected method for direct component pair checking
        $reflection = new \ReflectionClass($compatibilityService);
        $method = $reflection->getMethod('checkComponentPair');
        $method->setAccessible(true);
        
        $result = $method->invoke($compatibilityService, $this, $otherComponent);
        
        return $result['compatible'];
    }

    /**
     * Get compatible components for a specific category.
     */
    public function getCompatibleComponents(string $categorySlug): \Illuminate\Support\Collection
    {
        $compatibilityService = app(\App\Services\CompatibilityService::class);
        
        return $compatibilityService->getCompatibleComponents($this, $categorySlug);
    }

    /**
     * Get all compatibility issues with another component.
     */
    public function getCompatibilityIssues(Component $otherComponent): array
    {
        $compatibilityService = app(\App\Services\CompatibilityService::class);
        $result = $compatibilityService->checkCompatibility([$this, $otherComponent]);
        
        return $result->getIssues();
    }

    /**
     * Get power consumption value as integer.
     */
    public function getPowerConsumption(): int
    {
        // Use the new dedicated field first, fall back to specs
        if ($this->power_consumption !== null) {
            return $this->power_consumption;
        }
        
        $power = $this->specs['power_consumption'] ?? null;
        
        if (!$power) {
            return 0;
        }
        
        return (int) filter_var($power, FILTER_SANITIZE_NUMBER_INT);
    }

    /**
     * Check if component has specific specification.
     */
    public function hasSpec(string $key): bool
    {
        return isset($this->specs[$key]) && !empty($this->specs[$key]);
    }

    /**
     * Get specification value.
     */
    public function getSpec(string $key, $default = null)
    {
        return $this->specs[$key] ?? $default;
    }

    /**
     * Get approved reviews for the component.
     */
    public function approvedReviews(): HasMany
    {
        return $this->reviews()->where('is_approved', true);
    }

    /**
     * Get average rating for the component.
     */
    public function getAverageRatingAttribute(): float
    {
        return Review::getAverageRating($this->id);
    }

    /**
     * Get total review count for the component.
     */
    public function getReviewCountAttribute(): int
    {
        return Review::getReviewCount($this->id);
    }

    /**
     * Get rating distribution for the component.
     */
    public function getRatingDistributionAttribute(): array
    {
        return Review::getRatingDistribution($this->id);
    }

    /**
     * Get comprehensive review statistics.
     */
    public function getReviewStatsAttribute(): array
    {
        return Review::getComponentReviewStats($this->id);
    }

    /**
     * Check if user can review this component.
     */
    public function canBeReviewedBy(User $user): bool
    {
        // Check if user already reviewed this component
        $existingReview = $this->reviews()
            ->where('user_id', $user->id)
            ->exists();

        if ($existingReview) {
            return false;
        }

        // Check if user has purchased this component
        return \Illuminate\Support\Facades\DB::table('order_items')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->where('orders.user_id', $user->id)
            ->where('order_items.component_id', $this->id)
            ->where('orders.status', 'completed')
            ->exists();
    }

    // Component-Specific Functionality

    /**
     * Get the form factor of the component
     */
    public function getFormFactor(): ?string
    {
        return $this->form_factor;
    }

    /**
     * Get the socket type of the component
     */
    public function getSocketType(): ?string
    {
        return $this->socket_type;
    }

    /**
     * Get the chipset of the component
     */
    public function getChipset(): ?string
    {
        return $this->chipset;
    }

    /**
     * Get the cooling type of the component
     */
    public function getCoolingType(): ?string
    {
        return $this->cooling_type;
    }

    /**
     * Get the memory type of the component
     */
    public function getMemoryType(): ?string
    {
        return $this->memory_type;
    }

    /**
     * Get the interface type of the component
     */
    public function getInterfaceType(): ?string
    {
        return $this->interface_type;
    }

    /**
     * Get the warranty period in months
     */
    public function getWarrantyMonths(): int
    {
        return $this->warranty_months ?? 12;
    }

    /**
     * Get the manufacturer part number
     */
    public function getManufacturerPartNumber(): ?string
    {
        return $this->manufacturer_part_number;
    }

    /**
     * Get the EAN code
     */
    public function getEanCode(): ?string
    {
        return $this->ean_code;
    }

    /**
     * Get the weight in grams
     */
    public function getWeightGrams(): ?int
    {
        return $this->weight_grams;
    }

    /**
     * Get the dimensions as array
     */
    public function getDimensions(): ?array
    {
        return $this->dimensions_json;
    }

    /**
     * Get the release date
     */
    public function getReleaseDate(): ?\Carbon\Carbon
    {
        return $this->release_date;
    }

    /**
     * Check if the component is discontinued
     */
    public function isDiscontinued(): bool
    {
        return $this->discontinued_at !== null;
    }

    /**
     * Get the discontinued date
     */
    public function getDiscontinuedAt(): ?\Carbon\Carbon
    {
        return $this->discontinued_at;
    }

    /**
     * Get technical specifications as a formatted array
     */
    public function getTechnicalSpecs(): array
    {
        $specs = [];
        
        if ($this->socket_type) {
            $specs['Socket Type'] = $this->socket_type;
        }
        
        if ($this->chipset) {
            $specs['Chipset'] = $this->chipset;
        }
        
        if ($this->form_factor) {
            $specs['Form Factor'] = $this->form_factor;
        }
        
        if ($this->power_consumption) {
            $specs['Power Consumption'] = $this->power_consumption . 'W';
        }
        
        if ($this->cooling_type) {
            $specs['Cooling Type'] = $this->cooling_type;
        }
        
        if ($this->memory_type) {
            $specs['Memory Type'] = $this->memory_type;
        }
        
        if ($this->interface_type) {
            $specs['Interface Type'] = $this->interface_type;
        }
        
        // Merge with existing specs array
        return array_merge($specs, $this->specs ?? []);
    }

    /**
     * Enhanced compatibility checking with specific component
     */
    public function checkCompatibilityWith(Component $otherComponent): array
    {
        $issues = [];
        $compatible = true;
        
        // Socket compatibility for CPU/Motherboard
        if ($this->category?->slug === 'cpu' && $otherComponent->category?->slug === 'motherboard') {
            if ($this->socket_type && $otherComponent->socket_type && $this->socket_type !== $otherComponent->socket_type) {
                $issues[] = "CPU socket ({$this->socket_type}) is not compatible with motherboard socket ({$otherComponent->socket_type})";
                $compatible = false;
            }
        }
        
        // Memory type compatibility
        if ($this->category?->slug === 'ram' && $otherComponent->category?->slug === 'motherboard') {
            if ($this->memory_type && $otherComponent->memory_type && $this->memory_type !== $otherComponent->memory_type) {
                $issues[] = "RAM type ({$this->memory_type}) is not compatible with motherboard memory type ({$otherComponent->memory_type})";
                $compatible = false;
            }
        }
        
        // Form factor compatibility for motherboard/case
        if ($this->category?->slug === 'motherboard' && $otherComponent->category?->slug === 'case') {
            if ($this->form_factor && $otherComponent->form_factor) {
                // Simple form factor compatibility check (this could be more sophisticated)
                $compatibleFormFactors = [
                    'ATX' => ['ATX', 'Mid Tower', 'Full Tower'],
                    'Micro-ATX' => ['ATX', 'Micro-ATX', 'Mid Tower', 'Full Tower'],
                    'Mini-ITX' => ['ATX', 'Micro-ATX', 'Mini-ITX', 'Mid Tower', 'Full Tower']
                ];
                
                if (isset($compatibleFormFactors[$this->form_factor]) && 
                    !in_array($otherComponent->form_factor, $compatibleFormFactors[$this->form_factor])) {
                    $issues[] = "Motherboard form factor ({$this->form_factor}) may not fit in case ({$otherComponent->form_factor})";
                    $compatible = false;
                }
            }
        }
        
        return [
            'compatible' => $compatible,
            'issues' => $issues
        ];
    }

    /**
     * Get components that are compatible with this component
     */
    public function getCompatibleComponentsForCategory(string $categorySlug): \Illuminate\Database\Eloquent\Collection
    {
        $query = Component::where('category_id', function($q) use ($categorySlug) {
            $q->select('id')
              ->from('component_categories')
              ->where('slug', $categorySlug)
              ->limit(1);
        })->where('is_active', true);
        
        // Add specific compatibility filters based on this component's specs
        switch ($categorySlug) {
            case 'motherboard':
                if ($this->category?->slug === 'cpu' && $this->socket_type) {
                    $query->where('socket_type', $this->socket_type);
                }
                break;
                
            case 'ram':
                if ($this->category?->slug === 'motherboard' && $this->memory_type) {
                    $query->where('memory_type', $this->memory_type);
                }
                break;
                
            case 'case':
                if ($this->category?->slug === 'motherboard' && $this->form_factor) {
                    // This would need more sophisticated logic for form factor compatibility
                    $query->whereIn('form_factor', ['ATX', 'Mid Tower', 'Full Tower']);
                }
                break;
        }
        
        return $query->get();
    }

    // Purchasable Interface Implementation

    /**
     * Get the name of the component
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * Get the slug for URL generation
     */
    public function getSlug(): string
    {
        return $this->slug;
    }

    /**
     * Get the base price of the component
     */
    public function getPrice(): float
    {
        return (float) $this->price;
    }

    /**
     * Get the effective price (components don't have sale prices)
     */
    public function getEffectivePrice(): float
    {
        return $this->getPrice();
    }

    /**
     * Get the current stock quantity
     */
    public function getStock(): int
    {
        return $this->stock ?? 0;
    }

    /**
     * Check if the component is available for purchase
     */
    public function isAvailable(int $quantity = 1): bool
    {
        return $this->is_active && 
               !$this->isDiscontinued() &&
               $this->getStock() >= $quantity;
    }

    /**
     * Get the description of the component
     */
    public function getDescription(): ?string
    {
        return $this->description;
    }

    /**
     * Get the brand of the component
     */
    public function getBrand(): ?string
    {
        return $this->brand;
    }

    /**
     * Get the model of the component
     */
    public function getModel(): ?string
    {
        return $this->model;
    }

    /**
     * Get the category of the component
     */
    public function getCategory(): ?string
    {
        return $this->category?->name;
    }

    /**
     * Get the URL for the component
     */
    public function getUrl(): string
    {
        return route('shop.product', $this->slug);
    }
}