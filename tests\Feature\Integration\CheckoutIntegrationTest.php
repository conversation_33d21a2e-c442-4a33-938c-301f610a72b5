<?php

namespace Tests\Feature\Integration;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\User;
use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Address;
use App\Services\PaymentGatewayService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Illuminate\Support\Facades\Event;
use Mockery;

class CheckoutIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Component $component;
    protected ComponentCategory $cpuCategory;
    protected Cart $cart;
    protected CartItem $cartItem;
    protected Address $address;

    protected function setUp(): void
    {
        parent::setUp();

        // Create user
        $this->user = User::factory()->create();

        // Create component category
        $this->cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU', 
            'slug' => 'cpu'
        ]);

        // Create component
        $this->component = Component::factory()->create([
            'name' => 'Test CPU',
            'slug' => 'test-cpu',
            'category_id' => $this->cpuCategory->id,
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'price' => 299.99,
            'stock' => 10,
            'socket_type' => 'LGA1700',
            'power_consumption' => 65,
            'is_active' => true
        ]);

        // Create cart and add item
        $this->cart = Cart::factory()->create([
            'user_id' => $this->user->id
        ]);

        $this->cartItem = CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'component_id' => $this->component->id,
            'quantity' => 2
        ]);

        // Create address
        $this->address = Address::factory()->create([
            'user_id' => $this->user->id,
            'address_line1' => '123 Test Street',
            'city' => 'Test City',
            'state' => 'Test State',
            'postal_code' => '12345',
            'country' => 'Test Country',
            'is_default' => true
        ]);
    }

    /** @test */
    public function user_can_view_checkout_page_with_cart_items()
    {
        $this->actingAs($this->user);

        $response = $this->get('/checkout');

        $response->assertStatus(200);
        $response->assertSee('Test CPU');
        $response->assertSee('299.99');
        $response->assertSee('2'); // Quantity
    }

    /** @test */
    public function user_cannot_checkout_with_empty_cart()
    {
        // Create a new user with empty cart
        $userWithEmptyCart = User::factory()->create();
        $this->actingAs($userWithEmptyCart);

        $response = $this->get('/checkout');

        $response->assertRedirect('/cart');
        $response->assertSessionHas('error', 'Your cart is empty');
    }

    /** @test */
    public function user_can_select_shipping_address_during_checkout()
    {
        $this->actingAs($this->user);

        // Create another address
        $secondAddress = Address::factory()->create([
            'user_id' => $this->user->id,
            'address_line1' => '456 Another Street',
            'city' => 'Another City',
            'state' => 'Another State',
            'postal_code' => '67890',
            'country' => 'Another Country',
            'is_default' => false
        ]);

        $response = $this->get('/checkout');

        $response->assertStatus(200);
        $response->assertSee('123 Test Street'); // Default address
        $response->assertSee('456 Another Street'); // Second address
    }

    /** @test */
    public function user_can_add_new_address_during_checkout()
    {
        $this->actingAs($this->user);

        $response = $this->post('/addresses', [
            'address_line1' => '789 New Street',
            'city' => 'New City',
            'state' => 'New State',
            'postal_code' => '54321',
            'country' => 'New Country',
            'is_default' => false
        ]);

        $response->assertStatus(302); // Redirect after successful creation
        $response->assertSessionHasNoErrors();

        $this->assertDatabaseHas('addresses', [
            'user_id' => $this->user->id,
            'address_line1' => '789 New Street',
            'city' => 'New City'
        ]);
    }

    /** @test */
    public function user_can_see_available_payment_methods()
    {
        $this->actingAs($this->user);

        // Mock gateway settings in the database
        $this->mockGatewaySettings();

        $response = $this->get('/checkout');

        $response->assertStatus(200);
        $response->assertSee('Razorpay');
        $response->assertSee('PayUmoney');
        $response->assertSee('Cashfree');
    }

    /** @test */
    public function user_can_place_order_with_valid_data()
    {
        $this->actingAs($this->user);

        // Mock the payment gateway service
        $mockPaymentService = Mockery::mock(PaymentGatewayService::class);
        $mockPaymentService->shouldReceive('initiatePayment')
            ->once()
            ->andReturn([
                'success' => true,
                'payment_id' => 'test_payment_123',
                'redirect_url' => 'https://test-gateway.com/pay'
            ]);
        $this->app->instance(PaymentGatewayService::class, $mockPaymentService);

        $response = $this->post('/checkout/place-order', [
            'address_id' => $this->address->id,
            'payment_method' => 'razorpay',
            'notes' => 'Test order notes'
        ]);

        $response->assertStatus(302); // Redirect to payment gateway
        $response->assertRedirect('https://test-gateway.com/pay');

        // Check if order was created
        $this->assertDatabaseHas('orders', [
            'user_id' => $this->user->id,
            'status' => 'pending',
            'payment_method' => 'razorpay',
            'notes' => 'Test order notes'
        ]);

        // Check if order items were created
        $order = Order::where('user_id', $this->user->id)->first();
        $this->assertDatabaseHas('order_items', [
            'order_id' => $order->id,
            'component_id' => $this->component->id,
            'quantity' => 2,
            'price' => 299.99
        ]);

        // Check if cart was cleared
        $this->assertDatabaseMissing('cart_items', [
            'cart_id' => $this->cart->id
        ]);
    }

    /** @test */
    public function order_fails_with_invalid_payment_method()
    {
        $this->actingAs($this->user);

        $response = $this->post('/checkout/place-order', [
            'address_id' => $this->address->id,
            'payment_method' => 'invalid_method',
            'notes' => 'Test order notes'
        ]);

        $response->assertStatus(422);
        $response->assertSessionHasErrors('payment_method');

        // Check that no order was created
        $this->assertDatabaseMissing('orders', [
            'user_id' => $this->user->id,
            'payment_method' => 'invalid_method'
        ]);
    }

    /** @test */
    public function order_fails_with_invalid_address()
    {
        $this->actingAs($this->user);

        $response = $this->post('/checkout/place-order', [
            'address_id' => 9999, // Non-existent address
            'payment_method' => 'razorpay',
            'notes' => 'Test order notes'
        ]);

        $response->assertStatus(422);
        $response->assertSessionHasErrors('address_id');

        // Check that no order was created
        $this->assertDatabaseMissing('orders', [
            'user_id' => $this->user->id,
            'payment_method' => 'razorpay'
        ]);
    }

    /** @test */
    public function payment_callback_processes_successful_payment()
    {
        $this->actingAs($this->user);

        // Create an order first
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'pending',
            'payment_method' => 'razorpay',
            'payment_id' => 'test_payment_123',
            'total' => 599.98 // 2 * 299.99
        ]);

        // Create order item
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'component_id' => $this->component->id,
            'quantity' => 2,
            'price' => 299.99
        ]);

        // Mock the payment gateway service
        $mockPaymentService = Mockery::mock(PaymentGatewayService::class);
        $mockPaymentService->shouldReceive('verifyPayment')
            ->once()
            ->andReturn([
                'success' => true,
                'payment_id' => 'test_payment_123',
                'transaction_id' => 'txn_123456'
            ]);
        $this->app->instance(PaymentGatewayService::class, $mockPaymentService);

        // Simulate payment callback
        $response = $this->get('/payment/callback/razorpay?order_id=' . $order->id . '&payment_id=test_payment_123');

        $response->assertStatus(302); // Redirect to order confirmation
        $response->assertRedirect('/orders/' . $order->id . '/confirmation');

        // Check if order status was updated
        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'status' => 'processing',
            'transaction_id' => 'txn_123456'
        ]);

        // Check if component stock was reduced
        $this->assertDatabaseHas('components', [
            'id' => $this->component->id,
            'stock' => 8 // Original 10 - 2 purchased
        ]);
    }

    /** @test */
    public function payment_callback_handles_failed_payment()
    {
        $this->actingAs($this->user);

        // Create an order first
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'pending',
            'payment_method' => 'razorpay',
            'payment_id' => 'test_payment_123',
            'total' => 599.98 // 2 * 299.99
        ]);

        // Create order item
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'component_id' => $this->component->id,
            'quantity' => 2,
            'price' => 299.99
        ]);

        // Mock the payment gateway service
        $mockPaymentService = Mockery::mock(PaymentGatewayService::class);
        $mockPaymentService->shouldReceive('verifyPayment')
            ->once()
            ->andReturn([
                'success' => false,
                'error' => 'Payment failed'
            ]);
        $this->app->instance(PaymentGatewayService::class, $mockPaymentService);

        // Simulate payment callback
        $response = $this->get('/payment/callback/razorpay?order_id=' . $order->id . '&payment_id=test_payment_123');

        $response->assertStatus(302); // Redirect to checkout
        $response->assertRedirect('/checkout');
        $response->assertSessionHas('error', 'Payment failed');

        // Check if order status was updated to failed
        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'status' => 'failed'
        ]);

        // Check that component stock was not reduced
        $this->assertDatabaseHas('components', [
            'id' => $this->component->id,
            'stock' => 10 // Original stock unchanged
        ]);
    }

    /** @test */
    public function user_can_view_order_confirmation_page()
    {
        $this->actingAs($this->user);

        // Create a completed order
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'processing',
            'payment_method' => 'razorpay',
            'payment_id' => 'test_payment_123',
            'transaction_id' => 'txn_123456',
            'total' => 599.98 // 2 * 299.99
        ]);

        // Create order item
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'component_id' => $this->component->id,
            'quantity' => 2,
            'price' => 299.99
        ]);

        $response = $this->get('/orders/' . $order->id . '/confirmation');

        $response->assertStatus(200);
        $response->assertSee('Order #' . $order->id);
        $response->assertSee('Test CPU');
        $response->assertSee('599.98'); // Total
        $response->assertSee('txn_123456'); // Transaction ID
    }

    /**
     * Mock gateway settings in the database
     */
    protected function mockGatewaySettings()
    {
        // Create gateway settings
        \DB::table('gateway_settings')->insert([
            [
                'gateway' => 'razorpay',
                'is_enabled' => true,
                'settings' => json_encode([
                    'key_id' => 'test_key',
                    'key_secret' => 'test_secret'
                ]),
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'gateway' => 'payumoney',
                'is_enabled' => true,
                'settings' => json_encode([
                    'merchant_key' => 'test_key',
                    'merchant_salt' => 'test_salt'
                ]),
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'gateway' => 'cashfree',
                'is_enabled' => true,
                'settings' => json_encode([
                    'app_id' => 'test_app_id',
                    'secret_key' => 'test_secret'
                ]),
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'gateway' => 'disabled_gateway',
                'is_enabled' => false,
                'settings' => json_encode([
                    'key' => 'test_key'
                ]),
                'created_at' => now(),
                'updated_at' => now()
            ]
        ]);
    }
}