<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class ProductController extends Controller
{
    /**
     * Display a listing of products.
     */
    public function index(Request $request): View
    {
        $query = Product::active();

        // Apply filters using Purchasable interface methods
        if ($request->filled('category')) {
            $query->byCategory($request->category);
        }

        if ($request->filled('brand')) {
            $query->byBrand($request->brand);
        }

        if ($request->filled('vendor_id')) {
            $query->where('vendor_id', $request->vendor_id);
        }

        if ($request->filled('condition')) {
            $query->where('condition', $request->condition);
        }

        if ($request->filled('featured')) {
            $query->featured();
        }

        if ($request->filled('on_sale')) {
            $query->onSale();
        }

        if ($request->filled('in_stock')) {
            $query->inStock();
        }

        if ($request->filled('price_min')) {
            $query->where('price', '>=', $request->price_min);
        }

        if ($request->filled('price_max')) {
            $query->where('price', '<=', $request->price_max);
        }

        // Apply search using interface methods
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('short_description', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%")
                  ->orWhere('brand', 'like', "%{$search}%")
                  ->orWhere('model', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortBy = $request->get('sort', 'sort_order');
        $sortDirection = $request->get('direction', 'asc');

        switch ($sortBy) {
            case 'price':
                $query->orderBy('price', $sortDirection);
                break;
            case 'effective_price':
                $query->orderByRaw('COALESCE(sale_price, price) ' . $sortDirection);
                break;
            case 'name':
                $query->orderBy('name', $sortDirection);
                break;
            case 'brand':
                $query->orderBy('brand', $sortDirection)
                      ->orderBy('name', $sortDirection);
                break;
            case 'stock':
                $query->orderBy('stock_quantity', $sortDirection);
                break;
            case 'created_at':
                $query->orderBy('created_at', $sortDirection);
                break;
            default:
                $query->orderBy('sort_order', 'asc')
                      ->orderBy('created_at', 'desc');
        }

        $products = $query->with(['vendor'])->paginate(12);

        // Get filter options
        $vendors = User::whereHas('vendorProducts')->orderBy('name')->get();
        $brands = Product::active()->distinct()->pluck('brand')->filter()->sort();
        $categories = Product::active()->distinct()->pluck('category')->filter()->sort();
        $conditions = ['new', 'refurbished', 'used'];

        return view('products.index', compact('products', 'vendors', 'brands', 'categories', 'conditions'));
    }

    /**
     * Display the specified product.
     */
    public function show(string $slug): View
    {
        $product = Product::where('slug', $slug)
            ->active()
            ->with(['vendor'])
            ->firstOrFail();

        // Get related products using interface methods
        $relatedProducts = Product::active()
            ->where('id', '!=', $product->id)
            ->where('category', $product->getCategory())
            ->limit(4)
            ->get();

        // Get vendor's other products
        $vendorProducts = [];
        if ($product->vendor) {
            $vendorProducts = Product::active()
                ->where('vendor_id', $product->vendor_id)
                ->where('id', '!=', $product->id)
                ->limit(4)
                ->get();
        }

        // Calculate bulk pricing tiers if available
        $bulkPricingTiers = $this->getBulkPricingTiers($product);

        return view('products.show', compact('product', 'relatedProducts', 'vendorProducts', 'bulkPricingTiers'));
    }

    /**
     * Display products by category.
     */
    public function category(string $category): View
    {
        $products = Product::active()
            ->byCategory($category)
            ->with(['vendor'])
            ->orderBy('sort_order')
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('products.category', compact('products', 'category'));
    }

    /**
     * Display products by vendor.
     */
    public function vendor(int $vendorId): View
    {
        $vendor = User::findOrFail($vendorId);
        
        $products = Product::active()
            ->where('vendor_id', $vendorId)
            ->with(['vendor'])
            ->orderBy('sort_order')
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('products.vendor', compact('products', 'vendor'));
    }

    /**
     * Display featured products.
     */
    public function featured(): View
    {
        $products = Product::active()
            ->featured()
            ->with(['vendor'])
            ->orderBy('sort_order')
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('products.featured', compact('products'));
    }

    /**
     * Display products on sale.
     */
    public function sale(): View
    {
        $products = Product::active()
            ->onSale()
            ->with(['vendor'])
            ->orderBy('sort_order')
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('products.sale', compact('products'));
    }

    /**
     * Calculate bulk pricing for a product and quantity.
     *
     * @param Request $request
     * @param int $productId
     * @return JsonResponse
     */
    public function calculateBulkPrice(Request $request, int $productId): JsonResponse
    {
        $request->validate([
            'quantity' => 'required|integer|min:1'
        ]);

        $product = Product::findOrFail($productId);
        $quantity = $request->quantity;

        // Check if quantity meets minimum order requirements
        if ($quantity < $product->min_order_quantity) {
            return response()->json([
                'error' => "Minimum order quantity is {$product->min_order_quantity}",
                'min_quantity' => $product->min_order_quantity
            ], 400);
        }

        // Check if quantity exceeds maximum order requirements
        if ($product->max_order_quantity && $quantity > $product->max_order_quantity) {
            return response()->json([
                'error' => "Maximum order quantity is {$product->max_order_quantity}",
                'max_quantity' => $product->max_order_quantity
            ], 400);
        }

        // Check stock availability
        if (!$product->isAvailable($quantity)) {
            return response()->json([
                'error' => 'Insufficient stock',
                'available_stock' => $product->getStock()
            ], 400);
        }

        $unitPrice = $product->getBulkPrice($quantity);
        $totalPrice = $unitPrice * $quantity;
        $regularPrice = $product->getEffectivePrice();
        $savings = ($regularPrice - $unitPrice) * $quantity;

        return response()->json([
            'product_id' => $product->id,
            'product_name' => $product->getName(),
            'quantity' => $quantity,
            'unit_price' => $unitPrice,
            'total_price' => $totalPrice,
            'regular_unit_price' => $regularPrice,
            'regular_total_price' => $regularPrice * $quantity,
            'savings_per_unit' => $regularPrice - $unitPrice,
            'total_savings' => $savings,
            'bulk_pricing_applied' => $unitPrice < $regularPrice,
            'min_order_quantity' => $product->min_order_quantity,
            'max_order_quantity' => $product->max_order_quantity,
            'available_stock' => $product->getStock(),
        ]);
    }

    /**
     * Get bulk pricing tiers for a product.
     *
     * @param int $productId
     * @return JsonResponse
     */
    public function getBulkPricingTiers(int $productId): JsonResponse
    {
        $product = Product::findOrFail($productId);
        $tiers = $this->getBulkPricingTiers($product);

        return response()->json([
            'product_id' => $product->id,
            'product_name' => $product->getName(),
            'regular_price' => $product->getEffectivePrice(),
            'bulk_pricing_tiers' => $tiers,
            'has_bulk_pricing' => !empty($tiers),
            'min_order_quantity' => $product->min_order_quantity,
            'max_order_quantity' => $product->max_order_quantity,
        ]);
    }

    /**
     * Get product details with pricing information.
     *
     * @param int $productId
     * @return JsonResponse
     */
    public function getProductDetails(int $productId): JsonResponse
    {
        $product = Product::with(['vendor'])->findOrFail($productId);

        return response()->json([
            'id' => $product->id,
            'name' => $product->getName(),
            'slug' => $product->getSlug(),
            'brand' => $product->getBrand(),
            'model' => $product->getModel(),
            'category' => $product->getCategory(),
            'description' => $product->getDescription(),
            'short_description' => $product->short_description,
            'sku' => $product->sku,
            'price' => $product->getPrice(),
            'sale_price' => $product->sale_price,
            'effective_price' => $product->getEffectivePrice(),
            'stock' => $product->getStock(),
            'available' => $product->isAvailable(),
            'manage_stock' => $product->manage_stock,
            'in_stock' => $product->in_stock,
            'condition' => $product->condition,
            'warranty_months' => $product->warranty_months,
            'min_order_quantity' => $product->min_order_quantity,
            'max_order_quantity' => $product->max_order_quantity,
            'shipping_weight' => $product->getShippingWeight(),
            'vendor' => $product->vendor ? [
                'id' => $product->vendor->id,
                'name' => $product->vendor->name,
                'email' => $product->vendor->email,
            ] : null,
            'images' => $product->getImages(),
            'attributes' => $product->attributes,
            'tags' => $product->tags_json,
            'bulk_pricing_tiers' => $this->getBulkPricingTiers($product),
            'url' => $product->getUrl(),
        ]);
    }

    /**
     * Get products by vendor with filtering.
     *
     * @param Request $request
     * @param int $vendorId
     * @return JsonResponse
     */
    public function getVendorProducts(Request $request, int $vendorId): JsonResponse
    {
        $vendor = User::findOrFail($vendorId);
        
        $query = Product::active()->where('vendor_id', $vendorId);

        // Apply filters
        if ($request->filled('category')) {
            $query->byCategory($request->category);
        }

        if ($request->filled('featured')) {
            $query->featured();
        }

        if ($request->filled('on_sale')) {
            $query->onSale();
        }

        $products = $query->limit($request->get('limit', 10))->get();

        return response()->json([
            'vendor' => [
                'id' => $vendor->id,
                'name' => $vendor->name,
            ],
            'products' => $products->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->getName(),
                    'price' => $product->getPrice(),
                    'effective_price' => $product->getEffectivePrice(),
                    'image' => $product->getImage(),
                    'stock' => $product->getStock(),
                    'available' => $product->isAvailable(),
                    'url' => $product->getUrl(),
                ];
            })
        ]);
    }

    /**
     * Get bulk pricing tiers for a product.
     *
     * @param Product $product
     * @return array
     */
    protected function getBulkPricingTiers(Product $product): array
    {
        if (!$product->bulk_pricing_json) {
            return [];
        }

        $tiers = $product->bulk_pricing_json;
        $regularPrice = $product->getEffectivePrice();

        return array_map(function ($tier) use ($regularPrice) {
            $savings = $regularPrice - $tier['price'];
            $savingsPercent = ($savings / $regularPrice) * 100;

            return [
                'min_quantity' => $tier['min_quantity'],
                'price' => $tier['price'],
                'savings_per_unit' => $savings,
                'savings_percent' => round($savingsPercent, 2),
            ];
        }, $tiers);
    }
}