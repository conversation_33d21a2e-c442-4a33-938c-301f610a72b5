<?php

namespace App\Http\Controllers;

use App\Models\Component;
use App\Models\Product;
use App\Services\CartService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class CartController extends Controller
{
    protected CartService $cartService;
    
    public function __construct(CartService $cartService)
    {   
        $this->cartService = $cartService;
    }
    
    /**
     * Display the cart page.
     *
     * @return \Illuminate\View\View
     */
    public function index(): View
    {   
        $cart = $this->cartService->getCart();
        $cartItems = $cart ? $cart->items()->with(['component', 'product'])->get() : collect();
        
        return view('shop.cart', compact('cart', 'cartItems'));
    }
    
    /**
     * Add an item to the cart (unified for both components and products).
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function addItem(Request $request)
    {   
        $validated = $request->validate([
            'item_type' => 'required|in:component,product',
            'item_id' => 'required|integer',
            'quantity' => 'required|integer|min:1',
        ]);
        
        try {
            // Get the purchasable item
            $item = $this->getPurchasableItem($validated['item_type'], $validated['item_id']);
            
            if (!$item) {
                throw new \Exception('Item not found');
            }
            
            // Check availability using Purchasable interface
            if (!$item->isAvailable($validated['quantity'])) {
                throw new \Exception('Item is not available in the requested quantity');
            }
            
            $result = $this->cartService->addItem($item, $validated['quantity']);
            
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true, 
                    'message' => 'Item added to cart successfully!',
                    'cart_count' => $this->cartService->getItemCount(),
                    'cart_total' => $this->cartService->getTotal()
                ]);
            }
            
            return redirect()->back()->with('message', 'Item added to cart successfully!');
        } catch (\Exception $e) {
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => $e->getMessage()
                ], 422);
            }
            
            return redirect()->back()->with('error', $e->getMessage());
        }
    }
    
    /**
     * Update multiple items in the cart.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function updateItems(Request $request)
    {   
        $validated = $request->validate([
            'items' => 'required|array',
            'items.*.cart_item_id' => 'required|exists:cart_items,id',
            'items.*.quantity' => 'required|integer|min:1',
        ]);
        
        try {
            $errors = [];
            foreach ($validated['items'] as $itemData) {
                try {
                    $cartItem = $this->cartService->getCartItem($itemData['cart_item_id']);
                    if (!$cartItem) {
                        $errors[$itemData['cart_item_id']] = 'Cart item not found';
                        continue;
                    }
                    
                    // Check availability using Purchasable interface
                    $purchasableItem = $cartItem->item();
                    if (!$purchasableItem->isAvailable($itemData['quantity'])) {
                        $errors[$itemData['cart_item_id']] = 'Item is not available in the requested quantity';
                        continue;
                    }
                    
                    $this->cartService->updateCartItemQuantity($itemData['cart_item_id'], $itemData['quantity']);
                } catch (\Exception $e) {
                    $errors[$itemData['cart_item_id']] = $e->getMessage();
                }
            }
            
            if (!empty($errors)) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'errors' => $errors,
                        'cart_count' => $this->cartService->getItemCount(),
                        'cart_total' => $this->cartService->getTotal()
                    ], 422);
                }
                
                return redirect()->back()->with('error', 'Some items could not be updated.');
            }
            
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true, 
                    'message' => 'Cart updated successfully!',
                    'cart_count' => $this->cartService->getItemCount(),
                    'cart_total' => $this->cartService->getTotal()
                ]);
            }
            
            return redirect()->back()->with('message', 'Cart updated successfully!');
        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => $e->getMessage()
                ], 422);
            }
            
            return redirect()->back()->with('error', $e->getMessage());
        }
    }
    
    /**
     * Update the quantity of a single item in the cart.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $cartItemId
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function updateItem(Request $request, int $cartItemId)
    {   
        $validated = $request->validate([
            'quantity' => 'required|integer|min:1',
        ]);
        
        try {
            $cartItem = $this->cartService->getCartItem($cartItemId);
            if (!$cartItem) {
                throw new \Exception('Cart item not found');
            }
            
            // Check availability using Purchasable interface
            $purchasableItem = $cartItem->item();
            if (!$purchasableItem->isAvailable($validated['quantity'])) {
                throw new \Exception('Item is not available in the requested quantity');
            }
            
            $result = $this->cartService->updateCartItemQuantity($cartItemId, $validated['quantity']);
            
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Cart updated successfully!',
                    'cart_count' => $this->cartService->getItemCount(),
                    'cart_total' => $this->cartService->getTotal()
                ]);
            }
            
            return redirect()->back()->with('message', 'Cart updated successfully!');
        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => $e->getMessage()
                ], 422);
            }
            
            return redirect()->back()->with('error', $e->getMessage());
        }
    }
    
    /**
     * Remove an item from the cart.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $cartItemId
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function removeItem(Request $request, int $cartItemId)
    {   
        try {
            $result = $this->cartService->removeCartItem($cartItemId);
            
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true, 
                    'message' => 'Item removed from cart!',
                    'cart_count' => $this->cartService->getItemCount(),
                    'cart_total' => $this->cartService->getTotal()
                ]);
            }
            
            return redirect()->back()->with('message', 'Item removed from cart!');
        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => $e->getMessage()
                ], 422);
            }
            
            return redirect()->back()->with('error', $e->getMessage());
        }
    }
    
    /**
     * Clear all items from the cart.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function clearCart()
    {   
        $result = $this->cartService->clearCart();
        
        if ($result) {
            return redirect()->back()->with('message', 'Cart cleared successfully!');
        }
        
        return redirect()->back()->with('error', 'Failed to clear cart.');
    }
    
    /**
     * Add a build to the cart.
     *
     * @param  int  $buildId
     * @return \Illuminate\Http\RedirectResponse
     */
    public function addBuild($buildId)
    {   
        $result = $this->cartService->addBuild($buildId);
        
        if ($result) {
            return redirect()->route('cart.index')->with('message', 'Build added to cart successfully!');
        }
        
        return redirect()->back()->with('error', 'Failed to add build to cart.');
    }
    
    /**
     * Apply a coupon to the cart.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function applyCoupon(Request $request)
    {   
        $validated = $request->validate([
            'coupon_code' => 'required|string',
        ]);
        
        try {
            $result = $this->cartService->applyCoupon($validated['coupon_code']);
            
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'discount_amount' => $result['discount_amount'],
                    'message' => 'Coupon applied successfully'
                ]);
            }
            
            return redirect()->back()->with('message', 'Coupon applied successfully!');
        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => $e->getMessage()
                ], 422);
            }
            
            return redirect()->back()->with('error', $e->getMessage());
        }
    }
    
    /**
     * Get cart summary with unified item information.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCartSummary(): JsonResponse
    {
        $cart = $this->cartService->getCart();
        
        if (!$cart) {
            return response()->json([
                'items' => [],
                'total_items' => 0,
                'subtotal' => 0,
                'total' => 0,
                'empty' => true
            ]);
        }
        
        $cartItems = $cart->items()->with(['component', 'product'])->get();
        
        $items = $cartItems->map(function ($cartItem) {
            $item = $cartItem->item();
            
            return [
                'cart_item_id' => $cartItem->id,
                'item_type' => $cartItem->item_type,
                'item_id' => $cartItem->item_type === 'component' ? $cartItem->component_id : $cartItem->product_id,
                'name' => $item->getName(),
                'brand' => $item->getBrand(),
                'image' => $item->getImage(),
                'price' => $item->getEffectivePrice(),
                'quantity' => $cartItem->quantity,
                'total_price' => $cartItem->getTotalPrice(),
                'stock' => $item->getStock(),
                'available' => $item->isAvailable($cartItem->quantity),
                'max_quantity' => $item->getMaxCartQuantity(),
                'url' => $item->getUrl(),
            ];
        });
        
        return response()->json([
            'items' => $items,
            'total_items' => $this->cartService->getItemCount(),
            'subtotal' => $this->cartService->getSubtotal(),
            'total' => $this->cartService->getTotal(),
            'empty' => $items->isEmpty()
        ]);
    }
    
    /**
     * Check stock availability for all cart items.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkStock(): JsonResponse
    {
        $cart = $this->cartService->getCart();
        
        if (!$cart) {
            return response()->json([
                'all_available' => true,
                'issues' => []
            ]);
        }
        
        $cartItems = $cart->items()->with(['component', 'product'])->get();
        $issues = [];
        
        foreach ($cartItems as $cartItem) {
            $item = $cartItem->item();
            
            if (!$item->isAvailable($cartItem->quantity)) {
                $issues[] = [
                    'cart_item_id' => $cartItem->id,
                    'name' => $item->getName(),
                    'requested_quantity' => $cartItem->quantity,
                    'available_stock' => $item->getStock(),
                    'message' => "Only {$item->getStock()} units available for {$item->getName()}"
                ];
            }
        }
        
        return response()->json([
            'all_available' => empty($issues),
            'issues' => $issues
        ]);
    }
    
    /**
     * Proceed to checkout.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function checkout(): RedirectResponse
    {   
        // Check if user is logged in
        if (!auth()->check()) {
            return redirect()->route('login')->with('message', 'Please login to proceed with checkout.');
        }
        
        // Check if cart has items
        $cart = $this->cartService->getCart();
        
        if (!$cart || $cart->items()->count() === 0) {
            return redirect()->route('shop.cart')->with('error', 'Your cart is empty.');
        }
        
        // Check stock availability for all items
        $cartItems = $cart->items()->with(['component', 'product'])->get();
        $stockIssues = [];
        
        foreach ($cartItems as $cartItem) {
            $item = $cartItem->item();
            if (!$item->isAvailable($cartItem->quantity)) {
                $stockIssues[] = $item->getName();
            }
        }
        
        if (!empty($stockIssues)) {
            $message = 'Some items in your cart are no longer available: ' . implode(', ', $stockIssues);
            return redirect()->route('cart.index')->with('error', $message);
        }
        
        // Redirect to checkout page
        return redirect()->route('checkout.index');
    }
    
    /**
     * Get a purchasable item by type and ID.
     *
     * @param string $itemType
     * @param int $itemId
     * @return \App\Contracts\Purchasable|null
     */
    protected function getPurchasableItem(string $itemType, int $itemId)
    {
        switch ($itemType) {
            case 'component':
                return Component::find($itemId);
            case 'product':
                return Product::find($itemId);
            default:
                return null;
        }
    }
}