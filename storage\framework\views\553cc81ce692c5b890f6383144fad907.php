<div class="border rounded-lg overflow-hidden hover:shadow-lg transition-shadow bg-white">
    <!-- Product Image -->
    <div class="relative">
        <!--[if BLOCK]><![endif]--><?php if($component->image): ?>
            <img src="<?php echo e($component->image); ?>" alt="<?php echo e($component->name); ?>" class="w-full h-48 object-cover">
        <?php else: ?>
            <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        
        <!-- Quick View Button -->
        <button 
            wire:click="toggleQuickView" 
            class="absolute top-2 right-2 p-2 bg-white rounded-full shadow-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            title="Quick View"
        >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
        </button>

        <!-- Stock Badge -->
        <!--[if BLOCK]><![endif]--><?php if($component->stock <= 0): ?>
            <div class="absolute top-2 left-2 px-2 py-1 bg-red-500 text-white text-xs rounded">
                Out of Stock
            </div>
        <?php elseif($component->stock <= 5): ?>
            <div class="absolute top-2 left-2 px-2 py-1 bg-yellow-500 text-white text-xs rounded">
                Low Stock
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    <!-- Product Info -->
    <div class="p-4">
        <!-- Category -->
        <div class="text-xs text-gray-500 uppercase tracking-wide mb-1">
            <?php echo e($component->category->name); ?>

        </div>

        <!-- Product Name -->
        <h3 class="font-semibold text-lg mb-2 line-clamp-2">
            <a href="<?php echo e(route('shop.product', $component->slug ?? $component->id)); ?>" class="text-gray-900 hover:text-blue-600 transition-colors">
                <?php echo e($component->name); ?>

            </a>
        </h3>

        <!-- Brand and Model -->
        <p class="text-gray-600 mb-2 text-sm">
            <?php echo e($component->brand); ?> 
            <!--[if BLOCK]><![endif]--><?php if($component->model): ?>
                <?php echo e($component->model); ?>

            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </p>

        <!-- Key Specifications -->
        <!--[if BLOCK]><![endif]--><?php if($component->specs && is_array($component->specs)): ?>
            <div class="mb-3">
                <?php
                    $keySpecs = array_slice($component->specs, 0, 2);
                ?>
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $keySpecs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="text-xs text-gray-600">
                        <span class="font-medium"><?php echo e(ucfirst(str_replace('_', ' ', $key))); ?>:</span> <?php echo e($value); ?>

                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <!-- Price -->
        <div class="mb-3">
            <span class="text-xl font-bold text-gray-900">$<?php echo e(number_format($component->price, 2)); ?></span>
        </div>

        <!-- Stock Status -->
        <div class="mb-4">
            <!--[if BLOCK]><![endif]--><?php if($component->stock > 0): ?>
                <span class="text-green-600 text-sm font-medium">
                    In Stock (<?php echo e($component->stock); ?> available)
                </span>
            <?php else: ?>
                <span class="text-red-600 text-sm font-medium">Out of Stock</span>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>

        <!-- Quantity and Add to Cart -->
        <!--[if BLOCK]><![endif]--><?php if($component->stock > 0): ?>
            <div class="flex items-center space-x-2 mb-4">
                <label class="text-sm text-gray-600">Qty:</label>
                <div class="flex items-center border rounded">
                    <button 
                        wire:click="decrementQuantity" 
                        class="px-2 py-1 hover:bg-gray-100 focus:outline-none"
                        <?php echo e($quantity <= 1 ? 'disabled' : ''); ?>

                    >
                        -
                    </button>
                    <span class="px-3 py-1 border-x"><?php echo e($quantity); ?></span>
                    <button 
                        wire:click="incrementQuantity" 
                        class="px-2 py-1 hover:bg-gray-100 focus:outline-none"
                        <?php echo e($quantity >= $component->stock ? 'disabled' : ''); ?>

                    >
                        +
                    </button>
                </div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <!-- Action Buttons -->
        <div class="flex space-x-2">
            <button 
                wire:click="addToCart" 
                class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors <?php echo e($component->stock <= 0 ? 'opacity-50 cursor-not-allowed' : ''); ?>"
                <?php echo e($component->stock <= 0 ? 'disabled' : ''); ?>

            >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2-2v6.01" />
                </svg>
                Add to Cart
            </button>
            
            <a 
                href="<?php echo e(route('shop.product', $component->slug ?? $component->id)); ?>" 
                class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors text-center"
            >
                Details
            </a>
        </div>
    </div>

    <!-- Quick View Modal -->
    <!--[if BLOCK]><![endif]--><?php if($showQuickView): ?>
        <div class="fixed inset-0 z-50 overflow-y-auto" x-data="{ show: <?php if ((object) ('showQuickView') instanceof \Livewire\WireDirective) : ?>window.Livewire.find('<?php echo e($__livewire->getId()); ?>').entangle('<?php echo e('showQuickView'->value()); ?>')<?php echo e('showQuickView'->hasModifier('live') ? '.live' : ''); ?><?php else : ?>window.Livewire.find('<?php echo e($__livewire->getId()); ?>').entangle('<?php echo e('showQuickView'); ?>')<?php endif; ?>.live }">
            <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <!-- Background overlay -->
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" wire:click="toggleQuickView"></div>

                <!-- Modal panel -->
                <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="flex justify-between items-start mb-4">
                            <h3 class="text-lg font-medium text-gray-900"><?php echo e($component->name); ?></h3>
                            <button wire:click="toggleQuickView" class="text-gray-400 hover:text-gray-600">
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        <div class="grid grid-cols-1 gap-4">
                            <!-- Image -->
                            <!--[if BLOCK]><![endif]--><?php if($component->image): ?>
                                <img src="<?php echo e($component->image); ?>" alt="<?php echo e($component->name); ?>" class="w-full h-48 object-cover rounded">
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <!-- Details -->
                            <div>
                                <p class="text-gray-600 mb-2"><?php echo e($component->brand); ?> <?php echo e($component->model); ?></p>
                                <p class="text-2xl font-bold text-gray-900 mb-2">$<?php echo e(number_format($component->price, 2)); ?></p>
                                
                                <!--[if BLOCK]><![endif]--><?php if($component->description): ?>
                                    <p class="text-gray-700 mb-4"><?php echo e($component->description); ?></p>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                <!-- Specifications -->
                                <!--[if BLOCK]><![endif]--><?php if($component->specs && is_array($component->specs)): ?>
                                    <div class="mb-4">
                                        <h4 class="font-medium text-gray-900 mb-2">Specifications:</h4>
                                        <div class="space-y-1">
                                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $component->specs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="text-sm text-gray-600">
                                                    <span class="font-medium"><?php echo e(ucfirst(str_replace('_', ' ', $key))); ?>:</span> <?php echo e($value); ?>

                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                <!-- Stock -->
                                <div class="mb-4">
                                    <!--[if BLOCK]><![endif]--><?php if($component->stock > 0): ?>
                                        <span class="text-green-600 text-sm font-medium">In Stock (<?php echo e($component->stock); ?> available)</span>
                                    <?php else: ?>
                                        <span class="text-red-600 text-sm font-medium">Out of Stock</span>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <a 
                            href="<?php echo e(route('shop.product', $component->slug ?? $component->id)); ?>" 
                            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                        >
                            View Full Details
                        </a>
                        <button 
                            wire:click="toggleQuickView" 
                            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                        >
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div><?php /**PATH C:\lara\www\pc-builder\resources\views/livewire/shop/product-card.blade.php ENDPATH**/ ?>