<?php

namespace App\Livewire\Shop;

use App\Models\Component;
use Livewire\Component as LivewireComponent;

class ProductCard extends LivewireComponent
{
    public Component $component;
    public $showQuickView = false;
    public $quantity = 1;

    public function mount(Component $component)
    {
        $this->component = $component;
    }

    public function addToCart()
    {
        if (!$this->component || $this->component->stock <= 0) {
            session()->flash('error', 'Product is out of stock.');
            return;
        }

        if ($this->quantity > $this->component->stock) {
            session()->flash('error', 'Not enough stock available.');
            return;
        }

        try {
            // Add to cart using the CartService
            $cartService = app(\App\Services\CartService::class);
            $cartService->addToCart($this->component, $this->quantity);

            $this->dispatch('cartUpdated');
            session()->flash('message', 'Product added to cart successfully!');
            
            // Reset quantity after adding to cart
            $this->quantity = 1;
        } catch (\InvalidArgumentException $e) {
            session()->flash('error', $e->getMessage());
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to add product to cart. Please try again.');
        }
    }

    public function toggleQuickView()
    {
        $this->showQuickView = !$this->showQuickView;
    }

    public function incrementQuantity()
    {
        if ($this->component && $this->quantity < $this->component->stock) {
            $this->quantity++;
        }
    }

    public function decrementQuantity()
    {
        if ($this->quantity > 1) {
            $this->quantity--;
        }
    }

    public function render()
    {
        return view('livewire.shop.product-card');
    }
}