<?php

namespace App\Livewire\Shop;

use App\Services\CartService;
use Livewire\Component;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class CartIcon extends Component
{
    public $itemCount = 0;
    public $total = 0;
    public $showDropdown = false;
    public $recentItems = [];

    protected $listeners = [
        'cartUpdated' => 'refreshCartData',
        'cartRefreshed' => 'handleCartRefresh',
        'itemAdded' => 'refreshCartData',
        'itemRemoved' => 'refreshCartData',
        'cartCleared' => 'refreshCartData',
        'userLoggedIn' => 'handleUserLogin',
        'userLoggedOut' => 'handleUserLogout',
    ];

    public function mount()
    {
        try {
            $this->syncCartData();
            $this->refreshCartData();
        } catch (\Exception $e) {
            // Log error but don't break the component
            Log::error('CartIcon mount error: ' . $e->getMessage());
            $this->itemCount = 0;
            $this->total = 0;
            $this->recentItems = collect();
        }
    }

    public function syncCartData()
    {
        try {
            $cartService = app(CartService::class);

            if (Auth::check()) {
                $cartService->mergeGuestCart(Auth::user(), session()->getId());
            }

            $cart = $cartService->getCart();
            if ($cart) {
                $cartService->ensureCartIntegrity();
                $cartService->updateCartPrices();
                $cartService->validateCartStock();
            }
        } catch (\Exception $e) {
            // Log error but don't break the component
            Log::error('CartIcon syncCartData error: ' . $e->getMessage());
        }
    }

    public function refreshCartData()
    {
        try {
            $cartService = app(CartService::class);
            $this->itemCount = $cartService->getItemCount();
            $this->total = $cartService->getTotal();

            $cart = $cartService->getCart();
            if ($cart) {
                $this->recentItems = $cart->items()
                    ->with(['component', 'product'])
                    ->where(function ($query) {
                        $query->whereHas('component')
                            ->orWhereHas('product');
                    })
                    ->latest()
                    ->take(3)
                    ->get()
                    ->map(function ($cartItem) {
                        $item = $cartItem->item();
                        return (object) [
                            'id' => $cartItem->id,
                            'name' => $item->getName(),
                            'image' => $item->getImage(),
                            'price' => $item->getEffectivePrice(),
                            'quantity' => $cartItem->quantity,
                            'total_price' => $cartItem->getTotalPrice(),
                            'url' => $item->getUrl(),
                            'brand' => $item->getBrand(),
                            'stock' => $item->getStock(),
                            'available' => $item->isAvailable($cartItem->quantity),
                            'max_quantity' => $item->getMaxCartQuantity(),
                        ];
                    });
            } else {
                $this->recentItems = collect();
            }
        } catch (\Exception $e) {
            Log::error('CartIcon refreshCartData error: ' . $e->getMessage());
            $this->itemCount = 0;
            $this->total = 0;
            $this->recentItems = collect();
        }
    }

    public function handleCartRefresh($data)
    {
        $this->itemCount = $data['itemCount'] ?? 0;
        $this->total = $data['total'] ?? 0;
        $this->refreshCartData();
    }

    public function toggleDropdown()
    {
        $this->showDropdown = !$this->showDropdown;

        if ($this->showDropdown) {
            $this->refreshCartData();
        }
    }

    public function hideDropdown()
    {
        $this->showDropdown = false;
    }

    public function handleUserLogin()
    {
        $this->syncCartData();
        $this->refreshCartData();
    }

    public function handleUserLogout()
    {
        $this->refreshCartData();
    }

    public function quickRemoveItem($cartItemId)
    {
        try {
            $cartService = app(CartService::class);
            $cartService->removeCartItem($cartItemId);

            $this->refreshCartData();

            $this->dispatch('cartUpdated', [
                'itemCount' => $this->itemCount,
                'total' => $this->total
            ]);
            $this->dispatch('itemRemoved');
            $this->dispatch('item-removed');

            session()->flash('message', 'Item removed from cart!');
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to remove item from cart.');
        }
    }

    public function incrementQuantity($cartItemId)
    {
        try {
            $cartService = app(CartService::class);
            $cartItem = $cartService->getCartItem($cartItemId);

            if ($cartItem) {
                $item = $cartItem->item();
                $newQuantity = $cartItem->quantity + 1;

                // Use Purchasable interface to check availability
                if ($item->isAvailable($newQuantity)) {
                    $cartService->updateCartItemQuantity($cartItemId, $newQuantity);
                    $this->refreshCartData();

                    $this->dispatch('cartUpdated', [
                        'itemCount' => $this->itemCount,
                        'total' => $this->total
                    ]);
                    $this->dispatch('cart-updated');
                } else {
                    session()->flash('error', "Only {$item->getStock()} units available for {$item->getName()}.");
                }
            } else {
                session()->flash('error', 'Item not found.');
            }
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to update quantity.');
        }
    }

    public function decrementQuantity($cartItemId)
    {
        try {
            $cartService = app(CartService::class);
            $cartItem = $cartService->getCartItem($cartItemId);

            if ($cartItem) {
                if ($cartItem->quantity <= 1) {
                    $this->quickRemoveItem($cartItemId);
                } else {
                    $cartService->updateCartItemQuantity($cartItemId, $cartItem->quantity - 1);
                    $this->refreshCartData();

                    $this->dispatch('cartUpdated', [
                        'itemCount' => $this->itemCount,
                        'total' => $this->total
                    ]);
                    $this->dispatch('cart-updated');
                }
            }
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to update quantity.');
        }
    }

    public function addToCart($itemType, $itemId, $quantity = 1)
    {
        try {
            $cartService = app(CartService::class);
            
            // Get the purchasable item
            $item = $this->getPurchasableItem($itemType, $itemId);
            
            if (!$item) {
                throw new \Exception('Item not found');
            }

            // Check availability using Purchasable interface
            if (!$item->isAvailable($quantity)) {
                throw new \Exception("Only {$item->getStock()} units available for {$item->getName()}");
            }

            $cartService->addItem($item, $quantity);

            $this->refreshCartData();

            $this->dispatch('cartUpdated', [
                'itemCount' => $this->itemCount,
                'total' => $this->total
            ]);
            $this->dispatch('itemAdded');
            $this->dispatch('item-added');

            session()->flash('message', 'Item added to cart!');
        } catch (\Exception $e) {
            session()->flash('error', $e->getMessage());
        }
    }

    /**
     * Get a purchasable item by type and ID.
     *
     * @param string $itemType
     * @param int $itemId
     * @return \App\Contracts\Purchasable|null
     */
    protected function getPurchasableItem(string $itemType, int $itemId)
    {
        switch ($itemType) {
            case 'component':
                return \App\Models\Component::find($itemId);
            case 'product':
                return \App\Models\Product::find($itemId);
            default:
                return null;
        }
    }

    public function getFormattedTotal()
    {
        return number_format($this->total, 2);
    }

    public function getItemCountText()
    {
        if ($this->itemCount === 0) {
            return 'Empty cart';
        }

        return $this->itemCount . ' ' . \Str::plural('item', $this->itemCount);
    }

    public function render()
    {
        return view('livewire.shop.cart-icon');
    }
}
