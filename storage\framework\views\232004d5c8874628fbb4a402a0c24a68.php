<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #3b82f6;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f9fafb;
            padding: 20px;
            border: 1px solid #e5e7eb;
        }
        .order-details {
            background-color: white;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .item-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .item-row:last-child {
            border-bottom: none;
        }
        .total-row {
            font-weight: bold;
            font-size: 1.1em;
            background-color: #f3f4f6;
            padding: 10px;
            margin-top: 10px;
        }
        .address-section {
            background-color: white;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .footer {
            background-color: #374151;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 0 0 8px 8px;
            font-size: 0.9em;
        }
        .button {
            display: inline-block;
            background-color: #3b82f6;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Order Confirmation</h1>
        <p>Thank you for your order, <?php echo e($user->name); ?>!</p>
    </div>

    <div class="content">
        <div class="order-details">
            <h2>Order Details</h2>
            <p><strong>Order Number:</strong> <?php echo e($order->order_number); ?></p>
            <p><strong>Order Date:</strong> <?php echo e($order->created_at->format('F j, Y \a\t g:i A')); ?></p>
            <p><strong>Status:</strong> <?php echo e(ucfirst($order->status)); ?></p>
        </div>

        <div class="order-details">
            <h3>Items Ordered</h3>
            <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="item-row">
                    <div>
                        <strong><?php echo e($item->name); ?></strong><br>
                        <small>Quantity: <?php echo e($item->quantity); ?></small>
                    </div>
                    <div>
                        $<?php echo e(number_format($item->price * $item->quantity, 2)); ?>

                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            
            <div class="total-row">
                <div class="item-row">
                    <span>Subtotal:</span>
                    <span>$<?php echo e(number_format($order->subtotal, 2)); ?></span>
                </div>
                <?php if($order->tax > 0): ?>
                <div class="item-row">
                    <span>Tax:</span>
                    <span>$<?php echo e(number_format($order->tax, 2)); ?></span>
                </div>
                <?php endif; ?>
                <?php if($order->shipping > 0): ?>
                <div class="item-row">
                    <span>Shipping:</span>
                    <span>$<?php echo e(number_format($order->shipping, 2)); ?></span>
                </div>
                <?php endif; ?>
                <?php if($order->discount > 0): ?>
                <div class="item-row">
                    <span>Discount:</span>
                    <span>-$<?php echo e(number_format($order->discount, 2)); ?></span>
                </div>
                <?php endif; ?>
                <div class="item-row" style="font-size: 1.2em; font-weight: bold;">
                    <span>Total:</span>
                    <span>$<?php echo e(number_format($order->total, 2)); ?></span>
                </div>
            </div>
        </div>

        <div class="address-section">
            <h3>Shipping Address</h3>
            <p>
                <?php echo e($order->shipping_name); ?><br>
                <?php echo e($order->shipping_address); ?><br>
                <?php echo e($order->shipping_city); ?>, <?php echo e($order->shipping_state); ?> <?php echo e($order->shipping_zipcode); ?><br>
                <?php echo e($order->shipping_country); ?>

            </p>
        </div>

        <?php if($order->billing_address !== $order->shipping_address): ?>
        <div class="address-section">
            <h3>Billing Address</h3>
            <p>
                <?php echo e($order->billing_name); ?><br>
                <?php echo e($order->billing_address); ?><br>
                <?php echo e($order->billing_city); ?>, <?php echo e($order->billing_state); ?> <?php echo e($order->billing_zipcode); ?><br>
                <?php echo e($order->billing_country); ?>

            </p>
        </div>
        <?php endif; ?>

        <div style="text-align: center; margin: 20px 0;">
            <a href="<?php echo e(route('orders.show', $order)); ?>" class="button">View Order Details</a>
        </div>

        <?php if($order->notes): ?>
        <div class="order-details">
            <h3>Order Notes</h3>
            <p><?php echo e($order->notes); ?></p>
        </div>
        <?php endif; ?>
    </div>

    <div class="footer">
        <p>Questions about your order? Contact <NAME_EMAIL></p>
        <p>&copy; <?php echo e(date('Y')); ?> PC Builder. All rights reserved.</p>
    </div>
</body>
</html><?php /**PATH C:\lara\www\pc-builder\resources\views/emails/order-confirmation.blade.php ENDPATH**/ ?>