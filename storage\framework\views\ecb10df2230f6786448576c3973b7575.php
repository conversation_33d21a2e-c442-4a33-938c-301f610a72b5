

<?php $__env->startSection('title', 'Payment Status'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <?php if (isset($component)) { $__componentOriginalca12a1afafe34860db44e2b83437a69c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalca12a1afafe34860db44e2b83437a69c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.transaction-status','data' => ['transaction' => $transaction,'status' => $transaction->status,'showDetails' => true,'showActions' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('transaction-status'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['transaction' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($transaction),'status' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($transaction->status),'showDetails' => true,'showActions' => true]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalca12a1afafe34860db44e2b83437a69c)): ?>
<?php $attributes = $__attributesOriginalca12a1afafe34860db44e2b83437a69c; ?>
<?php unset($__attributesOriginalca12a1afafe34860db44e2b83437a69c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalca12a1afafe34860db44e2b83437a69c)): ?>
<?php $component = $__componentOriginalca12a1afafe34860db44e2b83437a69c; ?>
<?php unset($__componentOriginalca12a1afafe34860db44e2b83437a69c); ?>
<?php endif; ?>
        
        <?php if(in_array($transaction->status, ['pending', 'processing'])): ?>
            <!-- Additional information for pending transactions -->
            <div class="mt-8 bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">What's Happening?</h3>
                <div class="space-y-4">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 mt-1">
                            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900">Payment Initiated</h4>
                            <p class="text-sm text-gray-600 mt-1">Your payment request has been sent to <?php echo e(ucfirst($transaction->gateway_name)); ?>.</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 mt-1">
                            <div class="w-2 h-2 <?php echo e($transaction->status === 'processing' ? 'bg-blue-500' : 'bg-gray-300'); ?> rounded-full"></div>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900">Processing Payment</h4>
                            <p class="text-sm text-gray-600 mt-1">The payment gateway is verifying your transaction details.</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 mt-1">
                            <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900">Confirmation</h4>
                            <p class="text-sm text-gray-600 mt-1">You'll receive confirmation once the payment is complete.</p>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-yellow-400 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <div class="text-sm text-yellow-800">
                            <p class="font-medium mb-1">Please Wait</p>
                            <p>Don't close this page or navigate away. We're automatically checking for updates every few seconds.</p>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- FAQ Section -->
        <div class="mt-8 bg-white rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Frequently Asked Questions</h3>
            <div class="space-y-4">
                <div>
                    <h4 class="font-medium text-gray-900 mb-1">How long does payment processing take?</h4>
                    <p class="text-sm text-gray-600">Most payments are processed within 2-5 minutes. In rare cases, it may take up to 15 minutes.</p>
                </div>
                
                <div>
                    <h4 class="font-medium text-gray-900 mb-1">What if my payment is stuck in pending?</h4>
                    <p class="text-sm text-gray-600">If your payment remains pending for more than 15 minutes, please contact our support team with your transaction ID.</p>
                </div>
                
                <div>
                    <h4 class="font-medium text-gray-900 mb-1">Can I cancel a pending payment?</h4>
                    <p class="text-sm text-gray-600">Pending payments cannot be cancelled as they are being processed by the payment gateway. Please wait for the final status.</p>
                </div>
                
                <div>
                    <h4 class="font-medium text-gray-900 mb-1">Will I be charged if payment fails?</h4>
                    <p class="text-sm text-gray-600">No, you will only be charged if the payment is successfully completed. Failed payments do not result in any charges.</p>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\lara\www\pc-builder\resources\views/payments/status.blade.php ENDPATH**/ ?>