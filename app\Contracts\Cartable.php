<?php

namespace App\Contracts;

interface Cartable
{
    /**
     * Add this item to the cart
     */
    public function addToCart(int $quantity = 1): bool;

    /**
     * Get the display name for cart
     */
    public function getCartDisplayName(): string;

    /**
     * Get the image for cart display
     */
    public function getCartImage(): ?string;

    /**
     * Get the price for cart calculations
     */
    public function getCartPrice(): float;

    /**
     * Check if the item can be added to cart
     */
    public function canAddToCart(int $quantity = 1): bool;

    /**
     * Get the maximum quantity that can be added to cart
     */
    public function getMaxCartQuantity(): int;
}