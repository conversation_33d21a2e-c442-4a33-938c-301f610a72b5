<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Cart extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'session_id',
        'total',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'total' => 'decimal:2',
    ];

    /**
     * Get the user that owns the cart.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the items in the cart.
     */
    public function items(): HasMany
    {
        return $this->hasMany(CartItem::class);
    }

    /**
     * Add an item (component or product) to the cart.
     */
    public function addItem($item, int $quantity = 1, ?float $price = null)
    {
        $isProduct = $item instanceof \App\Models\Product;
        $isComponent = $item instanceof \App\Models\Component;

        if (!$isProduct && !$isComponent) {
            throw new \InvalidArgumentException('Item must be either a Component or Product');
        }

        // Determine search criteria based on item type
        $searchCriteria = $isProduct
            ? ['product_id' => $item->id, 'item_type' => CartItem::TYPE_PRODUCT]
            : ['component_id' => $item->id, 'item_type' => CartItem::TYPE_COMPONENT];

        // Check if item already exists in cart
        $existingItem = $this->items()->where($searchCriteria)->first();

        if ($existingItem) {
            $existingItem->quantity += $quantity;
            $existingItem->save();
            $this->updateTotal();
            return $existingItem;
        }

        // Create new cart item
        $itemData = [
            'quantity' => $quantity,
            'price' => $price ?? ($isProduct ? $item->effective_price : $item->price),
            'item_type' => $isProduct ? CartItem::TYPE_PRODUCT : CartItem::TYPE_COMPONENT,
        ];

        if ($isProduct) {
            $itemData['product_id'] = $item->id;
        } else {
            $itemData['component_id'] = $item->id;
        }

        $cartItem = $this->items()->create($itemData);

        $this->updateTotal();

        return $cartItem;
    }

    /**
     * Remove an item from the cart.
     */
    public function removeItem(int $itemId)
    {
        $result = $this->items()->where('id', $itemId)->delete();
        $this->updateTotal();
        return $result;
    }

    /**
     * Update the quantity of an item in the cart.
     */
    public function updateItemQuantity(int $itemId, int $quantity)
    {
        if ($quantity <= 0) {
            return $this->removeItem($itemId);
        }
        
        $item = $this->items()->where('id', $itemId)->first();
        
        if ($item) {
            $item->quantity = $quantity;
            $item->save();
            $this->updateTotal();
            return $item;
        }
        
        return false;
    }

    /**
     * Calculate and update the total price of the cart.
     */
    public function updateTotal()
    {
        $total = $this->items()->sum(\DB::raw('price * quantity'));
        
        $this->total = $total;
        $this->save();
        
        return $total;
    }

    /**
     * Clear all items from the cart.
     */
    public function clear()
    {
        $this->items()->delete();
        $this->total = 0;
        $this->save();
        
        return true;
    }

    /**
     * Add a build to the cart.
     */
    public function addBuild(Build $build)
    {
        $buildComponents = $build->components()->with('component')->get();
        
        foreach ($buildComponents as $buildComponent) {
            $this->addItem(
                $buildComponent->component,
                $buildComponent->quantity,
                $buildComponent->price
            );
        }
        
        return true;
    }
}