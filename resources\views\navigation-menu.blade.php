<nav x-data="{
    open: false,
    isScrolled: false,
    searchOpen: false,
    searchQuery: '',
    searchSuggestions: [],
    showSuggestions: false,
    activeDropdown: null,
    activeSuggestionIndex: -1,
    searchLoading: false,
    recentSearches: JSON.parse(localStorage.getItem('recentSearches') || '[]'),

    // Sample data - replace with actual API calls
    sampleSuggestions: [
        { id: 1, name: 'RTX 4090', type: 'GPU', category: 'Graphics Card', price: '$1599', image: '🎮' },
        { id: 2, name: 'Intel i9-13900K', type: 'CPU', category: 'Processor', price: '$589', image: '🖥️' },
        { id: 3, name: 'Gaming Build 2024', type: 'Build', category: 'Complete Build', price: '$2499', image: '🏗️' },
        { id: 4, name: 'DDR5-6000 32GB', type: 'Memory', category: 'RAM', price: '$299', image: '💾' },
        { id: 5, name: 'RTX 4080', type: 'GPU', category: 'Graphics Card', price: '$1199', image: '🎮' },
        { id: 6, name: 'AMD Ryzen 7900X', type: 'CPU', category: 'Processor', price: '$449', image: '🖥️' },
        { id: 7, name: 'Productivity Build', type: 'Build', category: 'Complete Build', price: '$1899', image: '🏗️' },
        { id: 8, name: 'NVMe SSD 2TB', type: 'Storage', category: 'Storage', price: '$199', image: '💿' }
    ],

    init() {
        this.handleScroll();
        window.addEventListener('scroll', () => this.handleScroll());

        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('[data-dropdown]')) {
                this.activeDropdown = null;
            }
            if (!e.target.closest('.search-container')) {
                this.showSuggestions = false;
            }
        });

        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.open = false;
                this.searchOpen = false;
                this.activeDropdown = null;
                this.showSuggestions = false;
                this.activeSuggestionIndex = -1;
            }
        });

        // Preload theme
        this.applyTheme();
    },

    handleScroll() {
        this.isScrolled = window.pageYOffset > 20;
    },

    toggleDropdown(dropdown) {
        this.activeDropdown = this.activeDropdown === dropdown ? null : dropdown;
    },

    async handleSearch() {
        if (this.searchQuery.length < 2) {
            this.showSuggestions = false;
            return;
        }

        this.searchLoading = true;

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 200));

        // Filter sample suggestions
        this.searchSuggestions = this.sampleSuggestions.filter(item =>
            item.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
            item.category.toLowerCase().includes(this.searchQuery.toLowerCase())
        ).slice(0, 6);

        this.showSuggestions = this.searchSuggestions.length > 0;
        this.searchLoading = false;
        this.activeSuggestionIndex = -1;
    },

    selectSuggestion(suggestion) {
        this.searchQuery = suggestion.name;
        this.showSuggestions = false;
        this.activeSuggestionIndex = -1;
        this.addToRecentSearches(suggestion);
        // Navigate to suggestion URL
        console.log('Navigating to:', suggestion);
    },

    addToRecentSearches(suggestion) {
        const recent = this.recentSearches.filter(item => item.id !== suggestion.id);
        recent.unshift(suggestion);
        this.recentSearches = recent.slice(0, 5);
        localStorage.setItem('recentSearches', JSON.stringify(this.recentSearches));
    },

    clearRecentSearches() {
        this.recentSearches = [];
        localStorage.removeItem('recentSearches');
    },

    navigateSuggestions(direction) {
        if (!this.showSuggestions || this.searchSuggestions.length === 0) return;

        if (direction === 'down') {
            this.activeSuggestionIndex = this.activeSuggestionIndex < this.searchSuggestions.length - 1 ?
                this.activeSuggestionIndex + 1 :
                0;
        } else if (direction === 'up') {
            this.activeSuggestionIndex = this.activeSuggestionIndex > 0 ?
                this.activeSuggestionIndex - 1 :
                this.searchSuggestions.length - 1;
        }

        // Scroll active suggestion into view
        this.$nextTick(() => {
            const activeEl = document.querySelector(`[data-suggestion-index='${this.activeSuggestionIndex}']`);
            if (activeEl) {
                activeEl.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
            }
        });
    },

    selectCurrentSuggestion() {
        if (this.activeSuggestionIndex >= 0 && this.activeSuggestionIndex < this.searchSuggestions.length) {
            this.selectSuggestion(this.searchSuggestions[this.activeSuggestionIndex]);
        } else if (this.searchQuery.trim()) {
            this.handleSearchSubmit();
        }
    },

    handleSearchSubmit() {
        if (this.searchQuery.trim()) {
            this.addToRecentSearches({
                id: Date.now(),
                name: this.searchQuery,
                type: 'Search',
                category: 'Search Query'
            });
            console.log('Search submitted:', this.searchQuery);
            this.showSuggestions = false;
        }
    },

    performAdvancedSearch() {
        console.log('Advanced search for:', this.searchQuery);
        this.showSuggestions = false;
    },

    applyTheme() {
        const isDark = localStorage.getItem('theme') === 'dark' ||
            (!localStorage.getItem('theme') && window.matchMedia('(prefers-color-scheme: dark)').matches);
        document.documentElement.classList.toggle('dark', isDark);
    },

    toggleTheme() {
        const isDark = document.documentElement.classList.contains('dark');
        document.documentElement.classList.toggle('dark', !isDark);
        localStorage.setItem('theme', !isDark ? 'dark' : 'light');
    }
}" @scroll.window="handleScroll()"
    @keydown.escape.window="open = false; searchOpen = false; activeDropdown = null; showSuggestions = false" x-cloak
    :class="{
        'bg-white/95 dark:bg-gray-900/95 shadow-2xl border-b-2 border-blue-500/20': isScrolled,
        'bg-white/90 dark:bg-gray-900/90 shadow-lg': !isScrolled
    }"
    class="fixed top-0 w-full z-50 transition-all duration-500 ease-out nav-blur border-b border-gray-200/30 dark:border-gray-700/30">

    <!-- Primary Navigation Menu -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <!-- Left Section: Logo & Navigation -->
            <div class="flex items-center">
                <!-- Enhanced Logo -->
                <div class="shrink-0 flex items-center">
                    <a href="{{ route('home') }}" class="flex items-center group">
                        <div class="relative">
                            <div
                                class="w-10 h-10 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 dark:from-blue-500 dark:via-purple-500 dark:to-indigo-600 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-2xl transition-all duration-500 transform group-hover:scale-110 group-hover:rotate-3">
                                <span
                                    class="text-white font-bold text-xl tracking-tight group-hover:scale-110 transition-transform duration-300">N</span>
                            </div>
                            <div
                                class="absolute -inset-2 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl opacity-0 group-hover:opacity-30 transition-all duration-500 blur-lg">
                            </div>
                        </div>
                        <div class="ml-3">
                            <span
                                class="text-xl font-bold bg-gradient-to-r from-gray-900 via-gray-700 to-gray-900 dark:from-white dark:via-gray-200 dark:to-white bg-clip-text text-transparent group-hover:from-blue-600 group-hover:to-purple-600 transition-all duration-300">Nexus</span>
                            <div
                                class="text-xs text-gray-500 dark:text-gray-400 font-medium -mt-1 group-hover:text-blue-500 dark:group-hover:text-blue-400 transition-colors duration-300">
                                PC Builder</div>
                        </div>
                    </a>
                </div>

                <!-- Desktop Navigation Links -->
                <div class="hidden lg:flex lg:space-x-1 lg:ml-10">
                    <!-- Enhanced Shop Link -->
                    <a href="{{ route('shop.index') }}"
                        class="group flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 dark:hover:from-blue-900/30 dark:hover:to-purple-900/30 rounded-xl transition-all duration-300 ease-out transform hover:scale-105">
                        <svg class="w-4 h-4 mr-2 group-hover:scale-125 group-hover:rotate-12 transition-all duration-300"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                        </svg>
                        Shop
                        <div
                            class="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-400/0 to-purple-400/0 group-hover:from-blue-400/10 group-hover:to-purple-400/10 transition-all duration-300">
                        </div>
                    </a>

                    <!-- Enhanced PC Builder Link -->
                    <a href="{{ route('builder.index') }}"
                        class="group flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 dark:hover:from-purple-900/30 dark:hover:to-pink-900/30 rounded-xl transition-all duration-300 ease-out transform hover:scale-105 relative">
                        <svg class="w-4 h-4 mr-2 group-hover:scale-125 group-hover:rotate-12 transition-all duration-300"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                            </path>
                        </svg>
                        PC Builder
                        <span
                            class="absolute -top-1 -right-1 bg-gradient-to-r from-orange-400 to-red-500 text-white text-xs px-1.5 py-0.5 rounded-full font-bold animate-pulse">NEW</span>
                    </a>

                    <!-- Enhanced Blog Link -->
                    <a href="{{ route('blog.index') }}"
                        class="group flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 dark:hover:from-green-900/30 dark:hover:to-emerald-900/30 rounded-xl transition-all duration-300 ease-out transform hover:scale-105 relative">
                        <svg class="w-4 h-4 mr-2 group-hover:scale-125 group-hover:rotate-12 transition-all duration-300"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z">
                            </path>
                        </svg>
                        Blog
                    </a>

                    <!-- Enhanced Tools Dropdown -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" @click.away="open = false"
                            class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/30 dark:hover:to-indigo-900/30 rounded-xl transition-all duration-300 ease-out inline-flex items-center group transform hover:scale-105">
                            <svg class="w-4 h-4 mr-2 group-hover:scale-125 group-hover:rotate-12 transition-all duration-300"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z">
                                </path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            Tools
                            <svg class="ml-2 h-4 w-4 transition-all duration-300"
                                :class="{ 'rotate-180 scale-110': open }" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>

                        <div x-show="open" x-transition:enter="transition ease-out duration-300"
                            x-transition:enter-start="opacity-0 scale-95 translate-y-2"
                            x-transition:enter-end="opacity-100 scale-100 translate-y-0"
                            x-transition:leave="transition ease-in duration-200"
                            x-transition:leave-start="opacity-100 scale-100 translate-y-0"
                            x-transition:leave-end="opacity-0 scale-95 translate-y-2"
                            class="absolute left-0 mt-3 w-64 glass-effect rounded-2xl shadow-2xl ring-1 ring-black/5 dark:ring-white/10 py-2 fade-in-up">
                            <div
                                class="px-4 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider border-b border-gray-200/50 dark:border-gray-700/50">
                                Utility Tools
                            </div>
                            <a href="#"
                                class="group flex items-center px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 dark:hover:from-blue-900/30 dark:hover:to-purple-900/30 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-200 suggestion-item">
                                <span
                                    class="w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center mr-3 text-white text-xs font-bold group-hover:scale-110 transition-transform duration-200">⚡</span>
                                <div>
                                    <div class="font-medium">Power Calculator</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">Calculate PSU requirements
                                    </div>
                                </div>
                            </a>
                            <a href="#"
                                class="group flex items-center px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 dark:hover:from-green-900/30 dark:hover:to-emerald-900/30 hover:text-green-600 dark:hover:text-green-400 transition-all duration-200 suggestion-item">
                                <span
                                    class="w-8 h-8 bg-gradient-to-br from-green-400 to-blue-500 rounded-lg flex items-center justify-center mr-3 text-white text-xs font-bold group-hover:scale-110 transition-transform duration-200">✓</span>
                                <div>
                                    <div class="font-medium">Compatibility Checker</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">Verify component compatibility
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Center Section: Enhanced Search Form (Desktop) -->
            <div class="hidden lg:flex lg:items-center lg:flex-1 lg:max-w-2xl lg:mx-8 search-container">
                <form action="#" method="GET" class="w-full" @submit.prevent="handleSearchSubmit()">
                    <div class="relative group">
                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                            <svg x-show="!searchLoading"
                                class="h-5 w-5 text-gray-400 dark:text-gray-500 group-focus-within:text-blue-500 dark:group-focus-within:text-blue-400 transition-all duration-300"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            <div x-show="searchLoading"
                                class="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin">
                            </div>
                        </div>
                        <input type="text" name="query" x-model="searchQuery"
                            @input.debounce.250ms="handleSearch()"
                            @focus="showSuggestions = searchQuery.length >= 2 || recentSearches.length > 0"
                            @keydown.arrow-down.prevent="navigateSuggestions('down')"
                            @keydown.arrow-up.prevent="navigateSuggestions('up')"
                            @keydown.enter.prevent="selectCurrentSuggestion()"
                            @keydown.escape="showSuggestions = false"
                            placeholder="Search components, builds, or guides..."
                            class="w-full pl-12 pr-16 py-4 bg-gradient-to-r from-gray-50/90 to-gray-100/90 dark:from-gray-800/90 dark:to-gray-700/90 border border-gray-200/60 dark:border-gray-600/60 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-4 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 focus:border-blue-500/60 dark:focus:border-blue-400/60 focus:bg-white dark:focus:bg-gray-800 rounded-2xl transition-all duration-300 text-sm nav-blur shadow-lg hover:shadow-xl focus:shadow-2xl search-glow group-hover:scale-[1.02] focus:scale-[1.02] transform"
                            autocomplete="off" aria-label="Search" :aria-expanded="showSuggestions" role="combobox"
                            aria-autocomplete="list" />

                        <!-- Enhanced action buttons -->
                        <div class="absolute inset-y-0 right-0 flex items-center space-x-1 pr-3">
                            <button type="button" x-show="searchQuery.length > 0"
                                @click="searchQuery = ''; showSuggestions = false"
                                class="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-200/50 dark:hover:bg-gray-600/50 rounded-lg transition-all duration-200 transform hover:scale-110"
                                aria-label="Clear search">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                            <div class="w-px h-6 bg-gray-300 dark:bg-gray-600"></div>
                            <button type="submit"
                                class="p-1.5 text-blue-500 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/30 rounded-lg transition-all duration-200 transform hover:scale-110">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                </svg>
                            </button>
                        </div>

                        <!-- Enhanced Search suggestions dropdown -->
                        <div x-show="showSuggestions && (searchSuggestions.length > 0 || (searchQuery.length < 2 && recentSearches.length > 0))"
                            x-cloak x-transition:enter="transition ease-out duration-300"
                            x-transition:enter-start="opacity-0 scale-95 translate-y-2"
                            x-transition:enter-end="opacity-100 scale-100 translate-y-0"
                            x-transition:leave="transition ease-in duration-200"
                            x-transition:leave-start="opacity-100 scale-100 translate-y-0"
                            x-transition:leave-end="opacity-0 scale-95 translate-y-2"
                            @click.away="showSuggestions = false"
                            class="absolute top-full left-0 right-0 mt-3 glass-effect rounded-2xl shadow-2xl border border-gray-200/50 dark:border-gray-700/50 z-50 max-h-[500px] overflow-hidden fade-in-up"
                            role="listbox">
                            <!-- Recent searches (when no query) -->
                            <div x-show="searchQuery.length < 2 && recentSearches.length > 0">
                                <div
                                    class="flex items-center justify-between p-4 border-b border-gray-200/50 dark:border-gray-700/50">
                                    <span class="text-sm font-semibold text-gray-700 dark:text-gray-300">Recent
                                        Searches</span>
                                    <button @click="clearRecentSearches()"
                                        class="text-xs text-gray-500 hover:text-red-500 transition-colors duration-200">Clear</button>
                                </div>
                                <div class="py-2 suggestions-scroll overflow-y-auto max-h-64">
                                    <template x-for="(search, index) in recentSearches" :key="search.id">
                                        <button type="button" @click="selectSuggestion(search)"
                                            class="w-full text-left px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-gray-100/80 hover:to-gray-50/80 dark:hover:from-gray-700/50 dark:hover:to-gray-800/50 transition-all duration-200 flex items-center group suggestion-item">
                                            <div
                                                class="w-8 h-8 bg-gradient-to-br from-gray-400 to-gray-600 rounded-lg flex items-center justify-center mr-3 text-white text-xs font-semibold group-hover:scale-110 transition-transform duration-200">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                            </div>
                                            <div class="flex-1">
                                                <div x-text="search.name" class="font-medium"></div>
                                                <div class="text-xs text-gray-500 dark:text-gray-400">Recent search
                                                </div>
                                            </div>
                                        </button>
                                    </template>
                                </div>
                            </div>

                            <!-- Search results -->
                            <div x-show="searchSuggestions.length > 0">
                                <div
                                    class="p-4 text-sm text-gray-600 dark:text-gray-400 border-b border-gray-200/50 dark:border-gray-700/50 font-medium flex items-center justify-between">
                                    <span>
                                        <span x-text="searchSuggestions.length"></span> results found
                                    </span>
                                    <span
                                        class="text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 px-2 py-1 rounded-full">
                                        ↑↓ Navigate • Enter Select
                                    </span>
                                </div>
                                <div class="py-2 suggestions-scroll overflow-y-auto max-h-80">
                                    <template x-for="(suggestion, index) in searchSuggestions" :key="suggestion.id">
                                        <button type="button" @click="selectSuggestion(suggestion)"
                                            @mouseenter="activeSuggestionIndex = index" :data-suggestion-index="index"
                                            :class="{
                                                'bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/30 dark:to-purple-900/30 border-l-4 border-blue-500': activeSuggestionIndex ===
                                                    index,
                                                'hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100/50 dark:hover:from-gray-700/30 dark:hover:to-gray-800/30': activeSuggestionIndex !==
                                                    index
                                            }"
                                            class="w-full text-left px-4 py-4 text-sm text-gray-700 dark:text-gray-300 transition-all duration-200 flex items-center justify-between group suggestion-item"
                                            role="option" :aria-selected="activeSuggestionIndex === index">
                                            <div class="flex items-center flex-1">
                                                <div
                                                    class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mr-4 text-white text-lg font-bold group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-lg">
                                                    <span
                                                        x-text="suggestion.image || suggestion.type.charAt(0)"></span>
                                                </div>
                                                <div class="flex-1">
                                                    <div class="flex items-center space-x-2">
                                                        <span x-text="suggestion.name"
                                                            class="font-semibold text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200"></span>
                                                        <span x-show="suggestion.price" x-text="suggestion.price"
                                                            class="text-sm font-medium text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/30 px-2 py-0.5 rounded-full"></span>
                                                    </div>
                                                    <div class="flex items-center space-x-2 mt-1">
                                                        <span x-text="suggestion.category"
                                                            class="text-xs text-gray-500 dark:text-gray-400"></span>
                                                        <span class="w-1 h-1 bg-gray-400 rounded-full"></span>
                                                        <span x-text="suggestion.type"
                                                            class="text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 px-2 py-0.5 rounded-full font-medium"></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div
                                                class="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                                <svg class="w-4 h-4 text-blue-500 transform group-hover:translate-x-1 transition-transform duration-200"
                                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                </svg>
                                            </div>
                                        </button>
                                    </template>
                                </div>
                            </div>

                            <!-- Quick actions footer -->
                            <div
                                class="border-t border-gray-200/50 dark:border-gray-700/50 p-3 bg-gradient-to-r from-gray-50/50 to-white/50 dark:from-gray-800/50 dark:to-gray-900/50">
                                <div class="flex items-center justify-between space-x-2">
                                    <button type="button" @click="performAdvancedSearch()"
                                        class="flex-1 px-3 py-2 text-xs font-medium text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/30 rounded-lg transition-all duration-200 flex items-center justify-center transform hover:scale-105">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                        Advanced Search
                                    </button>
                                    <div class="w-px h-6 bg-gray-300 dark:bg-gray-600"></div>
                                    <button type="button"
                                        class="flex-1 px-3 py-2 text-xs font-medium text-purple-600 dark:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/30 rounded-lg transition-all duration-200 flex items-center justify-center transform hover:scale-105">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z">
                                            </path>
                                        </svg>
                                        Filters
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Right Section: Enhanced Cart, Theme, Auth -->
            <div class="flex items-center space-x-3">
                <!-- Debug indicator -->
                
                    @livewire('shop.cart-icon')
                   
                </div>

                <!-- Always Visible Cart Icon -->
               





                <!-- Enhanced Theme Toggle -->
                <button @click="toggleTheme()"
                    class="relative p-3 text-gray-700 dark:text-gray-300 hover:text-amber-600 dark:hover:text-amber-400 hover:bg-gradient-to-r hover:from-amber-50 hover:to-orange-50 dark:hover:from-amber-900/30 dark:hover:to-orange-900/30 rounded-xl transition-all duration-500 group transform hover:scale-110 overflow-hidden">
                    <!-- Enhanced sun icon -->
                    <svg class="h-6 w-6 hidden dark:block group-hover:rotate-180 transition-all duration-500"
                        fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z">
                        </path>
                    </svg>
                    <!-- Enhanced moon icon -->
                    <svg class="h-6 w-6 block dark:hidden group-hover:-rotate-12 transition-all duration-500"
                        fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z">
                        </path>
                    </svg>
                    <div
                        class="absolute inset-0 rounded-xl bg-gradient-to-r from-amber-400/20 to-orange-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                    </div>
                </button>

                <!-- Enhanced Authentication Links (Desktop) -->
                <div class="hidden lg:flex lg:items-center lg:space-x-3">
                    <!-- Login Button -->
                    <button
                        class="group relative px-8 py-3 text-sm font-bold text-white bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105 overflow-hidden">
                        <span class="relative z-10 flex items-center">
                            <svg class="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform duration-300"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1">
                                </path>
                            </svg>
                            Login
                        </span>
                        <div
                            class="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                        </div>
                        <div
                            class="absolute -inset-1 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl opacity-0 group-hover:opacity-30 blur-lg transition-opacity duration-500">
                        </div>
                    </button>
                </div>

                <!-- Enhanced Mobile Menu Button -->
                <button @click="open = !open"
                    class="lg:hidden relative p-3 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 dark:hover:from-blue-900/30 dark:hover:to-purple-900/30 rounded-xl transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 group transform hover:scale-110">
                    <svg class="h-6 w-6 transition-all duration-500"
                        :class="{ 'opacity-0 rotate-180 scale-75': open, 'opacity-100 rotate-0 scale-100': !open }"
                        fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                    <svg class="h-6 w-6 absolute inset-3 transition-all duration-500"
                        :class="{ 'opacity-100 rotate-0 scale-100': open, 'opacity-0 -rotate-180 scale-75': !open }"
                        fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    <div
                        class="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    </div>
                </button>
            </div>
        </div>
    </div>

    <!-- Enhanced Mobile Menu -->
    <div x-show="open" x-cloak x-transition:enter="transition ease-out duration-400"
        x-transition:enter-start="opacity-0 transform -translate-y-4"
        x-transition:enter-end="opacity-100 transform translate-y-0"
        x-transition:leave="transition ease-in duration-300"
        x-transition:leave-start="opacity-100 transform translate-y-0"
        x-transition:leave-end="opacity-0 transform -translate-y-4"
        class="lg:hidden glass-effect border-t border-gray-200/50 dark:border-gray-700/50 shadow-2xl">

        <!-- Enhanced Mobile Search -->
        <div class="px-6 py-6 border-b border-gray-200/50 dark:border-gray-700/50">
            <form action="#" method="GET" @submit.prevent="handleSearchSubmit()">
                <div class="relative group">
                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400 dark:text-gray-500 group-focus-within:text-blue-500 dark:group-focus-within:text-blue-400 transition-colors duration-300"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <input type="text" name="query" x-model="searchQuery"
                        placeholder="Search components, builds..."
                        class="w-full pl-12 pr-4 py-4 bg-gradient-to-r from-gray-50/90 to-gray-100/90 dark:from-gray-800/90 dark:to-gray-700/90 border border-gray-200/60 dark:border-gray-600/60 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-4 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 focus:border-blue-500/60 dark:focus:border-blue-400/60 focus:bg-white dark:focus:bg-gray-800 rounded-2xl transition-all duration-300 text-sm shadow-lg focus:shadow-xl" />
                </div>
            </form>
        </div>

        <!-- Enhanced Mobile Navigation Links -->
        <div class="px-6 pt-4 pb-6 space-y-3">
            <a href="{{ route('shop.index') }}"
                class="group flex items-center px-5 py-4 text-base font-semibold text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 dark:hover:from-blue-900/30 dark:hover:to-purple-900/30 rounded-2xl transition-all duration-300 transform hover:scale-105">
                <svg class="w-6 h-6 mr-4 group-hover:scale-125 group-hover:rotate-12 transition-all duration-300"
                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                </svg>
                <span class="flex-1">Shop</span>
                <svg class="w-5 h-5 text-gray-400 group-hover:text-blue-500 group-hover:translate-x-1 transition-all duration-300"
                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>

            <a href="{{ route('builder.index') }}"
                class="group flex items-center px-5 py-4 text-base font-semibold text-gray-700 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 dark:hover:from-purple-900/30 dark:hover:to-pink-900/30 rounded-2xl transition-all duration-300 transform hover:scale-105 relative">
                <svg class="w-6 h-6 mr-4 group-hover:scale-125 group-hover:rotate-12 transition-all duration-300"
                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                    </path>
                </svg>
                <span class="flex-1">PC Builder</span>
                <span
                    class="bg-gradient-to-r from-orange-400 to-red-500 text-white text-xs px-2 py-1 rounded-full font-bold mr-2">NEW</span>
                <svg class="w-5 h-5 text-gray-400 group-hover:text-purple-500 group-hover:translate-x-1 transition-all duration-300"
                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>

            <a href="{{ route('blog.index') }}"
                class="group flex items-center px-5 py-4 text-base font-semibold text-gray-700 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 dark:hover:from-green-900/30 dark:hover:to-emerald-900/30 rounded-2xl transition-all duration-300 transform hover:scale-105">
                <svg class="w-6 h-6 mr-4 group-hover:scale-125 group-hover:rotate-12 transition-all duration-300"
                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z">
                    </path>
                </svg>
                <span class="flex-1">Blog</span>
                <svg class="w-5 h-5 text-gray-400 group-hover:text-green-500 group-hover:translate-x-1 transition-all duration-300"
                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>



            <!-- Enhanced Mobile Tools Section -->
            <div class="mt-8">
                <div
                    class="px-3 py-2 text-sm font-bold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-4 flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z">
                        </path>
                    </svg>
                    Utility Tools
                </div>
                <div class="space-y-2 ml-6">
                    <a href="#"
                        class="group flex items-center py-3 text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-200">
                        <span
                            class="w-6 h-6 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center mr-3 text-white text-xs font-bold group-hover:scale-110 transition-transform duration-200">⚡</span>
                        Power Calculator
                    </a>
                    <a href="#"
                        class="group flex items-center py-3 text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-all duration-200">
                        <span
                            class="w-6 h-6 bg-gradient-to-br from-green-400 to-blue-500 rounded-lg flex items-center justify-center mr-3 text-white text-xs font-bold group-hover:scale-110 transition-transform duration-200">✓</span>
                        Compatibility Checker
                    </a>
                </div>
            </div>
        </div>

        <!-- Enhanced Mobile Authentication -->
        <div
            class="pt-6 pb-8 border-t border-gray-200/50 dark:border-gray-700/50 bg-gradient-to-b from-transparent to-gray-50/50 dark:to-gray-800/50">
            <div class="px-6">
                <button
                    class="group relative block w-full text-center px-8 py-4 text-base font-bold text-white bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105 overflow-hidden">
                    <span class="relative z-10 flex items-center justify-center">
                        <svg class="w-5 h-5 mr-3 group-hover:rotate-12 transition-transform duration-300"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1">
                            </path>
                        </svg>
                        Get Started
                    </span>
                    <div
                        class="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                    </div>
                    <div
                        class="absolute -inset-1 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl opacity-0 group-hover:opacity-30 blur-lg transition-opacity duration-500">
                    </div>
                </button>
            </div>
        </div>
    </div>



    <script>
        // Initialize theme on page load
        document.addEventListener('DOMContentLoaded', function() {
            const isDark = localStorage.getItem('theme') === 'dark' ||
                (!localStorage.getItem('theme') && window.matchMedia('(prefers-color-scheme: dark)').matches);
            document.documentElement.classList.toggle('dark', isDark);
        });
    </script>
</nav>